export interface Caddy {
  id: number;
  caddy_no: string;
  caddy_name: string;
}

export interface Question {
  id: number;
  index: number;
  type: number;
  content: string;
  require: number;
}

export interface Answer {
  id: number;
  index: number;
  type: number;
  answer: number;
  count: number;
}

export interface Survey {
  id: number;
  name: string;
  answer_time: string;
  answers: Answer[];
  feedback:string;
  feedback_caddy:string;
  feedback_golf:string;
}

export interface Cart {
  id: number;
  cart_no: string;
  caddy_no: number;
  caddy_id: string;
  played_date: string;
  start_time: string;
  status: number;
  survey: Survey[];
  open: boolean;
  start_course: string;
  showLowest:boolean;
}

export interface Lowest {
  question_id: number;
  score: number;
  evaluation_id: number;
}

export interface Questionnaire {
  current_page: number;
  data: Cart[];
  lowest: Lowest[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

export interface Evaluation {
  id: number;
  score: number;
  stage: number;
  content: string;
}


export interface SurveyItem {
  id: number;
  answers: Answer[];
}

export interface CaddySurvey {
  caddy_id: string;
  survey: SurveyItem[];
}

export interface SurveyData {
  month: string;
  survey: CaddySurvey[];
}

export interface StatisticalData {
  month: string;
  survey: SurveyItem[];
}

export interface QuesnaireSettings {
    // キャディの名前種類：1(キャディ)、2(コースアテンダント)
    caddy_name_type: number;
}

