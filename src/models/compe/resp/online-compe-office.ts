export interface OnlineCompeOfficeResp {
  code: number;
  msg: string;
  data: {
    compes: MergedCompe[];
  };
}

export interface MergedCompe {
  compe_no: number;
  compe_name: string;
  duration: {
    from: Date;
    to: Date;
  };
  play_date: string | null;
  compe_type: number | null;
  joined_players_count: number;
  aggregation_types: string[];
  prize_condition_setted: boolean;
  hidden_hole_setted: boolean;
  shared_key: string | null;
  is_front_system: boolean;
  participation_fee: number | null;
}
