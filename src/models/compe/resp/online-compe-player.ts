export interface OnlineCompeJoinedPlayersResp {
  code: number;
  msg: string;
  data: JoinedPlayer[];
}

export interface JoinedPlayer {
  birthday: string;
  cart_no: number;
  course_index: number;
  gender: number;
  glid_no: string;
  hdcp: string | null;
  hdcp_index: string | null;
  playing_hdcp: string | null;
  is_paid: boolean; //default false
  office_key: string;
  play_date: string; // today yyyymmdd 20250328 local timezone
  player_name: string;
  player_no: number;
  team_class_type: number | null;
  tee_id: string | null;
}
