


interface Player {
    player_no: number;
    player_name: string;
    hdcp_index: string | null;
    hdcp: string | null;
    score: number;
    total: number;
}

interface Pair {
    cart_no: number;
    start_time: string;
    player: Player[];
}

interface Pairing {
    course_name: string;
    pair: { [key: string]: Pair };
}



interface PairingResp {
    code: number;
    message: string;
    data: {
        pairing: Pairing[];
    };
}

export type {
    Player,
    Pair,
    Pairing,
    PairingResp
};



/**
 * {
  "code": 200,
  "message": "Success",
  "data": {
    "pairing": [
      {
        "course_name": "Course 1",
        "pair": {
          "1": {
            "cart_no": 1,
            "start_time": "08:00",
            "player": [
              {
                "player_no": 1,
                "player_name": "Player 1",
                "hdcp_index": "10.5",
                "hdcp": "10.5",
                "score": 80,
                "total": 80
              },
              {
                "player_no": 2,
                "player_name": "Player 2",
                "hdcp_index": "12.5",
                "hdcp": "12.5",
                "score": 85,
                "total": 85
              }
            ]
          }
        }
      }
    ]
  }
}
 */