
interface LeaderBoardRankingResp {
    code: number;
    message: string;
    data: RankingDetails
}

interface RankingDetails {
    play_date: string;
    compe_no: string;
    updated_at: string;
    courses: ScoreCourse[];
    rankings: PlayerRanking[];
    breaking_news: BreakingNews[];
}

interface ScoreCourse {
    course_index: string;
    course_name: string;
    start_hole: string;
    holes: Hole[];
}

interface Hole {
    hole_index: string;
    used_par: string;
    used_hdcp: string;
}

interface BreakingNews {
    player_no: string;
    score: string;
}

interface PlayerRanking {
    pos: string; //POS
    pos_net: string;
    order_net: string;
    is_tied: number;
    player_no: string;
    player_name: string;
    hole: string; //HOLE
    hole_number: string;
    par_gross: number; //SCORE
    par_net: number;
    score_gross: number; //TODAY
    score_net: number;
    hdcp_index: string;
    hdcp: string;
    course_hdcp: string;
    input_hole_count: number;
    course_index: string;
    hole_index: string;
    hole_score: RankingHoleScore[];
}

interface RankingHoleScore {
    hole_number: string;
    course_index: string;
    hole_index: string;
    score: string;
    stroke: string;
}

interface RankingTypeResp {
    code: number;
    message: string;
    data: RankingType;
}

interface RankingType {
    ranking_type: string[];
}

export type {
    LeaderBoardRankingResp,
    RankingDetails,
    ScoreCourse,
    Hole,
    BreakingNews,
    PlayerRanking,
    RankingHoleScore,
    RankingTypeResp
};

