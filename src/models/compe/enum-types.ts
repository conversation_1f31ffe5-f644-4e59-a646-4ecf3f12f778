export enum HdcpTypes {
    WHS = 0,
    Private = 1,
    Front = 1,
}

export enum Calculation {
    Hdcp = 0,
    Point = 1,
}

export enum Mail {
    Disabled = 0,
    Abled = 1,
}

export enum NaviEntry {
    Disabled = 0,
    Abled = 1,
}

export enum MarkerSetting {
    Disabled = 0,
    Free = 1,
}

export enum LeadBoardChange {
    Disabled = 0,
    Abled = 1,
}

// キャディの名前種類
export enum CaddyNameTypes {
    Caddy = 1,       // 名前：キャディ
    CourseAtt = 2,   // 名前：コースアテンダント
}

export enum EnableStartTime {
    Disabled = 0,
    Abled = 1,
}

export enum EnableAutoStart {
    Disabled = 0,
    Abled = 1,
}

export enum AutoStartType {
    TimeSchedule = 0,
    StartNumber = 1,
}

export enum EnableSelfScorePrint {
    Disabled = 0,
    Abled = 1,
}

export enum EnableQuestionnaire {
    Disabled = 0,
    Abled = 1,
}

export enum EnableStartGuide {
    Disabled = 0,
    Abled = 1,
}

export enum CardReaderType {
    None = 0,
    QRCode = 1,
    ICCard = 2,
}
