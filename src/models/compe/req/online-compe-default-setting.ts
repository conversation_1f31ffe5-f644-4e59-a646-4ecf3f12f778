import { LeadboardChange } from "./online-compe-creation"



/**
{
 "details": {
  "compe_setting": {
   "entry_from_navi": 0,
   "ranking_aggregation": 0,
   "round": "0.5"
  },
  "other_setting": {
   "marker_setting": 0
  },
  "handicap": {
   "type": 0
  }
 }
}
 */
export interface OnlineCompeDefaultSetting {
    compe_setting: CompeSetting //コンペ設定
    other_setting: OtherSetting //その他設定
    handicap: Handicap //ハンディキャップSetting
}

export interface CompeSetting {
    entry_from_navi: number // ナビからエントリー 0 許可しない 1許可する
    ranking_aggregation: number //ランキング集計 ? 0 最終6ホールを隠す?
    round: string // 0.5, 1, 1.5ラウンド
}

export interface OtherSetting {
    marker_setting: number //マーカー設定 0 しない 1 マーカーあり
    leadboard_change: LeadboardChange //リーダーボード切替
}

export interface Handicap {
    type: number //ハンディキャップSetting 0 HDCP Index(WHS) 1 フロント連携HDCP
}
