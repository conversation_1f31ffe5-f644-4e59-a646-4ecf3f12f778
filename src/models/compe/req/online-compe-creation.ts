

/**
{
 "basic": {
  "compe_no": 1,
  "compe_name": "片山津　平日月例",
  "duration": {
   "from": "2025-02-10T13:45:04+09:00",
   "to": "2025-02-10T13:45:04+09:00"
  },
  "target_office_type": 0,
  "target_office": "ipctest",
  "target_office_list": [
   "ipctest",
   "ipctest1",
   "ipctest2"
  ],
  "organizer": "片山津　平日月例",
  "participation_fee": 1000,
  "promotional_image": "s3://xxxxx.xxxx.xxxx.png",
  "old_compe": {
   "old_compe_no": "150",
   "old_compe_office_key": "ipctest",
   "old_compe_start_time": "20251101"
  }
 },
  "compe_setting": {
  "entry_from_navi": 0,
  "ranking_aggregation": 0,
  "round": "0.5",
  "prize_setting": [
   {
    "type": "とび賞①",
    "setting": {
     "1": 10,
     "2": 100,
     "3": 10
    }
   },
   {
    "type": "additon",
    "name": "金賞",
    "order": {
     "1": 1,
     "2": 2,
     "3": 3,
     "5": 4
    }
   }
  ]
 },
 {
 "competition_type_setting": {
  "type": 1,
  "handy": {
   "distribution": 1,
   "ranking_order": {
     "1": "birthday",
     "2": "gender",
     "3": "count_back",
     "5":"dhcp"
    },
   "handicap": {
    "type": 0,
    "dhcp_date": "2025-01-09"
   },
   "net_computation_type": 0
  },
  "peoria": {
   "aggregation_method": {
    "type": 1
   },
   "par_limit": {
    "type": 0,
    "par_n": 1,
    "par_x": 1
   },
   "handicap_upper_limit": {
    "men": 0,
    "women": 0
   },
   "distribution": 1,
   "ranking_order": {
    "1": "birthday",
     "2": "gender",
     "3": "count_back",
     "5":"dhcp"
    },
  }
 }
},
{
 "other_setting": {
  "marker_setting": 0,
  "leadboard_change": {
   "type": 0,
   "default": "ネットをデフォルト表示"
  }
 }
}
}
*/

export interface OnlineCompe {
    basic: Basic //コンペ基本情報
    compe_setting: CompeSetting //コンペ設定
    compe_type_setting: CompeTypeSetting //競技方式
    other_setting: OtherSetting //その他設定
    private_setting: PrivateSetting | null //プライベート設定 (setting for single office only, should be present when for signle office)
}

export interface Basic {
    compe_no: number //コンペNo
    compe_name: string //コンペ名
    duration: Duration  // 開催日
    target_office_type: number // 0 自コース 1 グループコース
    target_office: string | null  // 自コースのオフィスKEY
    target_office_list: string[] | null // グループコース
    organizer: string //主催者
    participation_fee: number // 参加料金 円
    promotional_image: string // 宣伝画像登録
    old_compe: OldCompe | null // optional
}

export interface Duration {
    from: string //ISO 8601 or date type
    to: string
}

export interface OldCompe {
    old_compe_no: string
    old_compe_office_key: string
    old_compe_start_time: string
}


export interface CompeSetting {
    entry_from_navi: number //ナビからエントリー 0 許可しない 1許可する
    ranking_aggregation: number //ランキング集計 0 全ホール表示 　1 最終3ホールを隠す、 2 最終6ホールを隠す、　3 最終9ホールを隠す 
    round: string // 0.5, 1, 1.5  ラウンド
    prize_setting: PrizeSetting[] //入賞設定
    //course_setting moved to private setting
}

export interface PrizeSetting {
    type: string
    setting?: Map<string, number>
    name?: string
    order?: Map<string, number>
}

export interface CompeTypeSetting {
    type: number // 0 team 1 個人戦
    handy: Handy | null //ハンディ optional item for 個人戦
    peoria: Peoria | null //ぺリア optional item for 個人戦
}

export interface Handy {
    distribution: number // 0 配信しない 1 配信する
    ranking_order: RankingOrder //同点時の優先順位
    handicap: Handicap  //使用ハンディキャップ
    net_computation_type: number //NET計算方法 0 HDCPナンバーで割り振り 1 按分方式
}

export interface RankingOrder {
    "1": string
    "2": string
    "3": string
    "4": string
}

export interface Handicap {
    type: number // 0 HDCP Index(WHS) 1 プライベートハンディキャップ
    hdcp_date: string //時点のHDCPを使う YYYY-MM-DD
    hdcp_allowance: number    // HDCPのアローワンス 0-100 (%) default 100
}

export interface Peoria {
    distribution: number // 0 配信しない 1 配信する
    ranking_order: RankingOrder //同点時の優先順位
    aggregation_method: AggregationMethod //集計方法
    par_limit: ParLimit //打数制限
    handicap_upper_limit: HandicapUpperLimit //ハンデ上限設定
}

export interface AggregationMethod {
    type: number  //  0 ペリア(6H)、1 新ペリア(12H)、2 新新ペリア(9H)
}

export interface ParLimit {
    type: number //  0 制限なし、1 PAR×2、2 PAR×2-1、3 PAR×3、4 PAR×3-1、　5  PAR+X、6 X、7 PAR×N+X　から選択。
    par_n: number // disabled when type is 0 1 2 3 4 5 6 ,  value = -1 when disabled
    par_x: number // value = -1 when disabled
}

export interface HandicapUpperLimit {
    men: number | null  // 男性女性それぞれにハンデ上限を入力させる , 未記入の場合は上限なし (null )扱いとする
    women: number | null // 男性女性それぞれにハンデ上限を入力させる , 未記入の場合は上限なし (null )扱いとする
}

export interface OtherSetting {
    marker_setting: number //  マーカー設定 0 しない 1 マーカー自由設定
    leadboard_change: LeadboardChange //リーダーボード切替
}

export interface LeadboardChange {
    type: number //0 しない 1 suru
    default: string //  option : net, gross when 1  ; "" when  type is 0
}


export interface PrivateSetting {
    course_setting: CourseSetting //コース設定
    hidden_hole: HiddenHoleSetting[] //隠しホール設定 list
}

export interface HiddenHoleSetting {
    course_index: string //コースインデックス
    course_name: string //コース名
    hidden_hole_index: number[] //隠しホールインデックス
}

export interface CourseSetting {
    "1": string // course name
    "2": string
    "3": string
    "4": string
}
