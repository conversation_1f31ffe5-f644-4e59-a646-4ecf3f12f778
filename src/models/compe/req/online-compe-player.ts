export interface OnlineCompePlayerJoinReq {
  player_no: number;
  player_name: string;
  glid_no: string | null;
  birthday: string;
  gender: number;
  tee_id: string; // from tee info endpoint
  hdcp: string | null;
  hdcp_index: string | null;
  compe_no: number;
  office_key: string;
  play_date: string; // today yyyymmdd 20250328 local timezone
  cart_no: number;
  course_index: number;
  is_paid: boolean; //default false
}

export interface OnlineCompeJoinedPlayersReq {
  joined_players: JoinedPlayer[];
}

export interface JoinedPlayer {
  birthday: string;
  cart_no: number;
  course_index: number;
  gender: number;
  glid_no: string;
  hdcp: string | null;
  hdcp_index: string | null;
  playing_hdcp: string | null;
  is_paid: boolean;
  office_key: string;
  play_date: string;
  player_name: string;
  player_no: number;
  team_class_type: number | null;
  tee_id: string | null;
}

export interface OnlineCompeJoinedPlayerHdcpReq {
  cart_no: number;
  glid_no: string;
  hdcp: string | null;
  hdcp_index: string | null;
  gender: number;
  birthday: string;
  play_date: string;
  player_no: number;
  scheduled_start_time: string | null;
}
