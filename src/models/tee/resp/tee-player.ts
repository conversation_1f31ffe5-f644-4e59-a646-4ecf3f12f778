export interface TeePlayerResp {
  code: number;
  msg: string;
  data: TeePlayer[];
}

export interface TeePlayer {
  birthday: string;
  player_no: number;
  player_name: string;
  gender: number;
  glid_no: string;
  tee_id: string | null;
  hdcp: string | null;
  hdcp_index: string | null;
  playing_hdcp: string | null;
  office_key: string;
  play_date: string;
  cart_no: number;
  course_index: number;
  team_class_type: number | null;
  is_paid: boolean;
}
