export interface TeeSheetResp {
    code: number;
    msg: string;
    data: TeeCourseData[]; // tee course data
}

export interface TeeCourseData {
    course_index: string;
    course_name: string;
    cart_data: TeeCartData[];
}

export interface TeePlayerData {
    player_no: number;
    player_name: string;
    birthday: string;
    gender: number;
    glid_no: string;
    tee_id: string;
    hdcp_index: string | null;
    hdcp: string | null;
    locker_no: number;
    joined_compes: JoinedCompe[];
}

export interface JoinedCompe {
    compe_no: number;
}

export interface TeeCartData {
    cart_no: number;
    scheduled_start_time: string;
    start_time: string;
    delegate_compe: DelegateCompe | null;
    players: TeePlayerData[];
}

export interface DelegateCompe {
    compe_name: string;
    compe_no: number;
    is_front_system: boolean;
};