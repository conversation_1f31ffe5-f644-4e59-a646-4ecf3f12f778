import { expect, test } from 'vitest'
import { render, screen } from '@testing-library/react'
import Home from '../app/answerlist/page';
import { ThemeProvider, createTheme } from "@mui/material/styles"

const theme = createTheme({
  palette: {
    primary: {
      main: '#304A89', 
    },
    grey: {
      400: '#BDBDBD',
    },
  },
});
test('answerlist page', () => {
  render(
    <ThemeProvider theme={theme}>
      (<Home />)
    </ThemeProvider>
  );
  expect(screen.queryAllByText('キャディ设问')).toBeDefined()
})