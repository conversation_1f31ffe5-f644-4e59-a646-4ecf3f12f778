import { expect, test } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import Home from '../app/settings/page';
import SurveyForm from '../components/survey-form'

test('settings page', async () => {
  render(<Home />)
  expect(screen.queryAllByText('アンケート設問設定')).toBeDefined()
  const saveElement = await screen.findByText('設定を保存');
  fireEvent.click(saveElement);
  const previewElement = await screen.findByText('プレビュー');
  fireEvent.click(previewElement);
  const addElement = await screen.findAllByText('+設問追加');
  expect(addElement).toHaveLength(2);
  fireEvent.click(addElement[0]);
  const saveElement1 = await screen.findAllByText('保存');
  expect(saveElement1).toHaveLength(3);
  fireEvent.click(saveElement1[1]);
})

test('SurveyForm', () => {
  const props = {
    questions: [{
      question: "キャディのtest1",
      id: 1,
      sqlIndex: 1,
      isRequired: true,
      editable: false,
    },
    {
      question: "ゴルフ場のtest2",
      id: 2,
      sqlIndex: 2,
      isRequired: true,
      editable: false,
    }],
    options: [{
      stage: 1,
      scoreHint: "1",
      contentHint: "非常に不満",
      score: "",
      content: "",
    }, {
      stage: 2,
      scoreHint: "2",
      contentHint: "やや不満",
      score: "",
      content: "",
    }, {
      stage: 3,
      scoreHint: "3",
      contentHint: "普通",
      score: "",
      content: "",
    }, {
      stage: 4,
      scoreHint: "4",
      contentHint: "やや普通",
      score: "",
      content: "",
    }, {
      stage: 5,
      scoreHint: "5",
      contentHint: "非常に満足",
      score: "",
      content: "",
    }]
  }
  render(<SurveyForm  {...props} />)
  expect(screen.queryAllByAltText('クラブの確認が完了でしたら')).toBeDefined()
})