import { beforeAll, afterEach, afterAll, expect, test, vi, it, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import {
    fetchRows,
    getQuestion,
    fetchCaddies,
    postQuestion,
    updateQuestion,
    updateQuestionIndex,
    deleteQuestion,
    getEvaluations,
    updateEvaluations,
    getCaddyEvaluate,
    getStatistical,
} from '../api/caddy-api'
import { accesssTokenResp, refreshTokenResp } from '../api/auth-api';
import { http, HttpResponse } from 'msw'
import { setupServer } from "msw/node";
import { describe } from 'node:test';

// const BaseUrl = process.env.BASE_URL;
const dateNow = new Date();
const startDate = "2000-01-01";
const caddie = "none";
const endDate = `${dateNow.getFullYear()}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`;
const weekday = "1";

const BaseUrl = process.env.BASE_URL;
const dataRows = {
    code: 200,
    current_page: 1,
    data: [{
        id: 1,
        cart_no: "1",
        caddy_no: 1,
        caddy_id: "test1",
        played_date: "2024-08-08",
        start_time: "2024-08-07",
        status: 1,
        survey: [{
            id: 1,
            name: "test1",
            answer_time: "2024-08-08",
            answers: [{
                id: 1,
                index: 1,
                type: 1,
                answer: 1,
                count: 1
            }],
        }],
        open: true,
        start_course: "test"
    }],
    lowest: [
        //     {
        //     question_id: 1,
        //     score: 1,
        //     evaluation_id: 1,
        // }
    ],
    from: 1,
    last_page: 1,
    per_page: 5,
    to: 1,
    total: 4,
}

const questionData = [{
    id: 1,
    index: 1,
    type: 1,
    content: "test conten",
    require: 1
}, {
    id: 2,
    index: 2,
    type: 2,
    content: "test content2",
    require: 2
},
]
const mockRowsData = {
    code: 200,
    data: dataRows
}
const mockCaddyData = [{
    id: 1,
    caddy_no: "1",
    caddy_name: "caddy 1"
}, {
    id: 2,
    caddy_no: "2",
    caddy_name: "caddy 2"
}, {
    id: 3,
    caddy_no: "3",
    caddy_name: "caddy 3"
}];
const evaluations = [{
    id: 1,
    score: 1,
    stage: 1,
    content: "evaluation test1"
}, {
    id: 2,
    score: 2,
    stage: 2,
    content: "evaluation test2"
}, {
    id: 3,
    score: 3,
    stage: 3,
    content: "evaluation test3"
}, {
    id: 4,
    score: 4,
    stage: 4,
    content: "evaluation test4"
}]
const surveyDatas = [{
    month: "2024-08",
    survey: [{
        caddy_id: "1",
        survey: [{
            id: 1,
            answers: [{
                id: 1,
                index: 1,
                type: 1,
                answer: 12,
                count: 1,
            }],
        }],
    }, {
        caddy_id: "2",
        survey: [{
            id: 2,
            answers: [{
                id: 2,
                index: 2,
                type: 2,
                answer: 13,
                count: 1,
            }],
        }],
    }],
}]
const statisticalDatas = [{
    month: "2024-08",
    survey: [{
        id: 0,
        answers: [{
            id: 1,
            index: 1,
            type: 1,
            answer: 12,
            count: 1,
        }],
    }],
}];
describe("fetchRow API ", async () => {
    test("fetchRowt API call", async () => {
        const result = await getQuestion();
        expect(result).toEqual(questionData);
        const result1 = await fetchRows(
            1,
            10,
            startDate,
            endDate,
            caddie,
            weekday
        );
        expect(result1).toEqual(dataRows);
        const result2 = await fetchCaddies();
        expect(result2).toEqual(mockCaddyData);
        const result3 = await postQuestion("test", 1, "type");
        expect(result3).toEqual(true);
        const result4 = await updateQuestion(1, "content", 1);
        expect(result4).toEqual(true);
        const result5 = await updateQuestionIndex(1, 1);
        expect(result5).toEqual(true);
        const result6 = await deleteQuestion(1);
        expect(result6).toEqual(true);
        const result7 = await getEvaluations();
        expect(result7).toEqual(evaluations);
        const result8 = await updateEvaluations([{ id: 0, stage: 0, content: '0', score: 0 }, { id: 1, stage: 1, content: '1', score: 1 },]);
        expect(result8).toEqual(true);
        const result9 = await getCaddyEvaluate("202408", "202408", 3, "");
        expect(result9).toEqual(surveyDatas);
        const result10 = await getStatistical("202208", "202408", 1);
        expect(result10).toEqual(statisticalDatas);
        const result11 = await accesssTokenResp("auth_token");
        expect(result11).toEqual({
            access_token: "acceseetokentest",
            refresh_token: "refreshtokentest"
        });
        const result12 = await refreshTokenResp("auth_token");
        expect(result12).toEqual({
            access_token: "acceseetokentest2",
            refresh_token: "refreshtokentest2"
        });
    });
});
