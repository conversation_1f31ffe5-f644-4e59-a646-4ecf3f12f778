import { expect, test, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import CaddyQuesTable from '../components/caddy/caddy-ques-table-component';
import AnswerTableComponent from '../components/answer-table-component';
import ErrorHint from '../components/error-hint';
import NotMoveSettingsItem from '../components/not-move-settings-item';
import PaginationComponent from '../components/pagination-component';
import TableHeadRowComponent from '../components/table-head-row-component';
import TableRowComponent from '../components/table-row-component';
import SettingsItem from '../components/settings-item';
import { DateUtils } from '@/utils/date-utils';
import { Cart } from "../models/caddy-models";
import {
  fetchRows,
  getCaddyEvaluate,
} from '../api/caddy-api'
import { ThemeProvider, createTheme } from "@mui/material/styles"

test('AnswerTableComponent', () => {
  const rows: Cart[] = [{
    id: 1,
    cart_no: "1",
    caddy_no: 1,
    caddy_id: "test1",
    played_date: "2024-08-08",
    start_time: "2024-08-07",
    status: 1,
    survey: [{
      id: 1,
      name: "test1",
      answer_time: "202408-08",
      feedback: "",
      answers: [{
        id: 1,
        index: 1,
        type: 1,
        answer: 1,
        count: 1
      }],
    }],
    open: true,
    start_course: "test",
    showLowest: true
  }];
  const props = {
    rows: rows,
    questions: [{
      id: 1,
      index: 0,
      type: 1,
      content: "test content",
      require: 1
    }, {
      id: 2,
      index: 1,
      type: 1,
      content: "test content2",
      require: 2
    }],
    caddies: [{
      id: 1,
      caddy_no: "1",
      caddy_name: "caddy 1"
    }, {
      id: 2,
      caddy_no: "2",
      caddy_name: "caddy 2"
    }, {
      id: 3,
      caddy_no: "3",
      caddy_name: "caddy 3"
    }],
    evaluations: [{
      id: 1,
      score: 1,
      stage: 1,
      content: "evaluation test1"
    }, {
      id: 2,
      score: 2,
      stage: 2,
      content: "evaluation test2"
    }, {
      id: 3,
      score: 3,
      stage: 3,
      content: "evaluation test3"
    }, {
      id: 4,
      score: 4,
      stage: 4,
      content: "evaluation test4"
    }],
    lowest: [
      { question_id: 1, score: 5, evaluation_id: 101 },
      { question_id: 2, score: 3, evaluation_id: 102 },
    ],
    maxScoreId: 1
  };
  render(<AnswerTableComponent {...props} />)
  const cells = screen.getAllByRole('cell');
  expect(cells).toHaveLength(9);
  expect(screen.queryAllByText('キャディ')).toBeDefined();
  const button = document.querySelector('button[aria-label="expand row"]');
  expect(button).not.toBeNull();;
  fireEvent.click(button!);
})
test('ErrorHint', () => {
  const handleErrorButtonClick = async (type: string) => {
    const currentYear = new Date().getFullYear() + "";
    const currentMonth = new Date().getMonth() + 1;
    await getCaddyEvaluate(DateUtils.initAMonthAgo(currentYear, currentMonth.toString()), DateUtils.initMonth(currentYear, currentMonth.toString()), 1, "1");
  }
  const props = {
    screenWidth: true,
    onButtonClick: handleErrorButtonClick,
  }
  render(<ErrorHint {...props} />)
})

test('ErrorHint1', () => {
  const handleErrorButtonClick = async (type: string) => {
    const currentYear = new Date().getFullYear() + "";
    const currentMonth = new Date().getMonth() + 1;
    await getCaddyEvaluate(DateUtils.initAMonthAgo(currentYear, currentMonth.toString()), DateUtils.initMonth(currentYear, currentMonth.toString()), 1, "1");
  }
  const props = {
    isAll: true,
    isNotLogin: true,
    screenWidth: true,
    jumpSetting: true,
    onButtonClick: handleErrorButtonClick,
  }
  render(<ErrorHint {...props} />)
})

test('ErrorHint2', () => {
  const handleErrorButtonClick = async (type: string) => {
    const currentYear = new Date().getFullYear() + "";
    const currentMonth = new Date().getMonth() + 1;
    await getCaddyEvaluate(DateUtils.initAMonthAgo(currentYear, currentMonth.toString()), DateUtils.initMonth(currentYear, currentMonth.toString()), 1, "1");
  }
  const props = {
    isAll: true,
    isNotLogin: false,
    screenWidth: true,
    jumpSetting: true,
    onButtonClick: handleErrorButtonClick,
  }
  render(<ErrorHint {...props} />)
})

test('NotMoveSettingsItem', () => {
  const props = {
    key: 1,
    index: 20,
    question: {
      id: 1,
      type: 1,
      content: "test1",
      require: 1
    },
    type: 1,
    onSave: {
      id: Date.now(),
      question: "",
      isRequired: false,
      editable: true
    },
    editingId: 1
  }
  render(<NotMoveSettingsItem {...props} />)
})

const theme = createTheme({
  palette: {
    primary: {
      main: '#304A89',
    },
    grey: {
      400: '#BDBDBD',
    },
  },
});
test('PaginationComponent', () => {
  const handleSelectedPage = (event: any, value: number) => {

  };
  const handleSelectChange = async (event: any) => {
    await fetchRows(
      1,
      10,
      "2022-01-01",
      "2024-08-01",
      "none",
      "1"
    );
  };
  const props = {
    count: 2,
    page: 1,
    onChange: handleSelectedPage,
    selectedCount: 1,
    onCountChange: handleSelectChange,
    counts: [10, 25]
  }
  render(
    <ThemeProvider theme={theme}>
      (<PaginationComponent {...props} />)
    </ThemeProvider>
  );
})
test('TableHeadRowComponent', async () => {
  const props = {
    questions: [{
      id: 1,
      index: 1,
      type: 1,
      content: "test content1",
      require: 1
    }, {
      id: 2,
      index: 2,
      type: 2,
      content: "test content2",
      require: 2
    }, {
      id: 3,
      index: 3,
      type: 2,
      content: "test content3",
      require: 2
    }]
  }
  render(<TableHeadRowComponent {...props} />);
})

test('TableRowComponent', async () => {
  const handleClick = (index: string) => {

  }
  const props = {
    row: {
      id: 1,
      cart_no: "1",
      caddy_no: 1,
      caddy_id: "test1",
      played_date: "2024-08-08",
      start_time: "2024-08-07",
      status: 1,
      survey: [{
        id: 1,
        name: "test1",
        answer_time: "202408-08",
        feedback: "",
        answers: [{
          id: 1,
          index: 1,
          type: 1,
          answer: 1,
          count: 1
        }],
      }],
      open: true,
      start_course: "test",
      showLowest: true
    },
    rowIndex: 1,
    survey: {
      id: 1,
      name: "test1",
      answer_time: "202408-08",
      feedback: "",
      answers: [{
        id: 1,
        index: 1,
        type: 1,
        answer: 1,
        count: 1
      }],
    },
    surveyIndex: 0,
    questions: [{
      id: 1,
      index: 0,
      type: 1,
      content: "test content",
      require: 1
    }, {
      id: 2,
      index: 1,
      type: 1,
      content: "test content2",
      require: 2
    }],
    caddies: [{
      id: 1,
      caddy_no: "1",
      caddy_name: "caddy 1"
    }, {
      id: 2,
      caddy_no: "2",
      caddy_name: "caddy 2"
    }, {
      id: 3,
      caddy_no: "3",
      caddy_name: "caddy 3"
    }],

    expanded: false,
    handleRowClick: handleClick,
  }
  render(<TableRowComponent {...props} />);
  expect(screen.queryAllByText('未回答')).toBeDefined()
})