import { expect, test, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import Home from '../app/caddy/page';


test('caddy page', async() => {
  render(<Home  />)

  expect(screen.queryAllByText('アンケート設問設定')).toBeDefined()
  const buttonElement = screen.getByText('表示');
  fireEvent.click(buttonElement)
  const buttonElement1 = screen.getByText('リセット');
  fireEvent.click(buttonElement1)
  const caddyNameElement = await screen.findByText('caddy 1');
  expect(caddyNameElement).not.toBeNull();
  fireEvent.click(caddyNameElement);
})