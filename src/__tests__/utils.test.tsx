import { expect, test, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { DateUtils } from '@/utils/date-utils';
import { MathUtils } from '@/utils/math-utils';
import { formatDate, formatTime } from '@/utils/formatters';
import { describe } from 'node:test';


describe('Utils', () => {
  test('Utils', () => {
    const RealDate = global.Date;
    const MockDate = vi.fn(() => new RealDate('2024-08-15T00:00:00Z'));
    global.Date = MockDate as unknown as typeof Date;
    const result = DateUtils.getMonth();
    const result1 = DateUtils.getAMonthAgo();
    const result2 = DateUtils.getTwoYearsAgoMonth();
    const result3 = DateUtils.initMonth('2024', '8');
    const result4 = DateUtils.initMonth('2024', '08');
    const result5 = DateUtils.initMonth('2024', '10');
    const result6 = DateUtils.getOneYearsAgoMonth('2024-08');
    const result7 = DateUtils.initAMonthAgo('2024', '08');
    const result8 = DateUtils.initAMonthAgo('2024', '01');
    const result9 = DateUtils.getLineData();
    global.Date = RealDate;
    const result10 = DateUtils.getOneYearLineData('2024-02');
    const result11 = DateUtils.formatDateString('2023年3月');
    const result12 = DateUtils.formatDateString('2023年12月');
    const result13 = DateUtils.getMonthRange('2024', '8');
    const result14 = DateUtils.getDaysOfPreviousYearToCurrentMonth('2024-02');
    const result15 = DateUtils.getPreviousSixMonths('2024-08');
    const result16 = DateUtils.getPreviousSixMonths('2024-06');
    const result17 = DateUtils.getNextSixMonths('2024-02');
    const result18 = DateUtils.getNextSixMonths('2024-08');
    const result19 = DateUtils.isDateEqualOrAfter('2024-02', '2024-02');
    const result20 = DateUtils.isDateEqualOrAfter('2024-02', '2024-06');
    const result21 = MathUtils.calculateAverage(10, 2);
    const result22 = MathUtils.calculateAverage(10, 3);
    const result23 = formatDate('20240807');
    const result24 = formatDate('2024-08-07');
    const result25 = formatDate('20241007');
    const result26 = formatTime('2024-08-21T15:30:00');
    const result27 = MathUtils.calculateAverage(10, 0);

    expect(result).toBe('2024-08');
    expect(result1).toBe('2024-07');
    expect(result2).toBe('2022-09');
    expect(result3).toBe('2024-08');
    expect(result4).toBe('2024-08');
    expect(result5).toBe('2024-10');
    expect(result6).toBe('2023-09');
    expect(result7).toBe('2024-07');
    expect(result8).toBe('2023-12');
    expect(result9[0].name).toBe('2022年9月');
    expect(result10[0].name).toBe('2023年3月');
    expect(result11).toBe('2023-03');
    expect(result12).toBe('2023-12');
    //
    expect(result13).toBe('2024-08-01~2024-08-31');
    expect(result14).toBe('2023-03-01 ～ 2024-02-29');
    expect(result15).toBe('2024-02');
    expect(result16).toBe('2023-12');
    expect(result17).toBe('2024-08');
    expect(result18).toBe('2025-02');
    expect(result19).toBe(true);
    expect(result20).toBe(false);
    expect(result21).toBe(5);
    expect(result22).toBe(3.33);
    expect(result23).toBe('2024-08-07');
    expect(result24).toBe('2024-08-07');
    expect(result25).toBe('2024-10-07');
    expect(result26).toBe('15:30');
    expect(result27).toBe(0);
  })
})