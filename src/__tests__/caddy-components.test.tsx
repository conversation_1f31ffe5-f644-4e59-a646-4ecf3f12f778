import { expect, test, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import CaddyQuesTable from '../components/caddy/caddy-ques-table-component';
import CaddyTable from '../components/caddy/caddy-table-component';
import { DateUtils } from '@/utils/date-utils';
import {
  getCaddyEvaluate,
} from '../api/caddy-api'

test('CaddyQuesTable', () => {
  const props = {
    caddyid: '1',
    questions: [{
      id: 1,
      index: 0,
      type: 1,
      content: "test content",
      require: 1
    }, {
      id: 2,
      index: 1,
      type: 1,
      content: "test content2",
      require: 2
    }],
    caddies: [{
      id: 1,
      caddy_no: "1",
      caddy_name: "caddy 1"
    }, {
      id: 2,
      caddy_no: "2",
      caddy_name: "caddy 2"
    }, {
      id: 3,
      caddy_no: "3",
      caddy_name: "caddy 3"
    }],
    data: [{ index: 0, caddyName: "test1", tableDataQuArray: [{ id: 1, count: 1, total: 1, average: 1, compare: 1 }, { id: 2, count: 1, total: 1, average: 1, compare: 1 }] },
    { index: 1, caddyName: "test1", tableDataQuArray: [{ id: 3, count: 1, total: 1, average: 1, compare: 1 }, { id: 4, count: 1, total: 1, average: 1, compare: 1 }] }],
    caddyindex: 0,
  };
  render(<CaddyQuesTable {...props} />)
  const cells = screen.getAllByRole('cell');
  expect(cells).toHaveLength(10);
  expect(screen.queryAllByText('キャディ')).toBeDefined();
  // expect(screen.queryAllByText('ゴルフ場')).toBeDefined();
})
test('CaddyTable', () => {
  const handleCaddyNameClick = async (caddyName: string) => {
    const caddyNo = "1";
    const currentYear = new Date().getFullYear() + "";
    const currentMonth = new Date().getMonth() + 1;
    await getCaddyEvaluate(DateUtils.initAMonthAgo(currentYear, currentMonth.toString()), DateUtils.initMonth(currentYear, currentMonth.toString()
    ), 1, "1");
    // await getChatData(DateUtils.initMonth(filters.year, filters.month.toString()
    //     ), Number(filters.weekday), filters.caddie);
  };
  const props = {
    questions: [{
      id: 1,
      index: 0,
      type: 1,
      content: "test content",
      require: 1
    }, {
      id: 2,
      index: 1,
      type: 1,
      content: "test content2",
      require: 2
    }],
    caddies: [{
      id: 1,
      caddy_no: "1",
      caddy_name: "caddy 1"
    }, {
      id: 2,
      caddy_no: "2",
      caddy_name: "caddy 2"
    }, {
      id: 3,
      caddy_no: "3",
      caddy_name: "caddy 3"
    }],
    data: [{ index: 0, caddyName: "test1", tableDataQuArray: [{ id: 1, count: 1, total: 1, average: 1, compare: 1 }, { id: 2, count: 1, total: 1, average: 1, compare: 1 }] },
    { index: 1, caddyName: "test2", tableDataQuArray: [{ id: 3, count: 1, total: 1, average: 1, compare: 1 }, { id: 4, count: 1, total: 1, average: 1, compare: 1 }] }],
    onCaddyNameClick: handleCaddyNameClick,
  }
  render(<CaddyTable {...props} />)
  const typographyElement = screen.getByText('test1');
  fireEvent.click(typographyElement);
})