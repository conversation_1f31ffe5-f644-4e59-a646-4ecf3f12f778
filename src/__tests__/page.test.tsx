import { expect, test, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import Home from '../app/page';


vi.mock('next/navigation', async () => {
  const actual = await vi.importActual('next/navigation');
  return {
    ...actual,
    useRouter: vi.fn(() => ({
      push: vi.fn(),
      replace: vi.fn(),
    })),
    useSearchParams: vi.fn(() => ({
      // get: vi.fn(),
    })),
    usePathname: vi.fn(),
  };
});
test('app page', () => {
  render(<Home />)
})