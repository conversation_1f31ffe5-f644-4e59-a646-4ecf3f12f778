import { cleanup } from "@testing-library/react";
import { afterAll, afterEach, beforeAll, vi } from "vitest";
import { server } from '../mocks/server';

// Establish API mocking before all tests.
beforeAll(() => {
    server.listen()
    global.ResizeObserver = class ResizeObserver {
        observe() {
          // do nothing
        }
        unobserve() {
          // do nothing
        }
        disconnect() {
          // do nothing
        }
      };
});

// runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
    // cleanup();
    // server.resetHandlers()
});

// Clean up after the tests are finished.
afterAll(async () => {
  await new Promise(resolve => setTimeout(resolve, 0)); 
  server.close();
});