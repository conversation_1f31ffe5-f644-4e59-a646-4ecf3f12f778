import { expect, test, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import Layout from '../app/layout';
import { describe } from 'node:test';

describe('Layout Component', () => {
  test('layout', () => {
    render(
      <Layout>
        <div>Test</div>
      </Layout>
    );
    expect(screen.queryAllByAltText('マーシャルアイクラウド')).toBeDefined()
    // const firstItem = screen.getByText('アンケート');
    // fireEvent.click(firstItem.closest('a')!);
})
})