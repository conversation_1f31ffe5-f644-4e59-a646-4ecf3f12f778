import { expect, test, vi, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import {handleAuthToken, refreshAccessToken} from '@/auth/Auth';
import { describe } from 'node:test';

describe("localStorage tests", async () => {
  test('refreshAccessToken', async () => {
    const result = await refreshAccessToken();
    expect(result).toBe(false);
    localStorage.setItem('refresh_token', 'refreshTokenTest');
    const result1 = await refreshAccessToken();
    expect(result1).toBe(true);
  })

  test('handleAuthToken', async () => {
    Object.defineProperty(window, 'location', {
      get() {
        return { search: '?auth_token=wqeqe1232314' };
      },
    });
    const result = await handleAuthToken();
    expect(result).toBe(false);
  })
})