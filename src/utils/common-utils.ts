
import { CaddyNameTypes } from "@/models/compe/enum-types";

export class CommonUtils {
    static getCaddyName(caddyNameTypes: string | null): string {
        if (caddyNameTypes === (CaddyNameTypes.CourseAtt + '')) {
            return "コースアテンダント";
        } else {
            // デフォルト値
            return "キャディ";
        }
    }

    static getCaddyNameFromLocalStorage(): string {
        if (typeof localStorage !== "undefined") {
            return CommonUtils.getCaddyName(localStorage.getItem("caddy_name_type"));
        } else {
            // デフォルト値
            return "キャディ";
        }
    }
}