export class DateUtils {
    static getMonth(): string {
        const now = new Date();
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        return `${year}-${month}`;
    }

    static getAMonthAgo(): string {
        const now = new Date();
        let year = now.getFullYear();
        let month = (now.getMonth() + 1) - 1;
        if (month === 0) {
            month = 12;
            year--;
        }
        let monthS = month.toString().padStart(2, '0');
        return `${year}-${monthS}`;
    }

    static getTwoYearsAgoMonth(): string {
        const date = new Date();
        date.setMonth(date.getMonth() - 23);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${year}-${month}`;
    }

    static getOneYearsAgoMonth(dateStr: string): string {
        const [year, month] = dateStr.split('-').map(Number);
        const date = new Date(year, month - 1);
        date.setMonth(date.getMonth() - 11);
        const newYear = date.getFullYear();
        const newMonth = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${newYear}-${newMonth}`;
    }

    static initMonth(year:string,month:string): string {
        const formattedMonth = month.padStart(2, '0');
        return `${year}-${formattedMonth}`;
    }
    static initAMonthAgo(year:string,month:string): string {
        const monthInt = parseInt(month, 10);
        let prevMonthInt = monthInt - 1;

        if (prevMonthInt === 0) {
            prevMonthInt = 12;
            year = (parseInt(year, 10) - 1).toString();
        }

        const prevMonthStr = prevMonthInt.toString().padStart(2, '0');
        return `${year}-${prevMonthStr}`;
    }

    static getLineData(){
        const lineData = [];
        const currentDate = new Date();

        for (let i = 0; i < 24; i++) {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            lineData.unshift({ name: `${year}年${month}月` });
            currentDate.setMonth(currentDate.getMonth() - 1);
        }

        return lineData;
    }

    static getOneYearLineData(dateStr: string) {
        const lineData = [];
        const [year, month] = dateStr.split('-').map(Number);
        const startDate = new Date(year, month - 1);

        for (let i = 0; i < 12; i++) {
            const currentYear = startDate.getFullYear();
            const currentMonth = startDate.getMonth() + 1;
            lineData.unshift({ name: `${currentYear}年${currentMonth}月` });
            startDate.setMonth(startDate.getMonth() - 1);
        }

        return lineData;
    }

    static formatDateString(date: string): string {
        const match = date.match(/(\d{4})年(\d{1,2})月/);
        if (match) {
            const year = match[1];
            const month = match[2].padStart(2, '0');
            return `${year}-${month}`;
        }
        return "";
    }

    static getMonthRange(year: string, month: string): string {
        month = month.padStart(2, '0');
        const firstDay = `${year}-${month}-01`;
        const date = new Date(parseInt(year), parseInt(month));
        date.setMonth(date.getMonth());
        date.setDate(0);
        const lastDay = `${year}-${month}-${date.getDate().toString().padStart(2, '0')}`;
        return `${firstDay}~${lastDay}`;
    }

    static getDaysOfPreviousYearToCurrentMonth(dateStr: string) {
        if (dateStr===""){
            return "";
        }
        const [year, month] = dateStr.split('-').map(Number);
        const startDate = new Date(year - 1, month , 1);
        const endDate = new Date(year, month, 0);
        const formatDate = (date: Date) => {
            const currentYear = date.getFullYear();
            const currentMonth = (date.getMonth() + 1).toString().padStart(2, '0');
            const currentDay = date.getDate().toString().padStart(2, '0');
            return `${currentYear}-${currentMonth}-${currentDay}`;
        };
        return `${formatDate(startDate)} ～ ${formatDate(endDate)}`;
    }

    static getPreviousSixMonths(dateStr: string): string {
        let [year, month] = dateStr.split('-').map(Number);
        const inputDate = new Date(year, month - 1);
        inputDate.setMonth(inputDate.getMonth() - 6);
        const newYear = inputDate.getFullYear();
        const newMonth = inputDate.getMonth() + 1;
        return `${newYear}-${newMonth.toString().padStart(2, '0')}`;
    }

    static getNextSixMonths(dateStr: string): string {
        let [year, month] = dateStr.split('-').map(Number);
        const inputDate = new Date(year, month - 1);

        inputDate.setMonth(inputDate.getMonth() + 6);

        const newYear = inputDate.getFullYear();
        const newMonth = inputDate.getMonth() + 1;

        return `${newYear}-${newMonth.toString().padStart(2, '0')}`;
    }

    static isDateEqualOrAfter(dateStr: string, nowDateStr: string): boolean {
        const [year1, month1] = dateStr.split('-').map(Number);
        const [year2, month2] = nowDateStr.split('-').map(Number);
        const date1 = new Date(year1, month1 - 1);
        const date2 = new Date(year2, month2 - 1);
        return date1.getTime() >= date2.getTime();
    }

    static getValidDate(dateStr: string): string {
        const nextSixMonthsDate = this.getNextSixMonths(dateStr);

        const [newYear, newMonth] = nextSixMonthsDate.split('-').map(Number);

        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        if (newYear > currentYear || (newYear === currentYear && newMonth > currentMonth)) {
            return `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
        }

        return nextSixMonthsDate;
    }


    static convertDate(dateString: string): string {
        const yearEnd = dateString.indexOf("年");
        const monthEnd = dateString.indexOf("月");
        const dayEnd = dateString.indexOf("日");
        const year = dateString.substring(0, yearEnd);
        let month = dateString.substring(yearEnd + 1, monthEnd);
        let day = dateString.substring(monthEnd + 1, dayEnd);
        month = month.padStart(2, '0');
        day = day.padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}