import { useEffect } from 'react';

// 定义一个类型，表示回调函数
type ResizeCallback = (width: number) => void;

const useResizeObserver = (callback: ResizeCallback) => {
    useEffect(() => {
        const observer = new ResizeObserver(entries => {
            for (let entry of entries) {
                callback(entry.contentRect.width);
            }
        });

        const element = document.querySelector('#box-to-watch'); // 选择器
        if (element) {
            observer.observe(element);
        }

        return () => {
            if (element) {
                observer.unobserve(element);
            }
        };
    }, [callback]);
};

export default useResizeObserver;
