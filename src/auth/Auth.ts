import { accesssTokenResp, refreshTokenResp } from "../api/auth-api";


type QueryParams = {
    [key: string]: string;
};

export const handleAuthToken = async (): Promise<boolean> => {
    const params = new URLSearchParams(window.location.search);
    const paramsObject: QueryParams = {};

    params.forEach((value, key) => {
        paramsObject[key] = value;
    });

    console.info(paramsObject)
    let authToken = paramsObject['auth_token'];
    let menuType = paramsObject['menu_type'] || localStorage.getItem('menu_type') || 'default';
    if (authToken && authToken !== localStorage.getItem('last_auth_token')) {
        localStorage.setItem('username', '');
        try {
            let resp = await accesssTokenResp(authToken)
            if (resp.access_token && resp.refresh_token) {
                localStorage.setItem('last_auth_token', authToken);
                localStorage.setItem('access_token', resp.access_token);
                localStorage.setItem('refresh_token', resp.refresh_token);
                localStorage.setItem('username', resp.username);
                localStorage.setItem('office_id', resp.office_id);
                localStorage.setItem('office_key', resp.office_key);
                localStorage.setItem('office_name', resp.office_name);
                localStorage.setItem('menu_type', menuType);
                window.postMessage({ type: 'UPDATE_USERNAME', username: localStorage.getItem('username') }, '*');
                return true
            } else {
                console.error("Error requst access token failed :", resp.error);
                return false
            }
        } catch (error) {
            console.error("Error requst access token failed :", error);
            return false
        }

    } else {
        if (localStorage.getItem('access_token') && localStorage.getItem('refresh_token')) {
            localStorage.setItem('menu_type', menuType);
            return true
        } else {
            return false
        }
    }
}

export const refreshAccessToken = async () => {
    const refreshToken = localStorage.getItem('refresh_token');
    if (refreshToken) {
        let resp = await refreshTokenResp(refreshToken)
        if (resp.access_token && resp.refresh_token) {
            localStorage.setItem('access_token', resp.access_token);
            localStorage.setItem('refresh_token', resp.refresh_token);
            return true
        } else {
            console.error("Error refresh access token failed :", resp.error);
        }
    }
    return false
}
