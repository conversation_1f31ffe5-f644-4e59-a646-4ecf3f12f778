import React from "react";
import { TableRow, TableCell, Typography } from "@mui/material";
import { Question } from "../models/caddy-models";
import { CommonUtils } from '@/utils/common-utils';

interface TableHeadRowComponentProps {
  questions: Question[];
}

const TableHeadRowComponent: React.FC<TableHeadRowComponentProps> = ({
  questions,
}) => {
  return (
    <TableRow>
      <TableCell sx={{ borderBottom: `1px solid primary.main` }}></TableCell>
      <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          カート番号
        </Typography>
      </TableCell>
      <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          プレー日
        </Typography>
      </TableCell>
      <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          スタート
        </Typography>
      </TableCell>
      <TableCell
        sx={{ borderBottom: `1px solid primary.main` }}
        className="hidden-on-small"
      >
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          名前
        </Typography>
      </TableCell>
      <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          { CommonUtils.getCaddyNameFromLocalStorage() }
        </Typography>
      </TableCell>
      {questions.map((question) => (
        <TableCell
          key={question.id}
          sx={{ borderBottom: `1px solid primary.main` }}
          className="hidden-on-small"
        >
          <Typography
            sx={{ fontWeight: "bold" }}
            color="primary"
            variant="h2"
            align="center"
          >
            {question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}
            <br />
            設問{question.index}
          </Typography>
        </TableCell>
      ))}
      <TableCell
        sx={{ borderBottom: `1px solid primary.main` }}
        className="hidden-on-small"
      >
        <Typography
          sx={{ fontWeight: "bold" }}
          color="primary"
          variant="h2"
          align="center"
        >
          回答日時
        </Typography>
      </TableCell>
    </TableRow>
  );
};

export default TableHeadRowComponent;
