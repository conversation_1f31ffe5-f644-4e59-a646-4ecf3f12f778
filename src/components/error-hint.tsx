import React from 'react';
import Button from '@mui/material/Button';
import Link from 'next/link';

const ErrorHint = ({ onButtonClick, screenWidth, isAll, isNotLogin, isNull, jumpSetting }: {
    onButtonClick?: any,
    screenWidth: any,
    isAll?: any,
    isNotLogin?: any,
    isNull?: any,
    jumpSetting?: any
}) => {

    return (
        <div>
            {
                isNotLogin ?
                    (<div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '600px',
                        flexDirection: 'column',
                        textAlign: 'center'
                    }}>
                        <img src="/webapp/images/ic_notlogin.png" style={{ width: '120px', height: 'auto' }} />
                        <div style={{ margin: '20px', color: '#333333' }}>
                            このWebサイトにアクセスできません。まずログインしてください
                        </div>
                    </div>
                    ) : jumpSetting ? (
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '600px',
                            flexDirection: 'column',
                            textAlign: 'center'
                        }}>
                            <img src="/webapp/images/ic_setting.png" style={{ width: '120px', height: 'auto' }} />
                            <div style={{ margin: '20px', color: '#333333' }}>
                                まだ問題が設定されていません。設定に行ってください
                            </div>
                            <Link href="/settings" passHref>
                                <Button variant="outlined"
                                    sx={{ fontWeight: "bold", borderColor: "#333333", color: "#333333" }}
                                >
                                    設定へ移動
                                </Button>
                            </Link>
                        </div>
                    ) : isNull ? (
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '600px',
                            flexDirection: 'column',
                            textAlign: 'center'
                        }}>
                            <img src="/webapp/images/ic_nulldata.png" style={{ width: '120px', height: 'auto' }} />
                            <div style={{ margin: '20px', color: '#333333' }}>
                                アンケートデータはまだありません。
                            </div>
                        </div>
                    ) : isAll ? (
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '600px',
                            flexDirection: 'column',
                            textAlign: 'center'
                        }}>
                            <img src="/webapp/images/ic_allerror.png" style={{ width: '120px', height: 'auto' }} />
                            <div style={{ margin: '20px', color: '#333333' }}>
                                ロードに失敗しました。更新してみてください
                            </div>
                            <Button variant="outlined" sx={{ fontWeight: "bold", borderColor: "#333333", color: "#333333" }}
                                onClick={onButtonClick}>
                                ページの更新
                            </Button>
                        </div>
                    )
                        : (
                            <div style={{
                                border: '2px solid red', paddingTop: '5px', paddingBottom: '5px',
                                paddingLeft: '10px', paddingRight: '20px', marginTop: '20px', marginBottom: '10px'
                            }}>
                                <div style={{ display: 'flex', padding: '10px', alignItems: 'center' }}>
                                    <img src="/webapp/images/ic_apierror.png" style={{ width: '30px', height: 'auto' }} />
                                    <div style={{ display: 'flex', flexDirection: 'column', marginLeft: '10px' }}>
                                        <div
                                            style={{
                                                color: 'red',
                                                fontSize: '16px',
                                                fontWeight: 'bold',
                                                margin: '0'
                                            }}>エラ一が発生しました
                                        </div>
                                        <div style={{
                                            color: 'red',
                                            fontSize: '12px',
                                            fontWeight: 'bold',
                                            margin: '0'
                                        }}>チエック“アンケート回答一覧”工ラ一時
                                        </div>
                                    </div>
                                    <div style={{ flex: 1 }}></div>
                                    {screenWidth &&
                                        <Button variant="outlined"
                                            sx={{ fontWeight: "bold", borderColor: "red", color: "red" }}
                                            style={{ display: 'flex', flexDirection: 'column' }}
                                            onClick={onButtonClick}>再口ード</Button>
                                    }
                                </div>
                                {!screenWidth && (
                                    <Button variant="outlined"
                                        sx={{ fontWeight: "bold", borderColor: "red", color: "red", margin: '10px' }}
                                        onClick={onButtonClick}>再口一ド</Button>
                                )}
                            </div>
                        )
            }


        </div>
    );
};

export default ErrorHint;
