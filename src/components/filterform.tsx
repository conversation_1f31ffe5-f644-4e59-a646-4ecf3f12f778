import {TextField, MenuItem, Typography} from "@mui/material";
import {MobileDatePicker} from "@mui/x-date-pickers/MobileDatePicker";
import React, { useState, useEffect } from "react";
import dayjs, {Dayjs} from "dayjs";
import "dayjs/locale/ja";
import { CommonUtils } from '@/utils/common-utils';

interface FilterFormProps {
    caddies: { value: number; label: string }[];
    weekdays: { value: number; label: string }[];
    filters: {
        startDate: string;
        endDate: string;
        caddie: string;
        weekday: string;
    };
    setFilters: React.Dispatch<
        React.SetStateAction<{
            startDate: string;
            endDate: string;
            caddie: string;
            weekday: string;
        }>
    >;
}

dayjs.locale("ja");

const FilterForm: React.FC<FilterFormProps> = ({
                                                   caddies,
                                                   weekdays,
                                                   filters,
                                                   setFilters,
                                               }) => {
    const handleChange = (name: string, value: Dayjs | null) => {
        setFilters((prev) => ({
            ...prev,
            [name]: value ? value.format("YYYY-MM-DD") : "",
        }));
    };

    
    const [caddyNameType, setCaddyNameType] = useState<string>("キャディ");
    useEffect(() => setCaddyNameType(CommonUtils.getCaddyNameFromLocalStorage()), [])

    return (
        <div className="w-full flex flex-wrap gap-4">
            <div className="flex-1 flex flex-col sm:flex-row sm:gap-4 sm:items-center">
                <div className="flex flex-col sm:flex-row sm:gap-4 sm:items-center space-y-1 sm:space-y-0">
                    <Typography noWrap component="span" variant="h3">
                        期間
                    </Typography>
                    <MobileDatePicker
                        label="開始日"
                        value={filters.startDate ? dayjs(filters.startDate) : null}
                        onChange={(newValue) => handleChange("startDate", newValue)}
                        format="YYYY年M月D日"
                        maxDate={dayjs(filters.endDate)}
                        slotProps={{
                            textField: {
                                size: "small",
                                InputProps: {
                                    style: {width: "180px", height: "36px"},
                                },
                            },
                        }}
                    />
                </div>
            </div>

            <div className="flex-1 flex flex-col sm:flex-row sm:gap-4 sm:items-center">
                <div className="flex flex-col sm:flex-row sm:gap-4 sm:items-center space-y-1 sm:space-y-0">
                    <Typography noWrap component="span" variant="h3">
                        至
                    </Typography>
                    <MobileDatePicker
                        label="終了日"
                        value={filters.endDate ? dayjs(filters.endDate) : null}
                        onChange={(newValue) => handleChange("endDate", newValue)}
                        format="YYYY年M月D日"
                        minDate={dayjs(filters.startDate)}
                        slotProps={{
                            textField: {
                                size: "small",
                                InputProps: {
                                    style: {width: "180px", height: "36px"},
                                },
                            },
                        }}
                    />
                </div>
            </div>

            <div className="flex-1 flex flex-col sm:flex-row sm:gap-4 sm:items-center">
                <div className="flex flex-col sm:flex-row sm:gap-4 sm:items-center space-y-2 sm:space-y-0">
                    <Typography noWrap component="span" variant="h3">
                        {caddyNameType}選択
                    </Typography>
                    <TextField
                        select
                        variant="outlined"
                        size="small"
                        name="caddie"
                        value={filters.caddie}
                        onChange={(event) =>
                            setFilters((prev) => ({...prev, caddie: event.target.value.toString()}))
                        }
                        InputProps={{
                            style: {width: "180px", height: "36px"},
                        }}
                    >
                        {caddies.map((caddie) => (
                            <MenuItem key={caddie.value} value={caddie.value}>
                                {caddie.label}
                            </MenuItem>
                        ))}
                    </TextField>
                </div>
            </div>


            <div className="flex-1 flex flex-col sm:flex-row sm:gap-4 sm:items-center">
                <div className="flex flex-col sm:flex-row sm:gap-4 sm:items-center space-y-2 sm:space-y-0">
                    <Typography noWrap component="span" variant="h3">
                        表示曜日
                    </Typography>
                    <TextField
                        select
                        variant="outlined"
                        size="small"
                        name="weekday"
                        value={filters.weekday}
                        onChange={(event) =>
                            setFilters((prev) => ({...prev, weekday: event.target.value.toString()}))
                        }
                        InputProps={{
                            style: {width: "180px", height: "36px"},
                        }}
                    >
                        {weekdays.map((weekday) => (
                            <MenuItem key={weekday.value} value={weekday.value}>
                                {weekday.label}
                            </MenuItem>
                        ))}
                    </TextField>
                </div>
            </div>
        </div>
    );
};

export default FilterForm;
