import React, { useEffect, useState } from "react";
import { MenuItem, TextField } from "@mui/material";

interface YearSelectorProps {
  year: string;
  onYearChange: (year: string) => void;
}

const YearSelector: React.FC<YearSelectorProps> = ({ year, onYearChange }) => {
  useEffect(() => {
    setYear(year);
  }, [year]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedYear = event.target.value;
    onYearChange(selectedYear);
  };

  const currentYear = new Date().getFullYear();
  const [nowYear, setYear] = useState<number | string>(year);
  const startYear = 2024;
  const years = Array.from(new Array(currentYear - startYear + 1), (val, index) => startYear + index + "年度");

  return (
    <TextField
      select
      variant="outlined"
      size="small"
      name="year"
      value={nowYear}
      onChange={handleChange}
      InputProps={{
        style: { width: "180px", height: "36px" },
      }}
    >
      {years.map((year) => (
        <MenuItem key={year} value={year}>
          {year}
        </MenuItem>
      ))}
    </TextField>
  );
};

export default YearSelector;
