import {
  Table,
  Typography,
  TableBody,
  TableCell as MuiTableCell,
  TableHead,
  Box,
  TableCellProps,
  TableRow,
  Divider,
} from "@mui/material";
import { styled } from "@mui/system";
import { Caddy, Question, Survey, Cart } from "../../models/caddy-models";
import { useEffect, useRef, useState } from "react";
import { TableCell } from "@mui/material";
import { CommonUtils } from '@/utils/common-utils';

interface CaddyQuesTableProps {
  caddyid: string;
  questions: Question[];
  caddies: Caddy[];
  caddyindex: number;
  data: any;
}

const CaddyQuesTable: React.FC<CaddyQuesTableProps> = ({
  caddyid,
  questions,
  caddies,
  data, caddyindex,
}) => {
  const ItemTableCell = styled(MuiTableCell)(() => ({
    width: calculateWidth(),
    minWidth: "80px",
  }));

  const calculateWidth = () => {
    return `${(3 / 20) * 100}%`;
  };

  return (
    <Table>
      <TableHead>
        <TableRow>
          <TableCell
          // sx={{
          //   borderBottom: "0",
          // }}
          >
            <Typography
              variant="h2"
              color="primary"
              align="center"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid primary.main",
              }}
              style={{
                flexGrow: 1,
                maxWidth: "600px",
                minWidth: "200px",
                paddingTop: "5px",
                paddingBottom: "5px",
                marginLeft: "50px",
                textAlign: "center",
              }}
            >
              設問
            </Typography>
          </TableCell>

          <ItemTableCell
          // sx={{
          //   borderBottom: "0",
          // }}
          >
            <Typography
              variant="h2"
              color="primary"
              align="center"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid primary.main",
              }}
              style={{ flexGrow: 1, textAlign: "center" }}
            >
              回答数
            </Typography>
          </ItemTableCell>

          <ItemTableCell
          // sx={{
          //   borderBottom: "0",
          // }}
          >
            <Typography
              variant="h2"
              color="primary"
              align="center"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid primary.main",
              }}
              style={{ flexGrow: 1, textAlign: "center" }}
            >
              総点数
            </Typography>
          </ItemTableCell>

          <ItemTableCell
          // sx={{
          //   borderBottom: "0",
          // }}
          >
            <Typography
              variant="h2"
              color="primary"
              align="center"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid primary.main",
              }}
              style={{ flexGrow: 1, textAlign: "center" }}
            >
              平均点
            </Typography>
          </ItemTableCell>

          <ItemTableCell
          // sx={{
          //   borderBottom: "0",
          // }}
          >
            <Typography
              variant="h2"
              color="primary"
              align="center"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid primary.main",
              }}
              style={{ flexGrow: 1, textAlign: "center" }}
            >
              前月比
            </Typography>
          </ItemTableCell>
        </TableRow>

      </TableHead>

      <TableBody>
        {questions.map((question, index) => (
          <TableRow key={question.id}>
            <TableCell>
              <Typography
                variant="h2"
                align="center"
                style={{
                  flexGrow: 1,
                  maxWidth: "600px",
                  minWidth: "200px",
                  paddingTop: "5px",
                  paddingBottom: "5px",
                  marginLeft: "50px",
                  textAlign: "left",
                }}
              >
                {`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}設問${question.index
                  }`}:{question.content}
              </Typography>
            </TableCell>

            <ItemTableCell key={index}>
              <Typography
                variant="h2"
                align="center"
                style={{ flexGrow: 1, textAlign: "center" }}
              >
                {data[caddyindex].tableDataQuArray[index].count}
              </Typography>
            </ItemTableCell>

            <ItemTableCell key={index}>
              <Typography
                variant="h2"
                align="center"
                style={{ flexGrow: 1, textAlign: "center" }}
              >
                {data[caddyindex].tableDataQuArray[index].total}
              </Typography>
            </ItemTableCell>

            <ItemTableCell key={index}>
              <Typography
                variant="h2"
                align="center"
                style={{ flexGrow: 1, textAlign: "center" }}
              >
                {data[caddyindex].tableDataQuArray[index].average === 0 ? '-' : data[caddyindex].tableDataQuArray[index].average}
              </Typography>
            </ItemTableCell>

            <ItemTableCell key={index}>
              <Typography
                variant="h2"
                align="center"
                style={{ flexGrow: 1, textAlign: "center", display: "flex", justifyContent: "center", alignItems: "center" }}
              >
                {data[caddyindex].tableDataQuArray[index].compare > 0 ? (
                  <>
                    <img
                      src="/webapp/images/ic_upward_facing.png"
                      alt="upward"
                      style={{ width: "12px", height: "16px", marginRight: "4px" }}
                    />
                    <span>+{data[caddyindex].tableDataQuArray[index].compare}</span>
                  </>
                ) : data[caddyindex].tableDataQuArray[index].compare === 0 ? (
                  <>
                    <span style={{ color: '#EFBB70', fontSize: '25px', fontWeight: '800' }}>--</span>
                    <span style={{ marginLeft: "8px" }}>±0.00</span>
                  </>
                ) : (
                  <>
                    <img
                      src="/webapp/images/ic_downward_facing.png"
                      alt="downward"
                      style={{ width: "12px", height: "16px", marginRight: "4px" }}
                    />
                    <span>{data[caddyindex].tableDataQuArray[index].compare}</span>
                  </>
                )}
              </Typography>
            </ItemTableCell>

          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default CaddyQuesTable;
