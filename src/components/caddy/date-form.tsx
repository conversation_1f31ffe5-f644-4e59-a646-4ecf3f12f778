import { TextField, MenuItem, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import YearSelector from "./year-selector";
import MonthSelector from "./month-selector";
import "dayjs/locale/ja";
import dayjs from "dayjs";
import { CommonUtils } from '@/utils/common-utils';

interface FilterFormProps {
    caddies: { value: string; label: string }[];
    weekdays: { value: number; label: string }[];
    filters: {
        year: string;
        month: string;
        caddie: string;
        weekday: string;
    };
    setFilters: React.Dispatch<
        React.SetStateAction<{
            year: string;
            month: string;
            caddie: string;
            weekday: string;
        }>
    >;
}
dayjs.locale("ja");

const DataForm: React.FC<FilterFormProps> = ({
    caddies,
    weekdays,
    filters,
    setFilters,
}) => {
    const handleYearChange = (year: string) => {
        setFilters((prev) => ({ ...prev, year }));
    };

    const handleMonthChange = (month: string) => {
        setFilters((prev) => ({ ...prev, month }));
    };

    const [caddyNameType, setCaddyNameType] = useState<string>("キャディ");
    useEffect(() => setCaddyNameType(CommonUtils.getCaddyNameFromLocalStorage()), [])

    return (
        <div className="w-full flex flex-wrap gap-2 mt-4">
            <div className="flex-1 flex flex-col sm:flex-row sm:space-x-1 sm:items-center" style={{ marginRight: '10px' }}>
                <Typography noWrap component="span" variant="h3" className="flex-shrink-0">
                    表示年度
                </Typography>
                <YearSelector year={filters.year} onYearChange={handleYearChange} />
            </div>

            <div className="flex-1 flex flex-col sm:flex-row sm:space-x-1 sm:items-center" style={{ marginRight: '10px' }}>
                <Typography noWrap component="span" variant="h3" className="flex-shrink-0" >
                    表示月
                </Typography>
                <MonthSelector month={filters.month} onMonthChange={handleMonthChange} />
            </div>

            <div className="flex-1 flex flex-col sm:flex-row sm:space-x-1 sm:items-center" style={{ marginRight: '10px' }}>
                <Typography noWrap component="span" variant="h3" className="flex-shrink-0">
                    {caddyNameType}選択
                </Typography>
                <TextField
                    select
                    variant="outlined"
                    size="small"
                    name="caddie"
                    value={filters.caddie}
                    onChange={(event) =>
                        setFilters((prev) => ({ ...prev, caddie: event.target.value }))
                    }
                    InputProps={{
                        style: { width: "180px", height: "36px" },
                    }}
                >
                    {caddies.map((caddie) => (
                        <MenuItem key={caddie.value} value={caddie.value}>
                            {caddie.label}
                        </MenuItem>
                    ))}
                </TextField>
            </div>

            <div className="flex-1 flex flex-col sm:flex-row sm:space-x-1 sm:items-center">
                <Typography noWrap component="span" variant="h3" className="flex-shrink-0">
                    表示曜日
                </Typography>
                <TextField
                    select
                    variant="outlined"
                    size="small"
                    name="weekday"
                    value={filters.weekday}
                    onChange={(event) =>
                        setFilters((prev) => ({ ...prev, weekday: event.target.value }))
                    }
                    InputProps={{
                        style: { width: "180px", height: "36px" },
                    }}
                >
                    {weekdays.map((weekday) => (
                        <MenuItem key={weekday.value} value={weekday.value}>
                            {weekday.label}
                        </MenuItem>
                    ))}
                </TextField>
            </div>
        </div>
    );
};

export default DataForm;
