import {
    Table,
    Typography,
    TableBody,
    TableCell as MuiTableCell,
    TableHead,
    Box,
    TableCellProps,
    TableRow,
} from "@mui/material";
import { styled } from "@mui/system";
import { Question } from "../../models/caddy-models";
import { useMediaQuery, useTheme } from "@mui/material";
import { CommonUtils } from '@/utils/common-utils';

// HeaderTableCell
interface HeaderTableCellProps extends TableCellProps {
    text: string;
    sx?: React.CSSProperties;
}

const HeaderTableCell: React.FC<HeaderTableCellProps> = ({
    text,
    sx,
    ...props
}) => {
    const defaultStyles = {
        fontWeight: "bold",
        borderBottom: "1px solid primary.main",
    };

    return (
        <MuiTableCell {...props} sx={sx}>
            <Typography
                sx={{ ...defaultStyles }}
                color="primary"
                variant="h3"
                align="center"
            >
                {text}
            </Typography>
        </MuiTableCell>
    );
};

// HeaderCell
interface HeaderTableProps extends TableCellProps {
    questions: Question[];
}

const HeaderCell: React.FC<HeaderTableProps> = ({ questions }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    return (
        <TableHead>
            {questions.length > 0 && (
                <TableRow>
                    {/* 固定的“キャディ番号”列 */}
                    <HeaderTableCell
                        text={ CommonUtils.getCaddyNameFromLocalStorage() + "番号"}
                        sx={{
                            background: "white",
                            position: "sticky",
                            left: 0,
                            zIndex: 1,
                        }}
                    />
                    <HeaderTableCell text="名前" sx={{ background: "white" }} />
                    {questions.map((question) => (
                        <MuiTableCell key={question.id}>
                            <Box sx={{ width: "100%" }}>
                                <Typography
                                    sx={{
                                        fontWeight: "bold",
                                        borderBottom: `1px solid primary.main`,
                                    }}
                                    color="primary"
                                    variant="h3"
                                    align="center"
                                >
                                    {`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}設問${question.index
                                        }`}
                                </Typography>
                            </Box>

                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                }}
                            >
                                <Typography
                                    sx={{
                                        fontWeight: "bold",
                                        borderBottom: `1px solid primary.main`,
                                        marginRight: 2,
                                    }}
                                    color="primary"
                                    variant="h3"
                                    align="center"
                                >
                                    回答数
                                </Typography>
                                <Typography
                                    sx={{
                                        fontWeight: "bold",
                                        borderBottom: `1px solid primary.main`,
                                    }}
                                    color="primary"
                                    variant="h3"
                                    align="center"
                                >
                                    平均点
                                </Typography>
                            </Box>
                        </MuiTableCell>
                    ))}
                </TableRow>
            )}
        </TableHead>
    );
};

interface CaddyTableForMobileProps {
    questions: Question[];
    data: any;
    onCaddyNameClick: (caddyName: string) => void;
}

const CaddyTableForMobile: React.FC<CaddyTableForMobileProps> = ({
    questions,
    data,
    onCaddyNameClick,
}) => {
    const minTableCellWidth = 80;
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    const ItemTableCell = styled(MuiTableCell)(() => ({
        width: `${(1 / (questions.length + 2)) * 100}%`,
        minWidth: minTableCellWidth,
    }));

    return (
        <Box sx={{ overflowX: "auto" }}>
            <Table aria-label="purchases">
                <HeaderCell questions={questions} />
                <TableBody>
                    {data.map((data: any, index: any) => (
                        <TableRow key={data.index}>
                            {/* 固定的“キャディ番号”列 */}
                            <ItemTableCell
                                sx={{
                                    background: "white",
                                    position: "sticky",
                                    left: 0,
                                    zIndex: 1,
                                }}
                            >
                                <Typography
                                    variant="h4"
                                    align="center"
                                    sx={{
                                        textAlign: "center",
                                        fontSize: isMobile ? "0.8rem" : "1rem",
                                    }}
                                >
                                    {data.index == 0 ? "-" : data.index}
                                </Typography>
                            </ItemTableCell>
                            <ItemTableCell sx={{ background: "white" }}>
                                <Typography
                                    variant="h2"
                                    align="center"
                                    style={{
                                        flexGrow: 1,
                                        textAlign: "center",
                                        fontWeight: "bold",
                                        cursor: data.caddyName ? "pointer" : "default",
                                        color: data.caddyName ? "#304A89" : "#000000",
                                    }}
                                    onClick={() => data.caddyName && onCaddyNameClick(data.caddyName)}
                                >
                                    {data.caddyName || "対象外"}
                                </Typography>
                            </ItemTableCell>
                            {data.tableDataQuArray.map((question: any, qIndex: any) => (
                                <ItemTableCell key={qIndex}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            width: "100%",
                                            flexDirection: "row",
                                        }}
                                    >
                                        <Typography
                                            sx={{
                                                flex: 1,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                marginRight: 2,
                                                fontSize: isMobile ? "0.8rem" : "1rem",
                                            }}
                                            variant="h4"
                                        >
                                            {(!data.caddyName || data.caddyName === "") && question.type === 1 ? "対象外" : question.count.toString()}
                                        </Typography>
                                        <Typography
                                            sx={{
                                                flex: 1,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                fontSize: isMobile ? "0.8rem" : "1rem",
                                            }}
                                            variant="h4"
                                        >
                                            {(!data.caddyName || data.caddyName === "") && question.type === 1 ? "-" : (question.average.toString() === "0" ? '-' : question.average.toString())}
                                        </Typography>
                                    </Box>
                                </ItemTableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );
};

export default CaddyTableForMobile;
