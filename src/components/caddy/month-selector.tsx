import React, { useEffect, useState } from "react";
import { MenuItem, TextField } from "@mui/material";

interface MonthSelectorProps {
  month: string;
  onMonthChange: (month: string) => void;
}

const MonthSelector: React.FC<MonthSelectorProps> = ({
  month,
  onMonthChange,
}) => {

  useEffect(() => {
    setMonth(month);
  }, [month]);

  const [nowMonth, setMonth] = useState<string>(month);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedMonth = event.target.value;

    onMonthChange(selectedMonth.toString());
  };

  const months = Array.from({ length: 12 }, (_, index) => (index + 1) + "月度");

  return (
    <TextField
      select
      variant="outlined"
      size="small"
      name="month"
      value={nowMonth}
      onChange={handleChange}
      InputProps={{
        style: { width: "180px", height: "36px" },
      }}
    >
      {months.map((month) => (
        <MenuItem key={month} value={month}>
          {month}
        </MenuItem>
      ))}
    </TextField>
  );
};

export default MonthSelector;
