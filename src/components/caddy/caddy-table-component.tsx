import {
  Table,
  Typography,
  TableBody,
  TableCell as MuiTableCell,
  TableHead,
  Box,
  TableCellProps,
  TableRow,
} from "@mui/material";
import { styled } from "@mui/system";
import { Caddy, Question, Survey, Cart } from "../../models/caddy-models";
import { useEffect, useRef, useState } from "react";
import { TableCell } from "@mui/material";
import { CommonUtils } from '@/utils/common-utils';

//fatherItem tableCell
interface HeaderTableCellProps extends TableCellProps {
  text: string;
  sx?: React.CSSProperties;
}

const HeaderTableCell: React.FC<HeaderTableCellProps> = ({
  text,
  sx,
  ...props
}) => {
  const defaultStyles = {
    fontWeight: "bold",
    borderBottom: "1px solid primary.main",
  };

  return (
    <TableCell {...props} sx={sx}>
      <Typography
        sx={{ ...defaultStyles }}
        color="primary"
        variant="h2"
        align="center"
      >
        {text}
      </Typography>
    </TableCell>
  );
};

//fatherItem table TableHeader
interface HeaderTable extends TableCellProps {
  questions: Question[];
  tableCellWidth: number;
}

const HeaderCell: React.FC<HeaderTable> = ({ questions, tableCellWidth }) => {
  return (
    <TableHead>
      {questions.length > 0 && (
        <TableRow>
          <HeaderTableCell
            text={ CommonUtils.getCaddyNameFromLocalStorage() + "番号"}
            sx={{ background: "white", position: "sticky", left: 0 }}
          />
          <HeaderTableCell
            text="名前"
            sx={{
              background: "white",
              position: "sticky",
              left: tableCellWidth,
            }}
          />
          {questions.map((question) => (
            <TableCell key={question.id}>
              <Box sx={{ width: "100%" }}>
                <Typography
                  sx={{
                    fontWeight: "bold",
                    borderBottom: `1px solid primary.main`,
                  }}
                  color="primary"
                  variant="h2"
                  align="center"
                >
                  {`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}設問${question.index
                    }`}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  width: "100%",
                  flexDirection: "row",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "bold",
                    borderBottom: `1px solid primary.main`,
                    flex: 1,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  color="primary"
                  variant="h2"
                  align="center"
                >
                  回答数
                </Typography>
                <Typography
                  sx={{
                    fontWeight: "bold",
                    borderBottom: `1px solid primary.main`,
                    flex: 1,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                  }}
                  color="primary"
                  variant="h2"
                  align="center"
                >
                  平均点
                </Typography>
              </Box>
            </TableCell>
          ))}
        </TableRow>
      )}
    </TableHead>
  );
};



interface CaddyTableProps {
  questions: Question[];
  caddies: Caddy[];
  data: any;
  onCaddyNameClick: (caddyName: string) => void;
}

const CaddyTable: React.FC<CaddyTableProps> = ({ questions, caddies, data, onCaddyNameClick, }) => {
  const minTableCellWidth = 130;
  const [tableCellWidth, setTableCellWidth] = useState(0);
  const tableCellRef = useRef<HTMLTableSectionElement | null>(null);

  const ItemTableCell = styled(MuiTableCell)(() => ({
    width: calculateWidth(questions.length),
    minWidth: minTableCellWidth,
  }));

  const calculateWidth = (questionsLength: number) => {
    return `${(1 / (questionsLength + 2)) * 100}%`;
  };

  useEffect(() => {
    const handleResize = () => {
      if (tableCellRef.current) {
        const currentWidth =
          tableCellRef.current?.getBoundingClientRect().width /
          (questions.length + 2);
        setTableCellWidth(Math.max(currentWidth, minTableCellWidth));
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [questions.length]);
  return (
    <div>
      <Table aria-label="purchases">
        {/* table header */}
        {<HeaderCell questions={questions} tableCellWidth={tableCellWidth} />}
        <TableBody ref={tableCellRef}>
          {data.map((data: any, index: any) => (
            <TableRow key={data.index}>
              <ItemTableCell
                sx={{ position: "sticky", left: 0, background: "white", zIndex: 1, }}
              >
                <Typography
                  variant="h2"
                  align="center"
                  style={{ flexGrow: 1, textAlign: "center" }}
                >
                  {data.index == 0 ? "-" : data.index}
                </Typography>
              </ItemTableCell>
              <ItemTableCell
                sx={{
                  position: "sticky",
                  left: tableCellWidth,
                  background: "white",
                  zIndex: 1,

                }}
              >
                <Typography
                  variant="h2"
                  align="center"
                  style={{
                    flexGrow: 1,
                    textAlign: "center",
                    fontWeight: "bold",
                    cursor: data.caddyName ? "pointer" : "default",
                    color: data.caddyName ? "#304A89" : "#000000",
                  }}
                  onClick={() => data.caddyName && onCaddyNameClick(data.caddyName)}
                >
                  {data.caddyName || "対象外"}
                </Typography>

              </ItemTableCell>
              {data.tableDataQuArray.map((question: any, qIndex: any) => (
                <ItemTableCell key={qIndex}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                      flexDirection: "row",
                    }}
                  >
                    <Typography
                      sx={{
                        flex: 1,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: 2
                      }}
                      variant="h2"
                    >
                      {(!data.caddyName || data.caddyName === "") && question.type === 1 ? "対象外" : question.count.toString()}
                    </Typography>
                    <Typography
                      sx={{
                        flex: 1,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center"
                      }}
                      variant="h2"
                    >
                      {(!data.caddyName || data.caddyName === "") && question.type === 1 ? "-" : (question.average.toString() === "0" ? '-' : question.average.toString())}
                    </Typography>
                  </Box>
                </ItemTableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default CaddyTable;
