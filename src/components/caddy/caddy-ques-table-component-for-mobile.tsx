import {
    Table,
    Typography,
    TableBody,
    TableCell as MuiTableCell,
    TableHead,
    Box,
    TableRow,
    useMediaQuery,
    useTheme,
} from "@mui/material";
import { styled } from "@mui/system";
import { <PERSON>ad<PERSON>, Question } from "../../models/caddy-models";
import { CommonUtils } from '@/utils/common-utils';

interface CaddyQuesTableForMobileProps {
    caddyid: string;
    questions: Question[];
    caddies: Caddy[];
    caddyindex: number;
    data: any;
}

const CaddyQuesTableForMobile: React.FC<CaddyQuesTableForMobileProps> = ({
    caddyid,
    questions,
    caddies,
    data,
    caddyindex,
}) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    const ItemTableCell = styled(MuiTableCell)(() => ({
        width: isMobile ? "auto" : calculateWidth(),
        minWidth: "40px",
    }));

    const calculateWidth = () => {
        return `${(3 / 20) * 100}%`;
    };

    return (
        <Box sx={{ overflowX: "auto" }}>
            <Table>
                <TableHead>
                    <TableRow>
                        <MuiTableCell
                            sx={{
                                padding: isMobile ? "4px" : "8px",
                            }}
                        >
                            <Typography
                                variant="h4"
                                color="primary"
                                align="center"
                                sx={{
                                    fontWeight: "bold",
                                    borderBottom: "1px solid primary.main",
                                    fontSize: isMobile ? "0.875rem" : "1rem",
                                }}
                                style={{
                                    textAlign: "center",
                                }}
                            >
                                設問
                            </Typography>
                        </MuiTableCell>

                        <ItemTableCell
                            sx={{
                                padding: isMobile ? "4px" : "8px",
                            }}
                        >
                            <Typography
                                variant="h4"
                                color="primary"
                                align="center"
                                sx={{
                                    fontWeight: "bold",
                                    borderBottom: "1px solid primary.main",
                                    fontSize: isMobile ? "0.875rem" : "1rem",
                                }}
                                style={{ textAlign: "center" }}
                            >
                                回答数
                            </Typography>
                        </ItemTableCell>

                        <ItemTableCell
                            sx={{
                                padding: isMobile ? "4px" : "8px",
                            }}
                        >
                            <Typography
                                variant="h4"
                                color="primary"
                                align="center"
                                sx={{
                                    fontWeight: "bold",
                                    borderBottom: "1px solid primary.main",
                                    fontSize: isMobile ? "0.875rem" : "1rem",
                                }}
                                style={{ textAlign: "center" }}
                            >
                                総点数
                            </Typography>
                        </ItemTableCell>

                        <ItemTableCell
                            sx={{
                                padding: isMobile ? "4px" : "8px",
                            }}
                        >
                            <Typography
                                variant="h4"
                                color="primary"
                                align="center"
                                sx={{
                                    fontWeight: "bold",
                                    borderBottom: "1px solid primary.main",
                                    fontSize: isMobile ? "0.875rem" : "1rem",
                                }}
                                style={{ textAlign: "center" }}
                            >
                                平均点
                            </Typography>
                        </ItemTableCell>

                        <ItemTableCell
                            sx={{
                                padding: isMobile ? "4px" : "8px",
                            }}
                        >
                            <Typography
                                variant="h4"
                                color="primary"
                                align="center"
                                sx={{
                                    fontWeight: "bold",
                                    borderBottom: "1px solid primary.main",
                                    fontSize: isMobile ? "0.875rem" : "1rem",
                                }}
                                style={{ textAlign: "center" }}
                            >
                                前月比
                            </Typography>
                        </ItemTableCell>
                    </TableRow>
                </TableHead>

                <TableBody>
                    {questions.map((question, index) => (
                        <TableRow key={question.id}>
                            <MuiTableCell
                                sx={{
                                    padding: isMobile ? "4px" : "8px",
                                }}
                            >
                                <Typography
                                    variant="h4"
                                    align="left"
                                    style={{
                                        maxWidth: "600px",
                                        minWidth: "60px",
                                        textAlign: "left",
                                        fontSize: isMobile ? "0.875rem" : "1rem",
                                    }}
                                >
                                    {`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}設問${question.index
                                        }`}:{question.content}
                                </Typography>
                            </MuiTableCell>

                            <ItemTableCell key={index}>
                                <Typography
                                    variant="h4"
                                    align="center"
                                    style={{ textAlign: "center", fontSize: isMobile ? "0.875rem" : "1rem" }}
                                >
                                    {data[caddyindex].tableDataQuArray[index].count}
                                </Typography>
                            </ItemTableCell>

                            <ItemTableCell key={index}>
                                <Typography
                                    variant="h4"
                                    align="center"
                                    style={{ textAlign: "center", fontSize: isMobile ? "0.875rem" : "1rem" }}
                                >
                                    {data[caddyindex].tableDataQuArray[index].total}
                                </Typography>
                            </ItemTableCell>

                            <ItemTableCell key={index}>
                                <Typography
                                    variant="h4"
                                    align="center"
                                    style={{ textAlign: "center", fontSize: isMobile ? "0.875rem" : "1rem" }}
                                >
                                    {data[caddyindex].tableDataQuArray[index].average === 0 ? '-' : data[caddyindex].tableDataQuArray[index].average}
                                </Typography>
                            </ItemTableCell>

                            <ItemTableCell key={index}>
                                <Typography
                                    variant="h4"
                                    align="center"
                                    style={{
                                        textAlign: "center",
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        fontSize: isMobile ? "0.875rem" : "1rem",
                                    }}
                                >
                                    {data[caddyindex].tableDataQuArray[index].compare > 0 ? (
                                        <>
                                            <img
                                                src="/webapp/images/ic_upward_facing.png"
                                                alt="upward"
                                                style={{ width: "12px", height: "16px", marginRight: "4px" }}
                                            />
                                            <span>+{data[caddyindex].tableDataQuArray[index].compare}</span>
                                        </>
                                    ) : data[caddyindex].tableDataQuArray[index].compare === 0 ? (
                                        <>
                                            <span style={{ color: '#EFBB70', fontSize: '25px', fontWeight: '800' }}>-</span>
                                            <span style={{ marginLeft: "8px" }}>±0.00</span>
                                        </>
                                    ) : (
                                        <>
                                            <img
                                                src="/webapp/images/ic_downward_facing.png"
                                                alt="downward"
                                                style={{ width: "12px", height: "16px", marginRight: "4px" }}
                                            />
                                            <span>{data[caddyindex].tableDataQuArray[index].compare}</span>
                                        </>
                                    )}
                                </Typography>
                            </ItemTableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );
};

export default CaddyQuesTableForMobile;
