import {
    Typography,
    TableHead,
    TableRow,
} from "@mui/material";

import { Question } from "../models/caddy-models";
import { TableCell } from "@mui/material";
import { TableCellProps } from "@mui/material";
import { CommonUtils } from '@/utils/common-utils';

//fatherItem tableCell
interface HeaderTableCellProps extends TableCellProps {
    text: string;
    sx?: React.CSSProperties;
}

const HeaderTableCell: React.FC<HeaderTableCellProps> = ({
    text,
    sx,
    ...props
}) => {
    const defaultStyles = {
        fontWeight: "bold",
        borderBottom: "1px solid primary.main",
    };

    return (
        <TableCell {...props} sx={sx}>
            <Typography
                sx={{ ...defaultStyles }}
                color="primary"
                variant="h2"
                align="center"
            >
                {text}
            </Typography>
        </TableCell>
    );
};

//fatherItem table TableHeader
interface HeaderTable extends TableCellProps {
    questions: Question[];
    tableCellWidth: number;
    isMobile: boolean;
}

const HeaderCell: React.FC<HeaderTable> = ({
    questions,
    tableCellWidth,
    isMobile,
}) => {
    return (
        <TableHead>
            <TableRow>
                <HeaderTableCell
                    text="カート番号"
                    sx={{
                        ...(!isMobile && {
                            position: "sticky",
                            left: 0,
                            zIndex: 2
                        }),
                        background: "white",

                    }}
                />
                <HeaderTableCell
                    text="プレー日"
                    sx={{
                        ...(!isMobile && {
                            position: "sticky",
                            left: tableCellWidth,
                            zIndex: 2
                        }),
                        background: "white",
                    }}
                />
                <HeaderTableCell
                    text="スタート"
                    sx={{
                        ...(!isMobile && {
                            position: "sticky",
                            left: tableCellWidth * 2,
                            zIndex: 2
                        }),
                        background: "white",
                    }}
                />
                <HeaderTableCell
                    text="名前"
                    className="hidden-on-small"
                    sx={{
                        ...(!isMobile && {
                            position: "sticky",
                            left: tableCellWidth * 3,
                            zIndex: 2
                        }),
                        background: "white",
                    }}
                />
                <HeaderTableCell
                    text={ CommonUtils.getCaddyNameFromLocalStorage() }
                    sx={{
                        ...(!isMobile && {
                            position: "sticky",
                            left: tableCellWidth * 4,
                            zIndex: 2
                        }),
                        background: "white",
                    }}
                />
                {questions.map((question) => (
                    <HeaderTableCell
                        key={question.id}
                        text={`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}\n設問${question.index}`}
                        className="hidden-on-small"
                        style={{ whiteSpace: 'pre-wrap' }}
                        sx={{
                            zIndex: 0,
                            background: "white",
                        }}
                    />
                ))}

                <HeaderTableCell
                    text="自由入力"
                    className="hidden-on-small"
                    sx={{
                        background: "white",
                        zIndex: 0
                    }}
                />
                <HeaderTableCell
                    text={CommonUtils.getCaddyNameFromLocalStorage() + "自由入力"}
                    className="hidden-on-small"
                    sx={{
                        background: "white",
                        zIndex: 0
                    }}
                />
                <HeaderTableCell
                    text="ゴルフ場自由入力"
                    className="hidden-on-small"
                    sx={{
                        background: "white",
                        zIndex: 0
                    }}
                />
                <HeaderTableCell
                    text="回答日時"
                    className="hidden-on-small"
                    sx={{
                        position: "sticky", right: 0, background: "white",
                        zIndex: 2
                    }}
                />
            </TableRow>
        </TableHead>
    );
};

export default HeaderCell;