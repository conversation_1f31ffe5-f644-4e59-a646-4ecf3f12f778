import React from "react";
import Link from "next/link";

interface BreadcrumbProps {
  items: { label: string; href?: string }[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <div className="mb-4">
      {items.map((item, index) => (
        <span key={index}>
          {item.href ? (
            <Link href={item.href} legacyBehavior>
              <a className="text-blue-500 hover:underline">{item.label}</a>
            </Link>
          ) : (
            <span>{item.label}</span>
          )}
          {index < items.length - 1 && " > "}
        </span>
      ))}
    </div>
  );
};

export default Breadcrumb;
