import {
    Collapse,
    Table,
    Typography,
    TableBody,
    TableCell as Mu<PERSON><PERSON><PERSON><PERSON><PERSON>,
    TableHead,
    IconButton,
    Box,
    TableRow,
} from "@mui/material";
import { styled } from "@mui/system";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import AddBoxOutlinedIcon from "@mui/icons-material/AddBoxOutlined";
import IndeterminateCheckBoxOutlinedIcon from "@mui/icons-material/IndeterminateCheckBoxOutlined";

import { formatDate, formatTime } from "../utils/formatters";
import { Caddy, Question, Survey, Cart, Evaluation, Lowest } from "../models/caddy-models";
import FeedbackDisplay from "./feedback-display";
import HeaderCell from "./header-cell";
import { CommonUtils } from '@/utils/common-utils';

interface AnswerTableComponentProps {
    rows: Cart[];
    lowest: Lowest[];
    questions: Question[];
    caddies: Caddy[];
    evaluations: Evaluation[];
    maxScoreId: number;
}

const AnswerTableComponent: React.FC<AnswerTableComponentProps> = ({
    rows,
    lowest,
    questions,
    caddies,
    evaluations,
    maxScoreId,
}) => {
    const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({
    });
    const [expandedFeedback, setExpandedFeedback] = useState<{ [key: string]: boolean }>({});


    const [isMobile, setMobile] = useState(false);
    useEffect(() => {
        const handleResize = () => {
            setMobile(window.innerWidth <= 910);
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    const handleRowClick = (id: string) => {
        setExpandedRows((prevState) => ({
            ...prevState,
            [id]: !prevState[id],
        }));
    };

    const calculateWidth = (questionsLength: number) => {
        return !isMobile ? `${(1 / (questionsLength + 7)) * 100}%` : "16.6%";
    };

    const minTableCellWidth = 130;
    const smallMinTableCellWidth = 80;
    const TableCell = styled(MuiTableCell)(() => ({
        // width: calculateWidth(questions.length),
        width: tableCellWidth,
        maxWidth: tableCellWidth,
        minWidth: !isMobile ? minTableCellWidth : "auto",
        wordBreak: "break-all",
    }));

    const SmallTableCell = styled(MuiTableCell)(() => ({
        width: calculateWidth(questions.length),
        minWidth: smallMinTableCellWidth,
        wordBreak: "break-all",
    }));

    const [tableCellWidth, setTableCellWidth] = useState(0);
    const tableCellRef = useRef<HTMLTableSectionElement | null>(null);

    useEffect(() => {
        const handleResize = () => {
            if (tableCellRef.current) {
                const currentWidth =
                    tableCellRef.current?.getBoundingClientRect().width /
                    (questions.length + 7);
                console.log("TableCell width:", currentWidth);
                setTableCellWidth(Math.max(currentWidth, minTableCellWidth));
            }
        };

        window.addEventListener("resize", handleResize);
        handleResize();

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [questions.length]);

    useEffect(() => {
        const defaultExpanded = rows.reduce((acc, cart) => {
            acc[cart.id] = cart.showLowest;
            return acc;
        }, {} as { [key: string]: boolean });

        setExpandedRows(defaultExpanded);
    }, [rows]);

    const handleFeedbackToggle = (id: string) => {
        setExpandedFeedback((prevState) => ({
            ...prevState,
            [id]: !prevState[id],
        }));
    };

    return (
        <>
            {rows && rows.map((row, index) => (
                <div key={row.id}>
                    <Table>
                        {/*father item header */}
                        {index === 0 && (
                            <HeaderCell
                                questions={questions}
                                tableCellWidth={tableCellWidth}
                                isMobile={isMobile}
                            />
                        )}
                        <TableBody ref={tableCellRef}>
                            <TableRow key={row.id}>
                                <TableCell
                                    sx={{
                                        ...(!isMobile && { position: "sticky", left: 0, zIndex: 2 }),
                                        background: "white",
                                        paddingLeft: 0,
                                        paddingRight: 0,
                                    }}
                                >
                                    <Box
                                        display="flex"
                                        alignItems="center"
                                        justifyContent="space-between"
                                    >
                                        <IconButton
                                            aria-label="expand row"
                                            size="small"
                                            onClick={() => handleRowClick(row.id.toString())}
                                        >
                                            {expandedRows[row.id] ? (
                                                <IndeterminateCheckBoxOutlinedIcon />
                                            ) : (
                                                <AddBoxOutlinedIcon />
                                            )}
                                        </IconButton>
                                        <Typography
                                            variant="h2"
                                            align="center"
                                            style={{ flexGrow: 1, textAlign: "left", marginLeft: 6 }}
                                        >
                                            {"No." + " " + row.cart_no}
                                        </Typography>
                                    </Box>
                                </TableCell>
                                <TableCell
                                    sx={{
                                        ...(!isMobile && {
                                            position: "sticky",
                                            left: tableCellWidth,
                                            zIndex: 2
                                        }),
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        {formatDate(row.played_date)}
                                    </Typography>
                                </TableCell>
                                <TableCell
                                    sx={{
                                        ...(!isMobile && {
                                            position: "sticky",
                                            left: tableCellWidth * 2,
                                            zIndex: 2,
                                        }),

                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        {/*{row.status === 1 ? "OUT" : "IN"}*/}
                                        {row.start_course.toString()}
                                        <br />
                                        {formatTime(row.start_time)}
                                    </Typography>
                                </TableCell>

                                <TableCell
                                    className="hidden-on-small"
                                    sx={{
                                        ...(!isMobile && {
                                            position: "sticky",
                                            left: tableCellWidth * 3,
                                            zIndex: 2
                                        }),
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        {row?.survey?.[0]?.name && row.survey[0].name}
                                    </Typography>
                                </TableCell>
                                <TableCell
                                    sx={{
                                        ...(!isMobile && {
                                            position: "sticky",
                                            left: tableCellWidth * 4,
                                            zIndex: 2,
                                        }),
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        {caddies.find((caddy) => caddy.caddy_no === row.caddy_id)?.caddy_name ||
                                            ""}
                                    </Typography>
                                </TableCell>
                                {questions.map((question) => (
                                    <TableCell key={question.id} className="hidden-on-small"
                                        sx={{
                                            zIndex: 0,
                                            background: "white",
                                        }}>
                                        <Typography variant="h2" align="center"
                                            style={{
                                                color: (() => {
                                                    const answer = row?.survey?.[0]?.answers?.find(
                                                        (ans) => ans.id === question.id
                                                    )?.answer;

                                                    if (!answer || !lowest) return 'black';

                                                    const evaluationMatch = lowest.find(
                                                        (low) =>
                                                            low.question_id === question.id && low.evaluation_id === answer
                                                    );


                                                    const maxScoreMatch = lowest.find(
                                                        (low) => low.question_id === question.id && low.evaluation_id === maxScoreId
                                                    );

                                                    if (maxScoreMatch) return 'black';

                                                    return evaluationMatch ? 'red' : 'black';
                                                })(),
                                            }}
                                        >
                                            {(() => {
                                                const answers = row?.survey?.[0]?.answers ?? [];
                                                if (answers.length === 0) {
                                                    return "-";
                                                }
                                                const answer = answers.find(
                                                    (ans) =>
                                                        ans.id === question.id
                                                )
                                                    ?.answer
                                                const evaluationScore = answer ? evaluations.find(evaluation => evaluation.id === answer)?.score : "";
                                                return evaluationScore ? evaluationScore.toString() : "未回答";
                                            })()}
                                        </Typography>
                                    </TableCell>
                                ))}

                                <TableCell
                                    className="hidden-on-small"
                                    sx={{
                                        zIndex: 0,
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        <FeedbackDisplay
                                            feedback={row?.survey?.[0]?.feedback || ""}
                                            id={row?.survey?.[0]?.id.toString()}
                                            handleFeedbackToggle={handleFeedbackToggle}
                                            expandedFeedback={expandedFeedback}
                                        />
                                    </Typography>
                                </TableCell>

                                <TableCell
                                    className="hidden-on-small"
                                    sx={{
                                        zIndex: 0,
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        <FeedbackDisplay
                                            feedback={row?.survey?.[0]?.feedback_caddy || ""}
                                            id={row?.survey?.[0]?.id.toString()}
                                            handleFeedbackToggle={handleFeedbackToggle}
                                            expandedFeedback={expandedFeedback}
                                        />
                                    </Typography>
                                </TableCell>

                                <TableCell
                                    className="hidden-on-small"
                                    sx={{
                                        zIndex: 0,
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        <FeedbackDisplay
                                            feedback={row?.survey?.[0]?.feedback_golf || ""}
                                            id={row?.survey?.[0]?.id.toString()}
                                            handleFeedbackToggle={handleFeedbackToggle}
                                            expandedFeedback={expandedFeedback}
                                        />
                                    </Typography>
                                </TableCell>

                                <TableCell
                                    className="hidden-on-small"
                                    sx={{
                                        borderBottom: `1px solid primary.main`,
                                        ...(!isMobile && { position: "sticky", right: 0, zIndex: 2 }),
                                        background: "white",
                                    }}
                                >
                                    <Typography variant="h2" align="center">
                                        {row?.survey?.[0]?.answer_time && formatDate(row.survey[0].answer_time)}
                                        <br />
                                        {row?.survey?.[0]?.answer_time && formatTime(row.survey[0].answer_time)}
                                    </Typography>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>

                    {!isMobile ? (
                        <Collapse
                            in={expandedRows[row.id]}
                            timeout="auto"
                            unmountOnExit
                        >
                            {rows[index]?.survey?.map((sur, surIndex) => (
                                <Table key={surIndex}>
                                    {surIndex > 0 && (
                                        <TableBody>
                                            <TableRow key={sur.id}>
                                                <TableCell
                                                    sx={{
                                                        position: "sticky",
                                                        background: "white",
                                                        left: 0,
                                                        zIndex: 1,
                                                        borderBottom: 0,
                                                        paddingLeft: 0,
                                                        paddingRight: 0,
                                                    }}
                                                >
                                                    <Box
                                                        display="flex"
                                                        alignItems="center"
                                                        justifyContent="space-between"
                                                    >
                                                        <div style={{ width: "35px" }}></div>{" "}
                                                        <Typography
                                                            variant="h2"
                                                            align="center"
                                                            style={{ flexGrow: 1, textAlign: "left", marginLeft: 6 }}
                                                        >
                                                            {"No." + " " + row.cart_no}
                                                        </Typography>
                                                    </Box>
                                                    <div
                                                        style={{
                                                            position: "absolute",
                                                            bottom: 0,
                                                            left: "50px",
                                                            right: 0,
                                                            height: "1px",
                                                            background: "rgba(0, 0, 0, 0.12)",
                                                        }}
                                                    ></div>
                                                </TableCell>
                                                <TableCell
                                                    sx={{
                                                        position: "sticky",
                                                        left: tableCellWidth,
                                                        background: "white",
                                                        zIndex: 1,
                                                        borderBottom: 0,
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        {formatDate(sur.answer_time)}
                                                    </Typography>
                                                    <DevLine />
                                                </TableCell>
                                                <TableCell
                                                    sx={{
                                                        position: "sticky",
                                                        left: tableCellWidth * 2,
                                                        background: "white",
                                                        borderBottom: 0,
                                                        zIndex: 1,
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        {/*{row.status === 1 ? "OUT" : "IN"}*/}
                                                        {row.start_course.toString()}
                                                        <br />
                                                        {formatTime(row.start_time)}
                                                    </Typography>
                                                    <DevLine />
                                                </TableCell>
                                                <TableCell
                                                    className="hidden-on-small"
                                                    sx={{
                                                        position: "sticky",
                                                        left: tableCellWidth * 3,
                                                        background: "white",
                                                        borderBottom: 0,
                                                        zIndex: 1,
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        {sur.name}
                                                    </Typography>
                                                    <DevLine />
                                                </TableCell>
                                                <TableCell
                                                    sx={{
                                                        position: "sticky",
                                                        left: tableCellWidth * 4,
                                                        background: "white",
                                                        borderBottom: 0,
                                                        zIndex: 1,
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        {caddies.find((caddy) => caddy.caddy_no === row.caddy_id)?.caddy_name || ""}
                                                    </Typography>
                                                    <DevLine />
                                                </TableCell>
                                                {questions.map((question) => (
                                                    <TableCell
                                                        key={question.id}
                                                        sx={{
                                                            position: "sticky",
                                                            background: "white",

                                                            borderBottom: 0,
                                                        }}
                                                    >
                                                        <Typography
                                                            variant="h2"
                                                            align="center"
                                                            style={{
                                                                color: (() => {
                                                                    const answer = sur?.answers?.find(
                                                                        (ans) => ans.id === question.id
                                                                    )?.answer;

                                                                    if (!answer || !lowest) return 'black';

                                                                    const evaluationMatch = lowest.find(
                                                                        (low) =>
                                                                            low.question_id === question.id && low.evaluation_id === answer
                                                                    );

                                                                    const maxScoreMatch = lowest.find(
                                                                        (low) => low.question_id === question.id && low.evaluation_id === maxScoreId
                                                                    );

                                                                    if (maxScoreMatch) return 'black';

                                                                    return evaluationMatch ? 'red' : 'black';
                                                                })(),
                                                            }}
                                                        >
                                                            {(() => {
                                                                const answers = sur.answers ?? [];
                                                                if (answers.length === 0) {
                                                                    return "-";
                                                                }
                                                                const answer = answers
                                                                    .find(
                                                                        (ans) =>
                                                                            ans.id === question.id
                                                                    )
                                                                    ?.answer
                                                                const evaluationScore = answer ? evaluations.find(evaluation => evaluation.id === answer)?.score : "";
                                                                return evaluationScore ? evaluationScore.toString() : "未回答";
                                                            })()}
                                                        </Typography>
                                                        <DevLine />
                                                    </TableCell>
                                                ))}

                                                <TableCell
                                                    className="hidden-on-small"
                                                    sx={{
                                                        zIndex: 0,
                                                        background: "white",
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </TableCell>

                                                <TableCell
                                                    className="hidden-on-small"
                                                    sx={{
                                                        zIndex: 0,
                                                        background: "white",
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback_caddy || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </TableCell>

                                                <TableCell
                                                    className="hidden-on-small"
                                                    sx={{
                                                        zIndex: 0,
                                                        background: "white",
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback_golf || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </TableCell>

                                                <TableCell
                                                    className="hidden-on-small"
                                                    sx={{
                                                        position: "sticky",
                                                        background: "white",
                                                        right: 0,
                                                        borderBottom: 0,
                                                        zIndex: 1,
                                                    }}
                                                >
                                                    <Typography variant="h2" align="center">
                                                        {formatDate(sur.answer_time)}
                                                        <br />
                                                        {formatTime(sur.answer_time)}
                                                    </Typography>
                                                    <DevLine />
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    )}{" "}
                                </Table>
                            ))}
                        </Collapse>
                    ) : (
                        <Collapse
                            in={expandedRows[row.id]}
                            timeout="auto"
                            unmountOnExit
                        >
                            <Box margin={1} overflow="auto">
                                <Table size="small" aria-label="purchases">
                                    <TableHead>
                                        <TableRow>
                                            <TableCell
                                                sx={{
                                                    borderBottom: `1px solid primary.main`,
                                                    position: "sticky",
                                                    background: "white",
                                                    left: 0,
                                                    zIndex: 1,
                                                }}
                                            >
                                                <Typography
                                                    sx={{ fontWeight: "bold" }}
                                                    color="primary"
                                                    variant="h4"
                                                    align="center"
                                                >
                                                    名前
                                                </Typography>
                                            </TableCell>
                                            {questions.map((question) => (
                                                <TableCell
                                                    key={question.id}
                                                    sx={{ borderBottom: `1px solid primary.main` }}
                                                >
                                                    <Typography
                                                        sx={{ fontWeight: "bold" }}
                                                        color="primary"
                                                        variant="h4"
                                                        align="center"
                                                    >
                                                        {question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"} 設問{" "}
                                                        {question.index}
                                                    </Typography>
                                                </TableCell>
                                            ))}

                                            <TableCell
                                                sx={{ borderBottom: `1px solid primary.main` }}
                                            >
                                                <Typography
                                                    sx={{ fontWeight: "bold" }}
                                                    color="primary"
                                                    variant="h4"
                                                    align="center"
                                                >
                                                    自由入力
                                                </Typography>
                                            </TableCell>
                                            <TableCell
                                                sx={{ borderBottom: `1px solid primary.main` }}
                                            >
                                                <Typography
                                                    sx={{ fontWeight: "bold" }}
                                                    color="primary"
                                                    variant="h4"
                                                    align="center"
                                                >
                                                    {CommonUtils.getCaddyNameFromLocalStorage()}自由入力
                                                </Typography>
                                            </TableCell>
                                            <TableCell
                                                sx={{ borderBottom: `1px solid primary.main` }}
                                            >
                                                <Typography
                                                    sx={{ fontWeight: "bold" }}
                                                    color="primary"
                                                    variant="h4"
                                                    align="center"
                                                >
                                                    ゴルフ場自由入力
                                                </Typography>
                                            </TableCell>
                                            <TableCell
                                                sx={{
                                                    borderBottom: `1px solid primary.main`,
                                                    position: "sticky",
                                                    background: "white",
                                                    right: 0,
                                                }}
                                            >
                                                <Typography
                                                    sx={{ fontWeight: "bold" }}
                                                    color="primary"
                                                    variant="h4"
                                                    align="center"
                                                >
                                                    回答日時
                                                </Typography>
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {row?.survey?.map((sur, surIndex) => (
                                            <TableRow key={surIndex}>
                                                <TableCell
                                                    sx={{
                                                        borderBottom: `1px solid primary.main`,
                                                        position: "sticky",
                                                        background: "white",
                                                        left: 0,
                                                        zIndex: 1,
                                                    }}
                                                >
                                                    <Typography variant="h5" align="center">
                                                        {sur.name}
                                                    </Typography>
                                                </TableCell>
                                                {questions.map((question) => (
                                                    <SmallTableCell key={question.id}>
                                                        <Typography
                                                            variant="h2"
                                                            align="center"
                                                            style={{
                                                                color: (() => {
                                                                    const answer = sur?.answers?.find(
                                                                        (ans) => ans.id === question.id
                                                                    )?.answer;

                                                                    if (!answer || !lowest) return 'black';

                                                                    const evaluationMatch = lowest.find(
                                                                        (low) =>
                                                                            low.question_id === question.id && low.evaluation_id === answer
                                                                    );

                                                                    const maxScoreMatch = lowest.find(
                                                                        (low) => low.question_id === question.id && low.evaluation_id === maxScoreId
                                                                    );

                                                                    if (maxScoreMatch) return 'black';

                                                                    return evaluationMatch ? 'red' : 'black';
                                                                })(),
                                                            }}
                                                        >
                                                            {(() => {
                                                                const answers = sur.answers ?? [];
                                                                if (answers.length === 0) {
                                                                    return "-";
                                                                }
                                                                const answer = answers
                                                                    .find(
                                                                        (ans) =>
                                                                            ans.id === question.id
                                                                    )
                                                                    ?.answer
                                                                const evaluationScore = answer ? evaluations.find(evaluation => evaluation.id === answer)?.score : "";
                                                                return evaluationScore ? evaluationScore.toString() : "未回答";
                                                            })()}
                                                        </Typography>
                                                    </SmallTableCell>
                                                ))}

                                                <SmallTableCell >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </SmallTableCell>

                                                <SmallTableCell >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback_caddy || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </SmallTableCell>

                                                <SmallTableCell >
                                                    <Typography variant="h2" align="center">
                                                        <FeedbackDisplay
                                                            feedback={sur.feedback_golf || ""}
                                                            id={sur.id.toString()}
                                                            handleFeedbackToggle={handleFeedbackToggle}
                                                            expandedFeedback={expandedFeedback}
                                                        />
                                                    </Typography>
                                                </SmallTableCell>


                                                <TableCell
                                                    sx={{
                                                        position: "sticky",
                                                        background: "white",
                                                        right: 0,
                                                    }}
                                                >
                                                    <Typography variant="h5" align="center">
                                                        {formatDate(sur.answer_time)}
                                                        <br />
                                                        {formatTime(sur.answer_time)}
                                                    </Typography>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </Box>
                        </Collapse>
                    )}
                </div>
            ))}
        </>
    );
};

const DevLine = () => {
    return (
        <div
            style={{
                position: "absolute",
                bottom: 0,
                left: "0px",
                right: 0,
                height: "1px",
                background: "rgba(0, 0, 0, 0.12)",
            }}
        ></div>
    );
};
export default AnswerTableComponent;