import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Backdrop, Typography } from "@mui/material";
import HideHoleTableComponent, { HideHoleTableProps, HideCourseProps, HideHoleProps } from "@/components/onlinecompe/hide-hole-table-component"
import { useEffect, useState } from "react";

export interface HideHoleDialogProps {
    openHideHoleDialog: boolean;
    closeHideHoleDialog: () => void;
    golfName: string;
    hideHole: HideHoleTableProps;
    cancelHideHoleSetting: () => void;
    hideHoleTitle: string;
    competitionName: string;
    randomSelecteHole: () => void;
    checkHideHoleSetting: () => void;
}
const HideHoleDialog: React.FC<HideHoleDialogProps> = ({
    openHideHoleDialog,
    closeHideHoleDialog,
    golfName,
    hideHole,
    cancelHideHoleSetting,
    hideHoleTitle,
    competitionName,
    randomSelecteHole,
    checkHideHoleSetting,
}) => {
    const dialogStyle = {
        width: 'auto',
        display: 'inline-block',
        maxWidth: '90%',
        minWidth: '700px',
        bgcolor: "background.paper",
        boxShadow: 24,
        borderRadius: 1,
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
    }
    return (
        <Modal
            aria-labelledby="transition-modal-title"
            aria-describedby="transition-modal-description"
            open={openHideHoleDialog}
            onClose={(event, reason) => {
                if (reason === 'backdropClick') return;
                closeHideHoleDialog();
            }}
            closeAfterTransition
            keepMounted
            slots={{ backdrop: Backdrop }}
            slotProps={{
                backdrop: {
                    timeout: 0,
                },
            }}
        >
            <Box sx={dialogStyle}>
                <Box sx={{ display: 'flex', direction: 'row', marginTop: '23px', marginLeft: '26px', marginRight: '23px' }}>
                    <Typography id="transition-modal-title" sx={{
                        display: "flex",
                        direction: 'row',
                        justifyContent: "space-between",
                        width: "100%",
                        alignItems: "baseline",
                    }}>
                        <Box component="span" sx={{ display: "flex", direction: 'row', gap: 1, alignItems: "baseline" }}>
                            <span style={{ fontSize: '24px', fontWeight: 'bold', color: 'black' }}>隠しホール設定</span>
                            <span style={{ fontSize: '20px', fontWeight: 'normal', color: 'black' }}> ({hideHoleTitle})</span>
                        </Box>
                        <span style={{ fontSize: '16px', fontWeight: 'bold', color: 'black' }}> コンペ名：{competitionName}</span>
                    </Typography>
                </Box>
                <div style={{
                    marginTop: '9px',
                    marginLeft: '23px',
                    marginRight: '22px',
                    border: '1px solid #707070 ',
                }}></div>
                <Typography sx={{ marginTop: '6px', marginLeft: '26px', fontSize: '16px' }}>
                    ホールをクリックして隠しホールを設定/変更できます。
                </Typography>
                <Typography sx={{ marginTop: '6px', marginLeft: '26px', fontSize: '16px', fontWeight: 'bold' }}>
                    {golfName}
                </Typography>
                <HideHoleTableComponent courses={hideHole.courses} />
                <Box sx={{
                    display: 'flex',
                    direction: 'row',
                    justifyContent: "space-between",
                    marginLeft: '26px',
                    marginRight: '29px',
                    marginTop: '73px',
                    marginBottom: '23px'
                }}>
                    <Button sx={{
                        width: '100px',
                        height: '30px',
                        border: '1px solid',
                        fontSize: '12px',
                        fontWeight: 'medium',
                    }}
                        onClick={cancelHideHoleSetting}>
                        キャンセル
                    </Button>
                    <Button sx={{
                        width: '150px',
                        height: '30px',
                        bgcolor: '#324F85',
                        color: 'white',
                        fontSize: '12px',
                        fontWeight: 'medium',
                        '&:hover': {
                            backgroundColor: '#324F8580',
                            color: '#white',
                        },
                    }}
                        onClick={randomSelecteHole}>
                        全コースランダム選出
                    </Button>
                    <Button sx={{
                        width: '50px',
                        height: '30px',
                        bgcolor: '#324F85',
                        color: 'white',
                        fontSize: '12px',
                        fontWeight: 'medium',
                        '&:hover': {
                            backgroundColor: '#324F8580',
                            color: '#white',
                        },
                    }}
                        onClick={checkHideHoleSetting}>
                        設定
                    </Button>
                </Box>
            </Box>
        </Modal>
    );;
}

export default HideHoleDialog;
