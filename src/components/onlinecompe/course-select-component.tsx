import React from "react";
import {
  TextField,
  MenuItem,
  Box
} from "@mui/material";

export interface SelectOptions {
  value: string;
  label: string;
}
export interface CourseSelectProps {
  index: number;
  errorList: string[] | null;
  duplication: boolean[]
  selectedOption: string;
  roundType: string;
  onOptionChange: (event: any) => void;
  options: SelectOptions[];
}

const CourseSelectComponent: React.FC<CourseSelectProps> = ({
  index,
  errorList,
  duplication,
  selectedOption,
  roundType,
  onOptionChange,
  options,
}) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', }}
    >
      {index != null ?
        <div style={{ marginLeft: '0px', marginRight: '40px' }}> {index}.</div> : <div></div>}
      <TextField
        select
        error={errorList?.includes('course') || duplication[index - 1]}
        variant="outlined"
        size="small"
        name="selectedCourse"
        value={selectedOption}
        onChange={onOptionChange}
        style={{ width: '250px', fontSize: '14px', height: "30px" }}
        InputProps={{
          style: { height: "30px" },
        }}
        // when (index+1) > (roundType * 2) disable
        disabled={index > (Number(roundType) * 2)}
      >
        {options.map((option, index) => (
          <MenuItem key={index} value={option.value}>
            {option.label === '' ? ' ' : option.label}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default CourseSelectComponent;
