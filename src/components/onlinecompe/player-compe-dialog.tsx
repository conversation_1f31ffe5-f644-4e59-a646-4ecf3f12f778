import React, { useState, useEffect } from 'react';
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    Typography,
    IconButton,
    Box,
    Button,
    Checkbox,
    FormControlLabel,
    List,
    ListItem,
    Divider
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { OnlineCompe } from '@/models/compe/req/online-compe-creation';

interface PlayerCompeDialogProps {
    open: boolean;
    onClose: () => void;
    playerName: string;
    joinedCompes: OnlineCompe[];
    availableCompes: AvailableCompesData[];
    joinCompes: (compe: AvailableCompesData[]) => void;
}

export interface AvailableCompesData {
    name: string;
    fee: number;
    compeNo: number;
    isJoined?: boolean;
}

const PlayerCompeDialog: React.FC<PlayerCompeDialogProps> = ({
    open,
    onClose,
    playerName,
    joinedCompes,
    availableCompes,
    joinCompes,
}) => {
    const [selectCompe, setSelectCompe] = useState<AvailableCompesData[]>(availableCompes);
    useEffect(() => {
        if (open) {
            setSelectCompe(availableCompes);
        }
    }, [open, availableCompes]);
    const handleSelectCompe = (index: number) => {
        setSelectCompe(prev =>
            prev.map((compe, i) => i === index ? { ...compe, isJoined: !compe.isJoined } : compe)
        );
    }
    return (
        <Dialog
            open={open}
            onClose={onClose}
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: 2,
                    maxHeight: '80vh'
                }
            }}
            sx={{
                '& .MuiDialog-paper': {
                    width: '800px',
                    maxWidth: 'none'
                }
            }}
        >
            <DialogTitle sx={{
                m: 0,
                p: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }}>
                {playerName !== '' && <Typography sx={{ fontSize: '22px', fontWeight: 'bold' }} component="div">
                    {playerName} 様
                </Typography>
                }
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Box sx={{ display: 'flex', gap: 4 }}>
                    {/* Left side - Joined Competitions */}
                    <Box sx={{ flex: 1, flexDirection: 'column', textAlign: 'center' }}>
                        <Typography sx={{ mb: 2, fontSize: '20px', fontWeight: 'bold' }}>
                            参加コンペ
                        </Typography>
                        <Box sx={{
                            height: '1px',
                            width: 'auto',
                            background: '#707070'
                        }}
                        ></Box>
                        <List sx={{ height: '270px', overflow: 'auto' }}>
                            {joinedCompes.map((compe, index) => (
                                <ListItem
                                    key={index}
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        mb: 1
                                    }}
                                >
                                    <Typography color="primary" sx={{ flex: 1 }}>
                                        {compe.basic.compe_name}
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        size="small"
                                        sx={{ ml: 2 }}
                                    >
                                        詳細
                                    </Button>
                                </ListItem>
                            ))}
                        </List>
                    </Box>

                    {/* <Divider orientation="vertical" flexItem /> */}

                    {/* Right side - Available Competitions */}
                    <Box sx={{ flex: 1, flexDirection: 'column', textAlign: 'center' }}>
                        <Typography sx={{ mb: 2, fontSize: '20px', fontWeight: 'bold' }}>
                            本日参加可能コンペ一覧
                        </Typography>
                        <Box sx={{
                            height: '1px',
                            width: 'auto',
                            background: '#707070'
                        }}
                        ></Box>
                        <Typography variant="body2" sx={{ mb: 2, marginTop: '10px', fontSize: '16px' }}>
                            追加で参加登録するコンペを選択してください。
                        </Typography>
                        <List sx={{ height: '270px', overflow: 'auto' }}>
                            {selectCompe.map((compe, index) => (
                                <ListItem
                                    key={index}
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'stretch',
                                        backgroundColor: compe.isJoined ? '#e8f5e9' : '#EDEDED',
                                        borderRadius: '10px',
                                        mb: '10px',
                                        p: 1
                                    }}
                                >
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                icon={<img src={"/webapp/images/compe_ic_box.png"} style={{ width: '34px', height: '34px' }} />}
                                                checkedIcon={<img src={"/webapp/images/compe_ic_correct_box.png"} style={{ width: '34px', height: '34px' }} />}
                                                checked={compe.isJoined}
                                                onChange={() => { handleSelectCompe(index) }}
                                            />
                                        }
                                        label=''
                                    />
                                    <Box display="flex" flexDirection='column' justifyContent="flex-start" sx={{ flex: 1 }}>
                                        <Typography variant="body2"
                                            sx={{
                                                fontSize: '20px',
                                                fontWeight: 'bold',
                                                width: '227px',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                            }}>
                                            {compe.name}
                                        </Typography>
                                        <Box sx={{
                                            height: '1px',
                                            width: 'auto',
                                            background: '#707070'
                                        }}
                                        ></Box>
                                        <Typography variant="body2" sx={{ fontSize: '16px', width: '100%', }}>
                                            参加料金: ¥{compe.fee.toLocaleString()}(税込)
                                        </Typography>
                                    </Box>

                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={() => {
                                    joinCompes(selectCompe);
                                    onClose();
                                }}
                            >
                                コンペ参加登録
                            </Button>
                        </Box>
                    </Box>
                </Box>
            </DialogContent>
        </Dialog>
    );
};

export default PlayerCompeDialog;