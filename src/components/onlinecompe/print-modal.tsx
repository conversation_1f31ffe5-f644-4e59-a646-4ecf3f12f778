import React from "react";
import { Box, Typography, Button, Divider, Icon<PERSON>utton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "35%",
  bgcolor: "background.paper",
  borderRadius: "10px",
  boxShadow: 24,
  p: 4,
};

interface KeyModalProps {
  onClose: () => void;
  onPrint: () => void;
}

const ShareKeyModal = ({ onClose, onPrint }: KeyModalProps) => {
  // 印刷ボタン
  const handleClick = () => {
    onPrint();
  };

  return (
    <Box sx={style}>
      <div className="flex items-center justify-between">
        <Typography noWrap component="div" variant="h1">
          印刷
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </div>
      <Divider className="my-2" />
      <div className="flex items-baseline justify-center gap-8 py-10">
        <Typography noWrap component="div" variant="h1">
          スコアを印刷しますか？
        </Typography>
      </div>
      <Divider className="mb-4" />
      <div className="flex justify-end gap-4">
        <Button variant="outlined" onClick={onClose}>
          キャンセル
        </Button>
        <Button type="submit" variant="contained" onClick={handleClick}>
          印　刷
        </Button>
      </div>
    </Box>
  );
};

export default ShareKeyModal;
