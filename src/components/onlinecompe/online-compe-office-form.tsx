import React from "react";
import { TextField, MenuItem, Typography } from "@mui/material";
import { MobileDatePicker } from "@mui/x-date-pickers/MobileDatePicker";
import dayjs from "dayjs";
import "dayjs/locale/ja";

interface FilterFormProps {
  filters: {
    play_date: string | null;
    compe_type: number | null;
    compe_kind: string | null;
    free_word: string | null;
    offset: number;
    limit: number;
  };
  setFilters: React.Dispatch<
    React.SetStateAction<{
      play_date: string;
      compe_kind: string;
      compe_type: number;
      free_word: string;
      offset: number;
      limit: number;
    }>
  >;
}

// コンペタイプ
const compe_types = [
  { label: "全て", value: 2 },
  // { label: "チーム戦", value: 0 },
  { label: "個人戦", value: 1 },
];

// 競技方法
const compe_kinds = [
  { label: "全て", value: "all" },
  { label: "ハンディ", value: "handy" },
  { label: "ぺリア", value: "peoria" },
];

const FilterForm: React.FC<FilterFormProps> = ({ filters, setFilters }) => {
  // 開催日の変更
  const handleChange = (name: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [name]: value ? value : "",
    }));
  };

  return (
    <div className="w-full sm:w-auto flex flex-wrap gap-5">
      <div className="flex items-center gap-3">
        <Typography noWrap component="span" variant="h3">
          開催日
        </Typography>
        <MobileDatePicker
          value={filters.play_date ? dayjs(filters.play_date) : null}
          onChange={(newValue) => handleChange("play_date", newValue)}
          format="YYYY-MM-DD"
          slotProps={{
            textField: {
              size: "small",
              InputProps: {
                style: { width: "140px", height: "36px" },
              },
            },
          }}
        />
      </div>
      <div className="flex items-center gap-3">
        <Typography noWrap component="span" variant="h3">
          コンペタイプ
        </Typography>

        <TextField
          select
          variant="outlined"
          size="small"
          name="compe_type"
          value={filters.compe_type}
          onChange={(event) =>
            setFilters((prev) => ({
              ...prev,
              compe_type: Number(event.target.value),
            }))
          }
          InputProps={{
            style: { width: "140px", height: "36px" },
          }}
        >
          {compe_types.map((compe_type) => (
            <MenuItem key={compe_type.value} value={compe_type.value}>
              {compe_type.label}
            </MenuItem>
          ))}
        </TextField>
      </div>
      <div className="flex items-center gap-3">
        <Typography noWrap component="span" variant="h3">
          競技方法
        </Typography>

        <TextField
          select
          variant="outlined"
          size="small"
          name="compe_kind"
          value={filters.compe_kind}
          onChange={(event) => {
            setFilters((prev) => ({
              ...prev,
              compe_kind: event.target.value.toString(),
            }));
          }}
          InputProps={{
            style: { width: "140px", height: "36px" },
          }}
        >
          {compe_kinds.map((compe_kind) => (
            <MenuItem key={compe_kind.value} value={compe_kind.value}>
              {compe_kind.label}
            </MenuItem>
          ))}
        </TextField>
      </div>
      <div className="flex items-center gap-3">
        <Typography noWrap component="span" variant="h3">
          フリーワード
        </Typography>

        <TextField
          variant="outlined"
          autoComplete="off"
          size="small"
          name="free_word"
          value={filters.free_word}
          onChange={(event) => {
            setFilters((prev) => ({
              ...prev,
              free_word: event.target.value.toString(),
            }));
          }}
          InputProps={{
            style: { width: "180px", height: "36px" },
          }}
        ></TextField>
      </div>
    </div>
  );
};

export default FilterForm;
