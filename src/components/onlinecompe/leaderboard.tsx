"use client";
import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  TableContainer,
  Table,
  TableBody,
  TableHead,
  TableRow,
  Paper,
  TextField,
  MenuItem,
  Alert,
  Modal,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import PrintIcon from "@mui/icons-material/Print";
import AddIcon from "@mui/icons-material/Add";
import { RankingDetails } from "@/models/compe/resp/ranking-resp";
import { leaderboardRanking, rankingType } from "@/api/online-compe-api";
import PrintModal from "@/components/onlinecompe/print-modal";

interface CombinedPlayerData {
  player_no: string;
  player_name: string;
  pos: string;
  net: string;
  is_tied: number;
  score: string;
  hole: string;
  today: string;
  hdcp: string;
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.action.hover,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(even)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

interface LeaderboardProps {
  compeNo: number;
  compeName: string;
}

const Leaderboard = ({ compeNo, compeName }: LeaderboardProps) => {
  const style = {};
  // プレイヤーマップ
  const [playerMap, setPlayerMap] = useState<Map<string, CombinedPlayerData>>(
    new Map()
  );
  // 印刷モーダルの状態
  const [openPrint, setOpenPrint] = useState(false);
  // 集計方法リスト
  const [rankingTypes, setRankingTypes] = useState<string[]>([]);
  // 集計方法
  const [rankinType, setRankinType] = useState("");
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState("");

  // 集計方法情報
  const getRankingType = async () => {
    try {
      await rankingType(compeNo.toString()).then(async (i) => {
        const types = i.data.ranking_type;
        if (types?.length > 0) {
          console.log("types", types);
          setRankingTypes(types);
          setRankinType(types[0]);
        } else {
          setErrorMsg("該当データはありません。");
        }
      });
    } catch (error) {
      setErrorMsg("該当データはありません。");
    }
  };

  // 順位情報
  const getLeaderboardRanking = async (type: string) => {
    try {
      const { data }: { data: RankingDetails } = await leaderboardRanking(
        compeNo.toString(),
        type
      );
      if (data && data.rankings) {
        const newPlayerMap = new Map<string, CombinedPlayerData>();
        data.rankings.forEach((ranking) => {
          if (!newPlayerMap.has(ranking.player_no)) {
            newPlayerMap.set(ranking.player_no, {
              player_no: ranking.player_no,
              player_name: ranking.player_name,
              pos: ranking.pos,
              // int to string like +1,0,-1
              net: ranking.par_net > 0 ? `+${ranking.par_net}` : ranking.par_net.toString(),
              is_tied: ranking.is_tied,
              // int to string like +1,0,-1
              score: ranking.par_gross > 0 ? `+${ranking.par_gross}` : ranking.par_gross.toString(),
              hole: ranking.hole,
              today: ranking.score_gross.toString(),
              hdcp: ranking.course_hdcp,
            });
          }
        });
        setPlayerMap(newPlayerMap);
      } else {
        setErrorMsg("該当データはありません。");
      }
    } catch (error) {
      setErrorMsg("該当データはありません。");
    }
  };

  useEffect(() => {
    getRankingType();
  }, []);

  useEffect(() => {
    if (rankinType) {
      getLeaderboardRanking(rankinType);
    }
  }, [rankinType]);

  // ヘッダー セルのインターフェース
  interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: "center" | "left";
    format?: (value: number) => string;
  }

  // ヘッダー 定義
  const columns: readonly Column[] = [
    { id: "pos", label: "POS", align: "center", minWidth: 50 },
    { id: "name", label: "NAME", align: "left", minWidth: 170 },
    { id: "score", label: rankinType === "gross" ? "GROSS" : "NET", align: "center", minWidth: 170 },
    { id: "hole", label: "HOLE", align: "center", minWidth: 170 },
    { id: "today", label: "TODAY", align: "center", minWidth: 170 },
    { id: "hdcp", label: "HDCP", align: "center", minWidth: 170 },
  ];

  // 別端末で表示
  const handleURL = () => {
    window.open(`/webapp/project?compeNo=${compeNo}&compeName=${compeName}`, "_blank");
  };

  // 印刷ボタン
  const handlePrint = () => {
    setOpenPrint(true);
  };

  // 印刷モーダルを閉じる
  const handlePrintClose = () => {
    setOpenPrint(false);
  };

  // 印刷モーダルを印刷
  const onPrint = () => {
    setOpenPrint(false);
    const obj = {
      compeName,
      playerDateFrom: localStorage.getItem("playerDateFrom"),
      printType: "Leaderboard",
      playerMapLB: [...playerMap.values()],
      rankinType,
    };
    localStorage.setItem("printData", JSON.stringify(obj));
    window.open(
      `/webapp/onlinecompe/list/detail/print?compeNo=${compeNo}&compeName=${compeName}`,
      "_blank"
    );
  };

  return (
    <Box sx={style} id="printableArea">
      <div className="relative text-right -top-20">
        <Button
          variant="contained"
          color="primary"
          size="small"
          disabled={!!errorMsg}
          startIcon={<PrintIcon />}
          onClick={handlePrint}
        >
          印刷
        </Button>
        <Button
          variant="contained"
          color="info"
          size="small"
          disabled={!!errorMsg}
          startIcon={<AddIcon />}
          className="!ml-4"
          onClick={handleURL}
        >
          別端末で表示
        </Button>
      </div>
      {errorMsg && (
        <Alert severity="error" className="w-full">
          {errorMsg}
        </Alert>
      )}
      {!errorMsg && (
        <React.Fragment>
          <div className="flex justify-between items-center -mt-6 mb-2 w-2/3">
            <Typography className="font-bold">*=INスタート</Typography>
            <div className="gap-4 flex justify-between items-center">
              <Typography>集計方法切替</Typography>
              <TextField
                select
                variant="outlined"
                size="small"
                name="caddie"
                InputProps={{
                  style: { width: "150px", height: "36px" },
                }}
                value={rankinType}
                onChange={(event) => setRankinType(event.target.value)}
              >
                {rankingTypes.map((i) => (
                  <MenuItem key={i} value={i}>
                    {
                      i === "gross"
                        ? "グロス" :
                        i === "handy"
                          ? "ネット"
                          : i === "peoria"
                            ? "ぺリア"
                            : i === "new-peoria"
                              ? "新ペリア"
                              : i === "new-new-peoria"
                                ? "新新ペリア"
                                : i}
                  </MenuItem>
                ))}
              </TextField>
            </div>
          </div>
          <TableContainer
            component={Paper}
            className="!w-2/3"
            id="print-content"
          >
            <Table size="small">
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <StyledTableCell
                      key={column.id}
                      align={column.align}
                      sx={{ minWidth: column.minWidth }}
                    >
                      {column.label}
                    </StyledTableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {[...playerMap.values()].map((player, index) => (
                  <StyledTableRow key={index}>
                    <TableCell align="center">
                      {player.pos
                        ? `${player.pos}`
                        : "-"}
                    </TableCell>
                    <TableCell>{player.player_name}</TableCell>
                    <TableCell align="center">{rankinType === "gross" ? player.score : player.net}</TableCell>
                    <TableCell align="center">{player.hole}</TableCell>
                    <TableCell align="center">{player.today}</TableCell>
                    <TableCell align="center">{player.hdcp ? player.hdcp : "-"}</TableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </React.Fragment>
      )}
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={openPrint}
        onClose={handlePrintClose}
      >
        <PrintModal onClose={handlePrintClose} onPrint={onPrint} />
      </Modal>
    </Box>
  );
};

export default Leaderboard;
