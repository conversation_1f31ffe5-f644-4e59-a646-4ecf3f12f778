import React, {useEffect, useState} from "react";
import {
  Table,
  Typography,
  TableBody,
  TableCell as MuiTableCell,
  TableRow,
  TableCell,
  Button,
  TableContainer,
  Paper
} from "@mui/material";
export interface HideHoleTableProps {
  courses: HideCourseProps[]
}
export interface HideCourseProps{
  courseIndex: string;
  name: string;
  holes: HideHoleProps[];
  onHoleSelected: (courseName: string, holeId: string) => void;
}
export interface HideHoleProps{
  id: string
  isSelected: boolean,
  strokes: string,
}

const HideHoleRowComponent: React.FC<HideCourseProps> = ({
  name,
  holes,
  onHoleSelected
}) => {
  const unSelectedStyle = {
    border: '1px solid #FFBADA',
    backgroundColor: 'white',
    color: '#000000',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: 'bold',
    minWidth: 0,
    width: '37px',
    height: '23px',
    '&:hover': {
      backgroundColor: 'white',
      color: '#000000',
    },
  }
  const selectedStyle = {
    backgroundColor: '#FFBADA',
    color: '#000000',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: 'bold',
    minWidth: 0,
    width: '37px',
    height: '23px',
    '&:hover': {
      backgroundColor: '#FFBADA',
      color: '#000000',
    },
  }
  const [selectedHole, setSelectedHole] = useState<HideHoleProps[]>(holes);
  useEffect(()=>{
    setSelectedHole(holes);
  } ,[holes]);
  const onButtonClick = (courseName: string, holeId: string) => {
    onHoleSelected(name, holeId);
    setSelectedHole(prev =>
      prev.map(hole =>
        hole.id === holeId
          ? { ...hole, isSelected: !hole.isSelected }
          : hole
      )
    );
  };
  const getSelectedHoleNumber = (): number => {
    let count = 0;
    selectedHole.map(hole => {
      if (hole.isSelected) {
        count += 1;
      }
    });
    return count;
  } 
  const getCoureseStrokes = (): number => {
    let count = 0;
    selectedHole.map(hole => {
      count += Number(hole.strokes);
    });
    return count;
  } 
  return (
    <>
    <TableRow>
      <TableCell rowSpan={2} sx={{width: 'auto', bgcolor: '#EFEFEF'}} >{name}</TableCell>
      {selectedHole.map((hole, index) => (
        <TableCell key={index} sx={{ width: 'auto', alignItems: 'center', padding: '10px' }}>
          <Button variant="outlined" 
          key={index}
          size="small" 
          sx={hole.isSelected ? selectedStyle : unSelectedStyle}
          onClick={() => onButtonClick(name,hole.id)}
          >
            {hole.id}H
          </Button>
        </TableCell>
      ))}
      <TableCell sx={{width: 'auto', alignItems: 'center', padding: '10px', whiteSpace: 'nowrap'}}>
        <Typography sx={{whiteSpace: 'nowrap', marginLeft: '21px', fontSize: '14px'}}>
          選択 {getSelectedHoleNumber()}
        </Typography>
      </TableCell>
    </TableRow>
    <TableRow sx={{height: '31px', bgcolor: '#F8F8F8'}}>
      {holes.map((hole, index) => (
          <TableCell key={index} sx={{width: 'auto', alignItems: 'center', padding: '10px' }}>
          <div style={{textAlign: 'center', fontSize: '16px', fontWeight: 'bold'}}>{hole.strokes}</div>
          </TableCell>
      ))}
      <TableCell sx={{width: 'auto', alignItems: 'center',  padding: '10px'}}>
        <Typography sx={{whiteSpace: 'nowrap', marginLeft: '21px', fontSize: '14px'}}>
          打数 {getCoureseStrokes()}
        </Typography>
        </TableCell>
    </TableRow>
    </>
  );
}

const HideHoleTableComponent: React.FC<HideHoleTableProps> = ({
 courses
}) => {
  return (
    <TableContainer component={Paper} sx={{width: 'auto', marginLeft: '26px', marginRight: '29px'}}>
        <Table sx={{ width: 'auto', border: "1px solid #000" }}>
        <TableBody>
          {courses.map((course, index) => (
            <HideHoleRowComponent 
            key={index}
            courseIndex={course.courseIndex}
            name={course.name}
            holes={course.holes}
            onHoleSelected={course.onHoleSelected} ></HideHoleRowComponent>
          ))}
        </TableBody>
        </Table>
    </TableContainer>
  );
};

export default HideHoleTableComponent;
