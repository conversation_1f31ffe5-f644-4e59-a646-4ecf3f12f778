import React from "react";
import { List, ListItem } from "@mui/material";

const ScoreFormat = () => {
  return (
    <List className="flex" style={{ fontSize: 14 }}>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "red" }}>★ </span>: アルバトロス
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "red" }}>◎ </span>: イーグル
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "red" }}>○ </span>: バーディー
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span>－ </span>: パー
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "blue" }}>△ </span>: ボギー
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "blue" }}>□ </span>: ダブルボギー
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span style={{ color: "blue" }}>■ </span>: トリプルボギー
      </ListItem>
      <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
        <span>数字 </span>: オーバーパー
      </ListItem>
    </List>
  );
};

export default ScoreFormat;
