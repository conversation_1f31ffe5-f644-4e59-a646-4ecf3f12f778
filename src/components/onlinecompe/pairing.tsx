"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  Modal,
  TextField,
  Alert,
  MenuItem,
  Snackbar,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import PrintIcon from "@mui/icons-material/Print";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import { JoinedPlayer } from "@/models/compe/resp/online-compe-player";
import { OnlineCompeJoinedPlayersReq } from "@/models/compe/req/online-compe-player";
import { PlayerInfo } from "@/models/compe/resp/player-resp";
import { TeeInfo } from "@/models/tee/resp/tee-info";
import {
  listJoinedPlayers,
  getTeeInfos,
  updateJoinedPlayers,
  getOnlineCompe,
} from "@/api/online-compe-api";
import SearchModal from "@/components/onlinecompe/search-modal";
import PrintModal from "@/components/onlinecompe/print-modal";

interface RowErrors {
  [index: number]: boolean;
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#eee",
    border: "1px solid #ddd",
    textAlign: "center",
  },
}));

interface PairingProps {
  compeNo: number;
  compeName: string;
}

const Pairing = ({ compeNo, compeName }: PairingProps) => {
  // コンペ詳細・参加者リスト
  const [pairingList, setPairingList] = useState<JoinedPlayer[]>([]);
  // 印刷用のコンペ詳細・参加者リスト
  const [printPairingList, setprintPairingList] = useState<JoinedPlayer[]>([]);
  // モーダルの状態
  const [open, setOpen] = useState(false);
  // 印刷モーダルの状態
  const [openPrint, setOpenPrint] = useState(false);
  // 操作タイプ
  const [type, setType] = useState<"add" | "edit">("add");
  // 操作行のインデックス
  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);
  // ティーリスト
  const [teeList, seTeeList] = useState<TeeInfo[]>([]);
  // エラー
  const [errors, setErrors] = useState<RowErrors>({});
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  // HDCP日付
  const [hdcpDate, setHdcpDate] = useState<string>("");
  // 無効な種類
  const [disabledType, setDisabledType] = useState(-1);
  // 保存成功通知の状態
  const [openSaveSuccess, setOpenSaveSuccess] = useState(false);

  // ティー情報
  const getTeeList = async () => {
    try {
      const teeData = await getTeeInfos();
      seTeeList(teeData.data || []);
    } catch (error) { }
  };

  // コンペ詳細・参加者情報
  const getPairingList = async () => {
    try {
      const rowsData = await listJoinedPlayers(compeNo);
      setPairingList(rowsData.data);
      setprintPairingList(rowsData.data);
    } catch (error) { }
  };

  // コンペ情報
  const getCompesInfo = async () => {
    try {
      const compes = await getOnlineCompe(compeNo);
      const type = compes.compe_type_setting?.handy?.handicap?.type ?? -1;
      setDisabledType(type);
      const hdcp_date =
        compes.compe_type_setting?.handy?.handicap.hdcp_date ?? "";
      if (hdcp_date) {
        setHdcpDate(hdcp_date.slice(0, 10));
      }
    } catch (error) { }
  };

  useEffect(() => {
    getTeeList();
    getPairingList();
    getCompesInfo();
  }, []);

  // ヘッダー セルのインターフェース
  interface Column {
    id: keyof JoinedPlayer | string;
    label: string;
    minWidth?: number;
  }
  // ヘッダー 定義
  const columns: readonly Column[] = [
    { id: "player_name", label: "Name" },
    { id: "gender", label: "性別" },
    { id: "player_no", label: "Glid\u00a0No" },
    { id: "hdcp_index", label: "HDCP\u00a0Index" },
    { id: "playing_hdcp", label: "Playing\u00a0HDCP" },
    { id: "hdcp", label: "Private\u00a0HDCP" },
    { id: "tee_id", label: "使用ティー" },
    { id: "option", label: "操作" },
  ];

  // 参加者追加ボタン
  const handleAdd = () => {
    setType("add");
    setOpen(true);
  };

  // モーダルを閉じる
  const handleClose = () => {
    setOpen(false);
  };

  // 印刷ボタン
  const handlePrint = () => {
    setOpenPrint(true);
  };

  // 印刷モーダルを閉じる
  const handlePrintClose = () => {
    setOpenPrint(false);
  };

  // 印刷モーダルを印刷
  const onPrint = () => {
    setOpenPrint(false);
    (printPairingList || []).forEach((player) => {
      const matchingTee = teeList.find((tee) => tee.tee_id === player.tee_id);
      if (matchingTee) {
        player.tee_id = matchingTee.men_tee_name;
      } else {
        player.tee_id = "";
      }
    });

    const obj = {
      compeName,
      playerDateFrom: localStorage.getItem("playerDateFrom"),
      printType: "Pairing",
      pairingList: printPairingList,
    };
    localStorage.setItem("printData", JSON.stringify(obj));
    window.open(
      `/webapp/onlinecompe/list/detail/print?compeNo=${compeNo}&compeName=${compeName}`,
      "_blank"
    );
  };

  // ティーシート検索
  const onTeeSearch = (newRow: JoinedPlayer) => {
    setOpen(false);
    setPairingList([
      ...pairingList,
      {
        ...newRow,
        tee_id: newRow.tee_id ? newRow.tee_id : teeList[0].tee_id,
      },
    ]);
  };

  // Glid No検索
  const onGlidNoSearch = (rowInfo: PlayerInfo) => {
    if (editingRowIndex === null) return;
    const updatedData = {
      ...pairingList[editingRowIndex],
      glid_no: rowInfo.glid_no,
      hdcp_index: rowInfo.hdcp_index,
      gender: rowInfo.gender,
      birthday: rowInfo.birthday,
    };
    const newRows = [...pairingList];
    newRows[editingRowIndex] = updatedData;
    setOpen(false);
    setPairingList(newRows);
  };

  // glid_no列をクリックする
  const handleGlidNoClick = (index: number) => {
    setEditingRowIndex(index);
    setType("edit");
    setOpen(true);
  };

  // hdcp列、使用ティー列を变更する
  const handleInputChange = (
    index: number,
    field: keyof JoinedPlayer,
    value: string
  ) => {
    if (field === "hdcp") {
      setErrors((prevErrors) => {
        return {
          ...prevErrors,
          [index]: false,
        };
      });
    }
    const newRows = [...pairingList];
    newRows[index] = { ...newRows[index], [field]: value };
    setPairingList(newRows);
  };

  // 削除ボタン
  const handleRemoveRow = (index: number) => {
    const newRows = [...pairingList];
    newRows.splice(index, 1);
    setPairingList(newRows);
  };

  const converResponseToRequest = (
    players: JoinedPlayer[]
  ): OnlineCompeJoinedPlayersReq => ({
    joined_players: players,
  });

  // 全ての行を検証
  const validateAll = (): boolean => {
    let isValid = true;
    const newErrors: RowErrors = {};
    pairingList.forEach((row, index) => {
      if (row.hdcp === "" || row.hdcp === null) return;
      if (!/^\+?(0|[1-9]\d{0,2})(\.\d)?$/.test(row.hdcp)) {
        newErrors[index] = true;
        isValid = false;
        return;
      }
    });
    setErrors(newErrors);
    return isValid;
  };

  // 保存ボタン
  const handleSave = async () => {
    setErrorMsg(null);
    if (!validateAll()) {
      setErrorMsg("HDCPを正しく入力してください。");
      return;
    }
    const playersObj = converResponseToRequest(pairingList);
    try {
      const res = await updateJoinedPlayers(compeNo, playersObj);
      if (res) {
        setOpenSaveSuccess(true);
        getPairingList();
      } else {
        setErrorMsg("保存失敗しました。");
      }
    } catch (error) {
      setErrorMsg("保存失敗しました。");
    }
  };

  return (
    <Box>
      <div className="relative text-right -top-20">
        <Button
          variant="contained"
          color="primary"
          size="small"
          startIcon={<PrintIcon />}
          onClick={handlePrint}
        >
          印刷
        </Button>
      </div>
      <div className="flex justify-between items-center -mt-6 mb-2 w-3/4">
        <div className="flex items-center">
          <Typography className="font-bold">参加者</Typography>
          <small>({pairingList?.length}名)</small>
        </div>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAdd}
        >
          参加者追加
        </Button>
      </div>
      {errorMsg && (
        <Alert severity="error" className="w-3/4 mb-2">
          {errorMsg}
        </Alert>
      )}
      <TableContainer component={Paper} className="!w-3/4">
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <StyledTableCell key={column.id}>
                  {column.label}
                </StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {(pairingList || []).map((row, index) => (
              <TableRow key={`row-${index}`}>
                <TableCell align="left">{row.player_name}</TableCell>
                <TableCell align="center">
                  {row.gender === 1 ? "男性" : row.gender === 2 ? "女性" : ""}
                </TableCell>
                <TableCell align="center">
                  <TextField
                    variant="outlined"
                    autoComplete="off"
                    size="small"
                    value={row.glid_no}
                    disabled={disabledType !== 0}
                    onClick={() => handleGlidNoClick(index)}
                    InputProps={{
                      style: { width: "140px", height: "36px" },
                    }}
                  />
                </TableCell>
                <TableCell align="center">
                  {row.hdcp_index
                    ? row.hdcp_index
                    : disabledType === 0 && (
                      <span style={{ color: "#FE3535", fontSize: 12 }}>
                        HDCPを設定してください
                      </span>
                    )}
                </TableCell>
                <TableCell align="center">
                  {row.playing_hdcp
                    ? row.playing_hdcp
                    : ""}
                </TableCell>
                <TableCell align="center">
                  <TextField
                    type="text"
                    variant="outlined"
                    autoComplete="off"
                    size="small"
                    value={row.hdcp}
                    disabled={disabledType !== 1}
                    onChange={(e) =>
                      handleInputChange(index, "hdcp", e.target.value)
                    }
                    inputProps={{
                      inputMode: "numeric",
                      pattern: "[0-9]*",
                    }}
                    InputProps={{
                      style: { width: "140px", height: "36px" },
                    }}
                    error={errors[index]}
                  />
                </TableCell>
                <TableCell align="left">
                  <TextField
                    select
                    variant="outlined"
                    size="small"
                    value={row.tee_id}
                    onChange={(e) =>
                      handleInputChange(index, "tee_id", e.target.value)
                    }
                    InputProps={{
                      style: { width: "140px", height: "36px" },
                    }}
                  >
                    {teeList.map((i) => (
                      <MenuItem key={i.tee_id} value={i.tee_id}>
                        {i.men_tee_name}
                      </MenuItem>
                    ))}
                  </TextField>
                </TableCell>
                <TableCell>
                  <Button
                    color="error"
                    onClick={() => handleRemoveRow(index)}
                    startIcon={<DeleteIcon />}
                    size="small"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <div className="flex justify-center mt-4 w-3/5">
        <Button type="submit" variant="contained" onClick={handleSave}>
          保　存
        </Button>
      </div>
      <Snackbar
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        open={openSaveSuccess}
        autoHideDuration={3000}
        onClose={() => {
          setOpenSaveSuccess(false);
        }}
      >
        <Alert severity="success" sx={{ width: 500 }}>
          保存成功しました。
        </Alert>
      </Snackbar>
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={open}
        onClose={handleClose}
      >
        <SearchModal
          onClose={handleClose}
          onTeeSearch={onTeeSearch}
          onGlidNoSearch={onGlidNoSearch}
          compeNo={compeNo}
          hdcpDate={hdcpDate}
          playeNoList={(pairingList || []).map((i) => i.player_no)}
          type={type}
        />
      </Modal>
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={openPrint}
        onClose={handlePrintClose}
      >
        <PrintModal onClose={handlePrintClose} onPrint={onPrint} />
      </Modal>
    </Box>
  );
};

export default Pairing;
