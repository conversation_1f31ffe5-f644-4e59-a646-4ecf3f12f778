import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Divider,
  IconButton,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { shareByEmail } from "@/api/online-compe-api";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "40%",
  bgcolor: "background.paper",
  borderRadius: "10px",
  boxShadow: 24,
  p: 4,
};

interface KeyModalProps {
  onClose: () => void;
  onSettingSave: (value: string) => void;
  settingType: string;
  compeNo: number;
  shareKey: string;
  selectTime: string | null;
}

const SettingModal = ({
  onClose,
  onSettingSave,
  settingType,
  compeNo,
  shareKey,
  selectTime,
}: KeyModalProps) => {
  // メールエラー
  const [sharedKeyError, setSharedKeyError] = useState(false);
  // メールエラーメッセージ
  const [sharedKeyErrorMsg, setSharedKeyErrorMsg] = useState("");
  // メール
  const [emailValue, setEmailValue] = useState<string>("");
  // 変更するページ時間
  const [timeValue, setTimeValue] = React.useState(selectTime ?? "0");
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState("");

  // 保存ボタン
  const handleClick = async () => {
    if (settingType === "email") {
      if (
        !emailValue ||
        !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(emailValue)
      ) {
        setSharedKeyError(true);
        setSharedKeyErrorMsg("メールアドレスの入力形式に誤りがあります。");
      } else {
        setSharedKeyError(false);
        setSharedKeyErrorMsg(" ");
        try {
          const res = await shareByEmail(emailValue, compeNo, shareKey);
          if (res) {
            onClose();
          } else {
            setErrorMsg("送信が失敗しました。");
          }
        } catch (error) {
          setErrorMsg("送信が失敗しました。");
        }
      }
    } else if (settingType === "pagetime") {
      onSettingSave(timeValue);
    }
  };

  return (
    <Box sx={style}>
      <div className="flex items-center justify-between">
        <Typography noWrap component="div" variant="h1">
          {settingType === "email" ? "リンクを共有" : "設定"}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </div>
      <Divider className="my-2" />

      {errorMsg && (
        <Alert severity="error" className="w-full my-2">
          {errorMsg}
        </Alert>
      )}

      <div className="py-10">
        {settingType === "email" ? (
          <FormControl className="!flex !items-baseline !justify-start !flex-row !gap-8">
            <FormLabel sx={{ color: "#000" }}>メールアドレス</FormLabel>
            <TextField
              error={sharedKeyError}
              helperText={sharedKeyErrorMsg}
              value={emailValue}
              placeholder="<EMAIL>"
              variant="outlined"
              size="small"
              name="email"
              color={sharedKeyError ? "error" : "primary"}
              InputProps={{
                style: { width: "350px", height: "36px" },
              }}
              onChange={(e) => {
                setEmailValue(e.target.value);
              }}
            />
          </FormControl>
        ) : (
          <FormControl className="!flex !items-center !flex-row !gap-6">
            <FormLabel sx={{ color: "#000" }}>自動ページ送り</FormLabel>
            <RadioGroup
              row
              name="time"
              value={timeValue}
              onChange={(e) => {
                setTimeValue(e.target.value);
              }}
            >
              <FormControlLabel value="0" control={<Radio />} label="無効" />
              <FormControlLabel
                value="20000"
                control={<Radio />}
                label="20秒"
              />
              <FormControlLabel
                value="40000"
                control={<Radio />}
                label="40秒"
              />
              <FormControlLabel
                value="60000"
                control={<Radio />}
                label="60秒"
              />
              <FormControlLabel
                value="80000"
                control={<Radio />}
                label="80秒"
              />
              <FormControlLabel
                value="100000"
                control={<Radio />}
                label="100秒"
              />
            </RadioGroup>
          </FormControl>
        )}
      </div>
      <Divider className="!mb-4" />
      <div className="flex justify-end gap-4">
        <Button variant="outlined" onClick={onClose}>
          キャンセル
        </Button>
        <Button type="submit" variant="contained" onClick={handleClick}>
          {settingType === "email" ? "送　信" : "保　存"}
        </Button>
      </div>
    </Box>
  );
};

export default SettingModal;
