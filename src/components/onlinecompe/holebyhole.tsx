"use client";
import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>pography,
  Button,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  MenuItem,
  Alert,
  Modal,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { tableCellClasses } from "@mui/material/TableCell";
import PrintIcon from "@mui/icons-material/Print";
import { leaderboardRanking, rankingType } from "@/api/online-compe-api";
import { ScoreCourse } from "@/models/compe/resp/ranking-resp";
import ScoreFormat from "@/components/onlinecompe/score-format";
import PrintModal from "@/components/onlinecompe/print-modal";

const methods = [
  { label: "記号", value: 1 },
  { label: "数字", value: 2 },
];

interface CourseData {
  course_name: string;
  hole: string;
  score_gross: number;
  score_net: number;
  hdcp_index: string;
  course_hdcp: string;
  start_hole: string;
  total_holes: number;
  hole_scores: {
    hole_number: string;
    course_index: string;
    hole_index: string;
    score: string;
    stroke: string;
    used_hdcp: string;
    start: string;
  }[];
}

interface CombinedPlayerData {
  player_no: string;
  player_name: string;
  pos: string;
  net: string;
  is_tied: number;
  score: string;
  today: string;
  courseData: {
    [key: string]: CourseData;
  };
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#eee",
    border: "1px solid #ddd",
    padding: 0,
    textAlign: "center",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
    border: "1px solid #ddd",
    textAlign: "center",
    padding: "0",
  },
}));

const renderField = (field: string, stroke: string) => {
  console.log("renderField", field, stroke);
  if (stroke === "*") return <span style={{ color: "black" }}>*</span>;
  if (field === "-") return null;
  else if (parseInt(field) <= -3) return <span style={{ color: "red" }}>★</span>;
  else if (field === "-2") return <span style={{ color: "red" }}>◎</span>;
  else if (field === "-1") return <span style={{ color: "red" }}>○</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>-</span>;
  else if (field === "1") return <span style={{ color: "blue" }}>△</span>;
  else if (field === "2") return <span style={{ color: "blue" }}>□</span>;
  else if (field === "3") return <span style={{ color: "blue" }}>■</span>;
  else if (parseInt(field) > 3)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

const renderField2 = (field: string, stroke: string) => {
  console.log("renderField2", field, stroke);
  if (field === "-") return null;
  else if (parseInt(field) < 0)
    return <span style={{ color: "red" }}>{field}</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>{field}</span>;
  else if (parseInt(field) > 0)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

interface HolebyholeProps {
  compeNo: number;
  compeName: string;
}

const Holebyhole = ({ compeNo, compeName }: HolebyholeProps) => {
  const style = {
    marginTop: -10,
  };
  // プレイヤーマップ
  const [playerMap, setPlayerMap] = useState<Map<string, CombinedPlayerData>>(
    new Map()
  );
  const [courses, setCourses] = useState<ScoreCourse[]>([]);
  const [maxHoles, setMaxHoles] = useState<number>(0);
  // 印刷モーダルの状態
  const [openPrint, setOpenPrint] = useState(false);
  // 集計方法リスト
  const [rankingTypeList, setRankingTypeList] = useState<string[]>([]);
  // 集計方法
  const [rankinType, setRankinType] = useState("");
  // スコア
  const [scoreRenderType, setScoreRenderType] = useState(1);
  // エラー
  const [errorMsg, setErrorMsg] = useState("");

  // 集計方法情報
  const getRankingType = async () => {
    try {
      await rankingType(compeNo.toString()).then(async (i) => {
        const types = i.data.ranking_type;
        if (types?.length > 0) {
          console.log("types", types);
          // remove types which contains peoria from types
          const filteredTypes = types.filter((type) => !type.includes("peoria"));
          setRankingTypeList(filteredTypes);
          setRankinType(filteredTypes[0]);
        } else {
          setErrorMsg("該当データはありません。");
        }
      });
    } catch (error) {
      setErrorMsg("該当データはありません。");
    }
  };

  // 順位情報
  const getLeaderboardRanking = async (type: string) => {
    try {
      const { data } = await leaderboardRanking(compeNo.toString(), type);
      if (data && data.rankings) {
        const newPlayerMap = new Map<string, CombinedPlayerData>();
        data.rankings.forEach((ranking) => {
          data.courses.forEach((course) => {
            if (!newPlayerMap.has(ranking.player_no)) {
              newPlayerMap.set(ranking.player_no, {
                player_no: ranking.player_no,
                player_name: ranking.player_name,
                pos: ranking.pos,
                net: ranking.par_net > 0 ? `+${ranking.par_net}` : ranking.par_net.toString(),
                is_tied: ranking.is_tied,
                score: ranking.par_gross > 0 ? `+${ranking.par_gross}` : ranking.par_gross.toString(),
                today: ranking.score_gross.toString(),
                courseData: {},
              });
            }

            const playerData = newPlayerMap.get(ranking.player_no)!;

            if (course) {
              const aMap = new Map<string, string>();
              (course.holes || []).forEach((item) => {
                aMap.set(item.hole_index, item.used_hdcp);
              });
              const combined = (ranking.hole_score.filter(
                (item) => item.course_index === course.course_index
              ) || []).map((item) => {
                const used_hdcp = aMap.get(item.hole_index) || "0";
                let start = "";
                if (
                  item.score === "-" ||
                  (item.score === "0" && item.stroke === "0")
                ) {
                  start = "";
                } else {
                  // しょう
                  const quotient = Math.floor(Number(ranking.course_hdcp)) >= 0 ? Math.floor(Number(ranking.course_hdcp) / 18) : 0;
                  // よすう
                  const remainder = Number(ranking.course_hdcp) % 18;
                  // ★個数
                  start = "★".repeat(
                    quotient + (remainder >= Number(used_hdcp) ? 1 : 0)
                  );
                }
                return { ...item, used_hdcp, start };
              });

              playerData.courseData[course.course_name] = {
                course_name: course.course_name,
                hole: ranking.hole,
                score_gross: ranking.score_gross,
                score_net: ranking.score_net,
                hdcp_index: ranking.hdcp_index,
                course_hdcp: ranking.course_hdcp,
                start_hole: course.start_hole,
                total_holes: course.holes.length,
                hole_scores: combined, // remove course_index != 
              };
            }
          });
        });
        setPlayerMap(newPlayerMap);
        setCourses([...new Set(data.courses)]);
        setMaxHoles(Math.max(...data.courses.map((c) => c.holes.length)));
      } else {
        setErrorMsg("該当データはありません。");
      }
    } catch (error) {
      console.error(error);
      setErrorMsg("該当データはありません。");
    }
  };

  useEffect(() => {
    getRankingType();
  }, []);

  useEffect(() => {
    if (rankinType) {
      getLeaderboardRanking(rankinType);
    }
  }, [rankinType]);

  // ヘッダー セルのインターフェース
  interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: "center";
    format?: (value: number) => string;
  }

  // ヘッダー 定義
  const columns: readonly Column[] = [
    { id: "pos", label: "POS", align: "center", minWidth: 50 },
    { id: "name", label: "NAME", align: "center", minWidth: 170 },
    { id: "score", label: rankinType === "gross" ? "GROSS" : "NET", align: "center", minWidth: 70 },
  ];

  // 別端末で表示
  const handleURL = () => {
    window.open(`/webapp/project?compeNo=${compeNo}&compeName=${compeName}`, "_blank");
  };

  // 印刷ボタン
  const handlePrint = () => {
    setOpenPrint(true);
  };

  // 印刷モーダルを閉じる
  const handlePrintClose = () => {
    setOpenPrint(false);
  };

  // 印刷モーダルを印刷
  const onPrint = () => {
    setOpenPrint(false);
    const obj = {
      compeName,
      playerDateFrom: localStorage.getItem("playerDateFrom"),
      printType: "Holebyhole",
      playerMap: [...playerMap.values()],
      courses,
      maxHoles,
      scoreRenderType,
      rankinType,
    };
    localStorage.setItem("printData", JSON.stringify(obj));
    window.open(
      `/webapp/onlinecompe/list/detail/print?compeNo=${compeNo}&compeName=${compeName}`,
      "_blank"
    );
  };

  return (
    <Box sx={style} id="printableArea">
      <div className="flex justify-end items-center">
        <Button
          variant="contained"
          color="primary"
          size="small"
          disabled={!!errorMsg}
          startIcon={<PrintIcon />}
          onClick={handlePrint}
        >
          印刷
        </Button>
        <Button
          variant="contained"
          color="info"
          size="small"
          disabled={!!errorMsg}
          startIcon={<AddIcon />}
          className="!ml-4"
          onClick={handleURL}
        >
          別端末で表示
        </Button>
      </div>
      {errorMsg && (
        <Alert severity="error" className="w-full mt-20">
          {errorMsg}
        </Alert>
      )}
      {!errorMsg && (
        <React.Fragment>
          <div className="flex justify-end items-center gap-4 my-2">
            <Typography>集計方法切替</Typography>
            <TextField
              select
              variant="outlined"
              size="small"
              InputProps={{
                style: { width: "150px", height: "36px" },
              }}
              value={rankinType}
              onChange={(event) => setRankinType(event.target.value)}
            >
              {rankingTypeList.map((i) => (
                <MenuItem key={i} value={i}>
                  {
                    i === "gross"
                      ? "グロス" :
                      i === "handy"
                        ? "ネット"
                        : i === "peoria"
                          ? "ぺリア"
                          : i === "new-peoria"
                            ? "新ペリア"
                            : i === "new-new-peoria"
                              ? "新新ペリア"
                              : i}
                </MenuItem>
              ))}
            </TextField>
          </div>
          <div className="flex justify-between items-center mb-2">
            <Typography variant="h4">★...ハンディキャップ適用</Typography>
            <div className="gap-4 flex items-center">
              <Typography>スコア表示切替</Typography>
              <TextField
                select
                variant="outlined"
                size="small"
                name="caddie"
                value={scoreRenderType}
                onChange={(event) => setScoreRenderType(Number(event.target.value))}
                InputProps={{
                  style: { width: "150px", height: "36px" },
                }}
              >
                {methods.map((i) => (
                  <MenuItem key={i.value} value={i.value}>
                    {i.label}
                  </MenuItem>
                ))}
              </TextField>
            </div>
          </div>
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <StyledTableCell
                      key={column.id}
                      align={column.align}
                      sx={{ minWidth: column.minWidth }}
                    >
                      {column.label}
                    </StyledTableCell>
                  ))}
                  {courses.map((course) => (
                    <React.Fragment key={course.course_index}>
                      {course.holes.map((hole) => (
                        <StyledTableCell
                          key={`hole-${hole.hole_index}`}
                          sx={{ minWidth: 38 }}
                        >
                          <p style={{ borderBottom: "1px solid #ddd" }}>
                            {Number(hole.hole_index) + Number(course.start_hole)}
                          </p>
                          <p>{hole.used_par}</p>
                        </StyledTableCell>
                      ))}
                      <StyledTableCell sx={{ minWidth: 70 }}>
                        <p>{course.course_name}</p>
                        <p>
                          {course.holes.reduce(
                            (sum, hole) => sum + Number(hole.used_par),
                            0
                          )}
                        </p>
                      </StyledTableCell>
                    </React.Fragment>
                  ))}
                  <StyledTableCell sx={{ minWidth: 70 }}>
                    <p>TODAY</p>
                    <p>
                      {courses
                        .flatMap((course) => course.holes)
                        .reduce(
                          (sum, hole) => sum + parseInt(hole.used_par),
                          0
                        )}
                    </p>
                  </StyledTableCell>
                </TableRow>
              </TableHead>

              <TableBody>
                {[...playerMap.values()].map((player, index) => (
                  <TableRow
                    key={index}
                    sx={{
                      height: "35px",
                    }}
                  >
                    <StyledTableCell>
                      {player.pos
                        ? `${player.pos}`
                        : "-"}
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{
                        textAlign: "left !important",
                        padding: "0 6px !important",
                      }}
                    >
                      {player.player_name}
                    </StyledTableCell>
                    <StyledTableCell>{rankinType === "gross" ? player.score : player.net}</StyledTableCell>

                    {courses.map((course) => {
                      const courseData = player.courseData[course.course_name];
                      return (
                        <React.Fragment key={course.course_index}>
                          {Array.from({ length: maxHoles }, (_, i) => {
                            const holeScore = courseData?.hole_scores?.find(
                              (hs) => parseInt(hs.hole_index) === i
                            );
                            return (
                              <StyledTableCell
                                key={i}
                                sx={{ position: "relative" }}
                              >
                                <p
                                  style={{
                                    fontSize: 12,
                                    position: "absolute",
                                    bottom: 0,
                                    left: 0,
                                    height: "14px",
                                    overflow: "hidden",
                                  }}
                                >
                                  {holeScore?.start}
                                </p>
                                <p>
                                  {holeScore
                                    ? scoreRenderType === 1
                                      ? renderField(
                                        holeScore.score,
                                        holeScore.stroke
                                      )
                                      : renderField2(
                                        holeScore.score,
                                        holeScore.stroke
                                      )
                                    : ""}
                                </p>
                              </StyledTableCell>
                            );
                          })}
                          <StyledTableCell>
                            {courseData?.hole_scores.reduce(
                              (sum, hole) => sum + Number(hole.score),
                              0
                            )}
                          </StyledTableCell>
                        </React.Fragment>
                      );
                    })}
                    <StyledTableCell>{player.today}</StyledTableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {scoreRenderType === 1 && <ScoreFormat />}
        </React.Fragment>
      )}
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={openPrint}
        onClose={handlePrintClose}
      >
        <PrintModal onClose={handlePrintClose} onPrint={onPrint} />
      </Modal>
    </Box>
  );
};

export default Holebyhole;
