import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    TextField,
    Button,
    Box,
    Typography,
    Grid
} from '@mui/material';

interface PlayerInputHdcpDialogProps {
    open: boolean;
    onClose: () => void;
    onApply: (hdcp: string) => void;
    playerName: string;
    playerHdcp: string;
}

const PlayerInputHdcpDialog: React.FC<PlayerInputHdcpDialogProps> = ({
    open,
    onClose,
    onApply,
    playerName,
    playerHdcp
}) => {
    const [hdcp, setHdcp] = useState(playerHdcp);
    useEffect(() => {
        if (open && playerHdcp !== '0.0') {
            setHdcp(playerHdcp);
        }
    }, [open, playerHdcp]);
    const record = () => {
        if (hdcp === '') {
            return;
        }
        onApply(hdcp);
        dialogClose();
    }
    const dialogClose = () => {
        setHdcp('');
        onClose();
    }
    return (
        <Dialog
            open={open}
            onClose={dialogClose}
            PaperProps={{
                sx: {
                    width: '550px',
                    borderRadius: 2,
                    
                }
            }}
        >
            <DialogTitle sx={{ pb: 1, fontSize: '18px' }}>
                HDCP手入力
            </DialogTitle>
            <Box sx={{
                height: '1px',
                width: 'auto',
                background: '#707070',
                marginLeft: '23px',
                marginRight: '23px',
            }}
            ></Box>
            <DialogContent>
                <Box sx={{ display: 'flex', flexDirection: 'column', }}>
                    <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center',}}>
                        <Typography component="label" 
                            sx={{ display: 'block', 
                            fontSize: '22px', 
                            fontWeight: 'bold',
                            width: '120px',
                            textAlign: 'right'}}>
                            氏名：
                        </Typography>
                        <Typography component="label" 
                            sx={{ 
                            fontSize: '18px', 
                            width: '120px',
                            textAlign: 'left'}}>
                        {playerName}
                        </Typography>
                    </Box>
                    <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center', mt: '30px'}}>
                        <Typography component="label" 
                            sx={{ display: 'block', 
                            fontSize: '22px', 
                            fontWeight: 'bold',
                            width: '120px',
                            textAlign: 'right'}}>
                            HDCP：
                        </Typography>
                        <TextField
                            style={{ width: '215px', fontSize: '14px', height: "30px" }}
                            InputProps={{
                                style: { height: "30px" },
                            }}
                            value={hdcp}
                            onChange={(e) => setHdcp(e.target.value)}
                            size="small"
                        />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: '46px', mt: '44px' }}>
                        <Button
                            variant="outlined"
                            onClick={record}
                            sx={{ width: '165px',
                                height: '40px',
                                fontSize: '20px',
                                border: '2px solid #324F85'
                             }}
                        >
                            登録
                        </Button>
                    </Box>
                </Box>
            </DialogContent>
        </Dialog>
    );
};

export default PlayerInputHdcpDialog;
