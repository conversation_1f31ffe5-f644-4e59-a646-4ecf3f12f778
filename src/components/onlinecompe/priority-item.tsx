import React, { useState, useRef, useEffect } from "react";
import { TextField, Box, Typography, MenuItem } from "@mui/material";
import { useDrag, useDrop } from "react-dnd";

const ItemTypes = {
    Priority: 'priority',
};

const PriorityItem = ({ id, item, index, itemStyle, options, moveItem, onOptionChange }: { id: string, item: any, index: number, itemStyle: {}, options: string[], moveItem: (fromIndex: number, toIndex: number) => void, onOptionChange: (id: string, value: string) => void; }) => {
    const ref = useRef(null);
    const [{ isDragging }, drag] = useDrag({
        type: ItemTypes.Priority,
        item: { id, index },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const [, drop] = useDrop({
        accept: ItemTypes.Priority,
        hover: (draggedItem: any) => {
            if (draggedItem.index === index) return;
            moveItem(draggedItem.index, index);
            draggedItem.index = index;
        },
    });
    drag(drop(ref));
    return (
        <Box ref={ref}
            sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mb: 2,
                opacity: isDragging ? 0.5 : 1,
                cursor: 'move',
                direction: 'row'
            }}
        >
            <Typography sx={itemStyle}>
                {index}.
            </Typography>
            <img src={"/webapp/images/compe_ic_drop.png"} style={{ height: '25px', width: '14px' }}></img>
            <TextField
                select
                size="small"
                name="ranking"
                variant="outlined"
                style={{ width: '250px', fontSize: '14px', height: "30px" }}
                InputProps={{
                    style: { height: "30px" },
                }}
                value={item.value}
                onChange={(e) => onOptionChange(id, e.target.value)}
            >
                {options.map((option, index) => (
                    <MenuItem key={index} value={option} style={{ fontSize: '14px' }}>
                        {option}
                    </MenuItem>
                ))}
            </TextField>
        </Box>
    );
};

export default PriorityItem;
