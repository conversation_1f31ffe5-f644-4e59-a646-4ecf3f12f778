import React, { useEffect } from "react";
import {
  Pagination,
  PaginationItem,
  Typography,
  TextField,
  MenuItem,
} from "@mui/material";
import { styled } from "@mui/system";
import { useTheme } from "@mui/material/styles";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";

const FixedPaginationItem = styled(PaginationItem)(({ theme }) => ({
  width: "38px",
  height: "38px",
  margin: "-0.5px",
  borderRadius: "4px",
  border: "1px solid",
  borderColor: theme.palette.grey[400],
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: theme.palette.primary.main,
  fontSize: "16px",
  "&.Mui-selected": {
    backgroundColor: theme.palette.primary.main,
    color: "white",
  },
}));

interface PaginationComponentProps {
  count: number;
  page: number;
  onChange: (event: any, value: number) => void;
  selectedCount: number;
  onCountChange: (event: any) => void;
  counts: number[];
}

const PaginationComponent: React.FC<PaginationComponentProps> = ({
  count,
  page,
  onChange,
  selectedCount,
  onCountChange,
  counts,
}) => {
  const theme = useTheme();

  useEffect(() => {
    if (page !== 1) {
      onChange({ target: { value: 1 } }, 1);
    }
  }, [count]);

  return (
    <div className="flex justify-between items-center mt-4">
      <Pagination
        color="primary"
        variant="outlined"
        shape="rounded"
        count={count}
        siblingCount={2}
        boundaryCount={2}
        showFirstButton
        showLastButton
        onChange={onChange}
        renderItem={(item) => (
          <FixedPaginationItem
            {...item}
            slots={{
              previous: () => <KeyboardArrowLeftIcon />,
              next: () => <KeyboardArrowRightIcon />,
              first: () => <KeyboardDoubleArrowLeftIcon />,
              last: () => <KeyboardDoubleArrowRightIcon />,
            }}
          />
        )}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
        }}
      />
      <div className="flex flex-wrap items-center space-x-4">
        <Typography noWrap component="span" variant="h3" color="primary">
          表示件数:
        </Typography>
        <TextField
          select
          variant="outlined"
          size="small"
          name="selectedCount"
          value={selectedCount}
          onChange={onCountChange}
          InputProps={{
            style: { height: "36px" },
          }}
          sx={{
            height: "36px",
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: theme.palette.primary.main,
              },
              "&:hover fieldset": {
                borderColor: theme.palette.primary.dark,
              },
              "&.Mui-focused fieldset": {
                borderColor: theme.palette.primary.main,
              },
            },
            "& .MuiInputBase-input": {
              color: theme.palette.primary.main,
            },
            "& .MuiSelect-icon": {
              color: theme.palette.primary.main,
            },
          }}
        >
          {counts.map((count) => (
            <MenuItem key={count} value={count}>
              {count}
            </MenuItem>
          ))}
        </TextField>
      </div>
    </div>
  );
};

export default PaginationComponent;
