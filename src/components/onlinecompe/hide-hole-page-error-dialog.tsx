import {Box, Typography, Button, Portal} from "@mui/material";

export interface HideHolePageErrorDialogProps {
    openErrorDialog: boolean;
    closeErrorDialog: () => void;
}

const HideHolePageErrorDialog: React.FC<HideHolePageErrorDialogProps> = ({
    openErrorDialog,
    closeErrorDialog
}) => {
    return ( 
        <Portal>
        {openErrorDialog && (
            <Box sx={{
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'white',
                padding: 3,
                boxShadow: 5,
                borderRadius: 2,
                zIndex: 1401,
              }}>
            <Typography>隠しホール数を確認してください。</Typography>
            <Button onClick={closeErrorDialog}>OK</Button>
            </Box>
          )}
         </Portal>
    );
}

export default HideHolePageErrorDialog;
