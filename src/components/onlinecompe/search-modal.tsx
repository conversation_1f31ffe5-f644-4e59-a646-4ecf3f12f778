"use client";
import React, { useRef, useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Divider,
  IconButton,
  TextField,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  ToggleButtonGroup,
  ToggleButton,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import CloseIcon from "@mui/icons-material/Close";
import { TeePlayer } from "@/models/tee/resp/tee-player";
import { JoinedPlayer } from "@/models/compe/resp/online-compe-player";
import { PlayerInfo } from "@/models/compe/resp/player-resp";
import {
  searchPlayersFromTeeSheet,
  searchPlayerInfo,
  getOnlineCompe,
} from "@/api/online-compe-api";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "35%",
  minWidth: 540,
  bgcolor: "background.paper",
  borderRadius: "10px",
  boxShadow: 24,
  p: 4,
};

interface KeyModalProps {
  onClose: () => void;
  onTeeSearch: (row: JoinedPlayer) => void;
  onGlidNoSearch: (row: PlayerInfo) => void;
  type: string;
  compeNo: number;
  hdcpDate: string;
  playeNoList?: number[];
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#eee",
    textAlign: "center",
  },
}));

const KeyModal = ({
  onClose,
  onTeeSearch,
  onGlidNoSearch,
  type,
  compeNo,
  hdcpDate,
  playeNoList,
}: KeyModalProps) => {
  // // GlidNoエラー
  // const [glidNoError, setGlidNoError] = useState(false);
  // // 氏名エラー
  // const [nameError, setNameError] = useState(false);
  // // 生年月日エラー
  // const [playerBirthError, setPlayerBirthError] = useState(false);
  // 年-月-日
  const [fields, setFields] = useState(["", "", ""]);
  // Glid情報
  const [glidInfo, setGlidInfo] = useState<PlayerInfo[]>([]);
  // ティー情報
  const [teeInfo, setTeeInfo] = useState<TeePlayer[]>([]);
  // 選択方法：Glid No、名前と生年月日
  const [radio, setRadio] = useState<string>("1");
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState("");

  // 生年月日変更
  const handleInputChange = (index: number, event: any) => {
    setErrorMsg("");
    let newFields = [...fields];
    newFields[index] = event.target.value
      .replace(/[^\d]/g, "")
      .slice(0, index === 0 ? 4 : 2);

    if (newFields[0].length === 4) {
      setFields(newFields);
      if (newFields[1].length === 2) {
        let focusNextField = setTimeout(() => {
          document.getElementById("3")?.focus();
          clearTimeout(focusNextField);
        }, 0);
      } else {
        let focusNextField = setTimeout(() => {
          document.getElementById("2")?.focus();
          clearTimeout(focusNextField);
        }, 0);
      }
    } else {
      setFields(newFields);
    }
  };

  const handleRadioClick = (
    _: React.MouseEvent<HTMLElement, MouseEvent>,
    newRadio: string
  ) => {
    setTeeInfo([]);
    setGlidInfo([]);
    setErrorMsg("");
    setFields(["", "", ""]);
    setRadio(newRadio);
  };

  // 検索ボタン
  const handleClick = async () => {
    setTeeInfo([]);
    setGlidInfo([]);
    setErrorMsg("");
    let tmpList = [];
    if (type === "edit" && radio === "1") {
      // GlidNo
      const no = document.getElementById("glidNo") as HTMLInputElement;
      if (!/^[0-9]+$/.test(no.value)) {
        // setGlidNoError(true);
        tmpList.push(true);
      }

      if (!tmpList.some((value) => value)) {
        try {
          const { data } = await searchPlayerInfo(
            "null", //TODO
            "null", //TODO
            no.value,
            hdcpDate,
            2
          );
          if (data.length > 0) {
            setGlidInfo(data);
          } else {
            setErrorMsg("検索している情報が存在しません。");
          }
        } catch (error) {
          setErrorMsg("検索している情報が存在しません。");
        }
      } else {
        setErrorMsg("検索条件を入力してください。");
      }
    } else if ((type === "edit" && radio === "2") || type === "add") {
      // 氏名
      const name = document.getElementById("name") as HTMLInputElement;
      if (!name.value) {
        // setNameError(true);
        tmpList.push(true);
      }
      // 年
      if (!/^\d{4}$/.test(fields[0])) {
        tmpList.push(true);
      }

      // 月
      if (!/^(0?[1-9]|1[0-2])$/.test(fields[1])) {
        tmpList.push(true);
      }

      // 日
      if (!/^(0?[1-9]|[12][0-9]|3[01])$/.test(fields[2])) {
        tmpList.push(true);
      }

      if (!tmpList.some((value) => value)) {
        let newFields = [...fields];
        if (newFields[1].length < 2) {
          newFields[1] = "0" + newFields[1];
        }
        if (newFields[2].length < 2) {
          newFields[2] = "0" + newFields[2];
        }
        if (type === "add") {
          try {
            const { data } = await searchPlayersFromTeeSheet(
              name.value,
              newFields.join("-"),
              null
            );
            if (data.length > 0) {
              setTeeInfo(data);
            } else {
              setErrorMsg("検索している情報が存在しません。");
            }
          } catch (error) {
            setErrorMsg("検索している情報が存在しません。");
          }
        } else {
          try {
            const { data } = await searchPlayerInfo(
              name.value,
              newFields.join("-"),
              null,
              hdcpDate,
              3
            );
            if (data.length > 0) {
              setGlidInfo(data);
            } else {
              setErrorMsg("検索している情報が存在しません。");
            }
          } catch (error) {
            setErrorMsg("検索している情報が存在しません。");
          }
        }
      } else {
        setErrorMsg("検索条件を入力してください。");
      }
    }
  };

  return (
    <Box sx={style}>
      <div className="flex items-center justify-between">
        <Typography noWrap component="div" variant="h1">
          {type === "add" ? "ティーシート検索" : "Glid No検索"}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </div>
      <Divider className="!my-2" />

      {type === "edit" && (
        <ToggleButtonGroup
          color="info"
          value={radio}
          exclusive
          onChange={handleRadioClick}
          className="gap-4 my-2"
          size="small"
          sx={{
            "& .MuiToggleButton-root": {
              color: "#304A89 !important",
              border: "1px solid #304A89 !important",
              borderRadius: "4px !important",
            },
            "& .Mui-selected": {
              color: "#fff !important",
              backgroundColor: "#304A89 !important",
            },
            "& .MuiToggleButton-root:hover": {
              color: "#fff !important",
              backgroundColor: "#304A89 !important",
            },
          }}
        >
          <ToggleButton value="1">Glid No</ToggleButton>
          <ToggleButton value="2">名前と年月日</ToggleButton>
        </ToggleButtonGroup>
      )}

      {errorMsg && (
        <Alert
          severity="error"
          // onClose={() => {
          //   setErrorMsg("");
          // }}
          className="w-full my-2"
        >
          {errorMsg}
        </Alert>
      )}
      <div>
        {type === "edit" && (
          <>
            {radio === "1" && (
              <div className="flex items-baseline flex-row gap-4 my-4 ">
                <Typography
                  component="div"
                  sx={{ width: 80, textAlign: "right" }}
                >
                  Glid No：
                </Typography>
                <TextField
                  id="glidNo"
                  variant="outlined"
                  autoComplete="off"
                  size="small"
                  // error={glidNoError}
                  onChange={(_) => setErrorMsg("")}
                  InputProps={{
                    style: { width: "250px", height: "36px" },
                  }}
                />
              </div>
            )}
          </>
        )}

        {(radio === "2" || type === "add") && (
          <React.Fragment>
            <div className="flex items-baseline flex-row gap-4 my-4 ">
              <Typography sx={{ width: 80, textAlign: "right" }}>
                氏名：
              </Typography>
              <TextField
                id="name"
                variant="outlined"
                autoComplete="off"
                size="small"
                // error={nameError}
                onChange={(_) => setErrorMsg("")}
                InputProps={{
                  style: { width: "250px", height: "36px" },
                }}
              />
            </div>
            <div className="flex items-baseline flex-row gap-4 mb-4">
              <Typography sx={{ width: 80, textAlign: "right" }}>
                生年月日：
              </Typography>
              <TextField
                id="1"
                variant="outlined"
                autoComplete="off"
                size="small"
                // error={playerBirthError}
                value={fields[0]}
                inputProps={{ maxLength: 4 }}
                onChange={(e) => handleInputChange(0, e)}
                InputProps={{
                  style: { width: "80px", height: "36px" },
                }}
              />
              <Typography>年</Typography>
              <TextField
                id="2"
                variant="outlined"
                autoComplete="off"
                size="small"
                // error={playerBirthError}
                value={fields[1]}
                inputProps={{ maxLength: 2 }}
                onChange={(e) => handleInputChange(1, e)}
                InputProps={{
                  style: { width: "80px", height: "36px" },
                }}
              />
              <Typography>月</Typography>
              <TextField
                id="3"
                variant="outlined"
                autoComplete="off"
                size="small"
                // error={playerBirthError}
                value={fields[2]}
                inputProps={{ maxLength: 2 }}
                onChange={(e) => handleInputChange(2, e)}
                InputProps={{
                  style: { width: "80px", height: "36px" },
                }}
              />
              <Typography>日</Typography>
            </div>
          </React.Fragment>
        )}

        <div className="flex justify-center gap-4 mt-8">
          <Button type="submit" variant="outlined" onClick={handleClick}>
            検　索
          </Button>
        </div>
      </div>
      {glidInfo.length > 0 && (
        <React.Fragment>
          <Divider className="!my-5" />
          <Table size="small">
            <TableHead>
              <TableRow>
                <StyledTableCell>Glid no</StyledTableCell>
                <StyledTableCell>HDCP</StyledTableCell>
                <StyledTableCell>所属</StyledTableCell>
                <StyledTableCell></StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {glidInfo.map((row, index) => (
                <TableRow key={`row-${index}`}>
                  <TableCell align="center">{row.glid_no}</TableCell>
                  <TableCell align="center">{row.hdcp_index}</TableCell>
                  <TableCell align="center">{row.home_club_name}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      onClick={() => onGlidNoSearch(row)}
                    >
                      選　択
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </React.Fragment>
      )}
      {teeInfo.length > 0 && (
        <React.Fragment>
          <Divider className="!my-5" />
          <Table size="small">
            <TableHead>
              <TableRow>
                <StyledTableCell>氏名</StyledTableCell>
                <StyledTableCell>生年月日</StyledTableCell>
                <StyledTableCell></StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {teeInfo.map((row, index) => (
                <TableRow key={`row-${index}`}>
                  <TableCell align="center">{row.player_name}</TableCell>
                  <TableCell align="center">{row.birthday}</TableCell>
                  <TableCell align="center">
                    <Button
                      variant="contained"
                      onClick={() => onTeeSearch(row)}
                      disabled={playeNoList?.includes(row.player_no)}
                    >
                      選　択
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </React.Fragment>
      )}
    </Box>
  );
};

export default KeyModal;
