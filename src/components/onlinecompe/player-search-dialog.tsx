import React, { useState } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    TextField,
    Button,
    Box,
    Typography,
    Grid,
    Alert,
    Divider,
    Table,
    TableCell,
    TableHead,
    TableBody,
    TableRow,
    ToggleButtonGroup,
    ToggleButton
} from '@mui/material';
import { searchPlayerInfo } from '@/api/online-compe-api';
import { PlayerInfo } from '@/models/compe/resp/player-resp';
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";

interface PlayerSearchDialogProps {
    open: boolean;
    onClose: () => void;
    onApply: (playerInfo: PlayerInfo) => void;
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#eee",
        textAlign: "center",
    },
}));

const PlayerSearchDialog: React.FC<PlayerSearchDialogProps> = ({
    open,
    onClose,
    onApply
}) => {
    const [name, setName] = useState('');
    const [year, setYear] = useState('');
    const [month, setMonth] = useState('');
    const [day, setDay] = useState('');
    const [gildNo, setGildNo] = useState('');
    const [searchResult, setSearchResult] = useState<PlayerInfo[] | null>([]);
    const [inputError, setInputError] = useState(false);
    const [searchError, setSearchError] = useState(false);
    const [searchType, setSearchType] = useState<string>('1');
    const dailogClose = () => {
        setName('');
        setYear('');
        setMonth('');
        setDay('');
        setGildNo('');
        setSearchType('1');
        setSearchResult([]);
        setInputError(false);
        setSearchError(false);
        onClose();
    }

    const handleSearch = async () => {
        if (searchType === '2' && (name === '' || year === '' || month === '' || day === '')) {
            setInputError(true);
            return;
        }
        if (searchType === '1' && (gildNo === '')) {
            setInputError(true);
            return;
        }
        setInputError(false);
        if (searchType === '2') {
            const birthday = year && month && day
                ? `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}` : '';
            try {
                const result = await searchPlayerInfo(name, birthday, null, null, 1);
                if (result.code === 200 && result.data && result.data.length !== 0) {
                    setSearchError(false);
                    setSearchResult(result.data);
                } else {
                    setSearchError(true);
                    setSearchResult(null);
                }
            } catch (error) {
                setSearchError(true);
                setSearchResult(null);
            }

        } else if (searchType === '1') {
            try {
                const result = await searchPlayerInfo('null', 'null', gildNo, '1970-01-01', 2);
                if (result.code === 200 && result.data && result.data.length !== 0) {
                    setSearchError(false);
                    setSearchResult(result.data);
                } else {
                    setSearchError(true);
                    setSearchResult(null);
                }
            } catch (error) {
                setSearchError(true);
                setSearchResult(null);
            }
        }

    };

    const handleSearchRadioClick = (
        _: React.MouseEvent<HTMLElement, MouseEvent>,
        newRadio: string
    ) => {
        if (searchType === newRadio) {
            return;
        }
        setYear('');
        setMonth('');
        setDay('');
        setGildNo('');
        setName('');
        setSearchResult(null);
        setInputError(false);
        setSearchError(false);
        setSearchType(newRadio);
    };
    return (
        <Dialog
            open={open}
            onClose={dailogClose}
            PaperProps={{
                sx: {
                    width: '550px',
                    borderRadius: 2,

                }
            }}
        >
            <DialogTitle sx={{ pb: 1, fontSize: '18px' }}>
                Glid No検索
            </DialogTitle>
            <Box sx={{
                height: '1px',
                width: 'auto',
                background: '#707070',
                marginLeft: '23px',
                marginRight: '23px',
            }}
            ></Box>
            <ToggleButtonGroup
                color="info"
                value={searchType}
                exclusive
                onChange={handleSearchRadioClick}
                className="gap-4 my-2"
                size="small"
                sx={{
                    "& .MuiToggleButton-root": {
                        color: "#304A89 !important",
                        border: "1px solid #304A89 !important",
                        borderRadius: "4px !important",
                    },
                    "& .Mui-selected": {
                        color: "#fff !important",
                        backgroundColor: "#304A89 !important",
                    },
                    "& .MuiToggleButton-root:hover": {
                        color: "#fff !important",
                        backgroundColor: "#304A89 !important",
                    },
                    marginLeft: '23px',
                }}
            >
                <ToggleButton value="1">Glid No</ToggleButton>
                <ToggleButton value="2">名前と年月日</ToggleButton>
            </ToggleButtonGroup>
            {inputError && (
                <Alert
                    severity="error"
                    className="my-2"
                    sx={{
                        marginLeft: '23px',
                        marginRight: '23px',
                    }}
                >
                    検索条件を入力してください。
                </Alert>
            )}
            {searchError && (
                <Alert
                    severity="error"
                    className="my-2"
                    sx={{
                        marginLeft: '23px',
                        marginRight: '23px',
                    }}
                >
                    検索している情報が存在しません。
                </Alert>
            )}
            <DialogContent>
                <Box sx={{ display: 'flex', flexDirection: 'column', }}>
                    {searchType === '2' && (
                        <React.Fragment>
                            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', }}>
                                <Typography component="label"
                                    sx={{
                                        display: 'block',
                                        fontSize: '22px',
                                        fontWeight: 'bold',
                                        width: '120px',
                                        textAlign: 'right'
                                    }}>
                                    氏名：
                                </Typography>
                                <TextField
                                    style={{ width: '215px', fontSize: '14px', height: "30px" }}
                                    InputProps={{
                                        style: { height: "30px" },
                                    }}
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    size="small"
                                />
                            </Box>

                            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', mt: '30px' }}>
                                <Typography component="label"
                                    sx={{
                                        display: 'block',
                                        fontSize: '22px',
                                        fontWeight: 'bold',
                                        width: '120px',
                                        textAlign: 'right'
                                    }}>
                                    生年月日：
                                </Typography>
                                <TextField
                                    style={{ width: '100px', fontSize: '14px', height: "30px" }}
                                    InputProps={{
                                        style: { height: "30px" },
                                    }}
                                    size="small"
                                    value={year}
                                    onChange={(e) => setYear(e.target.value)}
                                />
                                <Grid item sx={{ fontSize: '22px', fontWeight: 'bold', marginLeft: '10px', marginRight: '10px' }}>年</Grid>
                                <TextField
                                    style={{ width: '75px', fontSize: '14px', height: "30px" }}
                                    InputProps={{
                                        style: { height: "30px" },
                                    }}
                                    size="small"
                                    value={month}
                                    onChange={(e) => setMonth(e.target.value)}
                                />
                                <Grid item sx={{ fontSize: '22px', fontWeight: 'bold', marginLeft: '10px', marginRight: '10px' }}>月</Grid>
                                <TextField
                                    style={{ width: '75px', fontSize: '14px', height: "30px" }}
                                    InputProps={{
                                        style: { height: "30px" },
                                    }}
                                    size="small"
                                    value={day}
                                    onChange={(e) => setDay(e.target.value)}
                                />
                                <Grid item sx={{ fontSize: '22px', fontWeight: 'bold', marginLeft: '10px', marginRight: '10px' }}>日</Grid>
                            </Box>
                        </React.Fragment>
                    )}
                    {searchType === '1' && (
                        <React.Fragment>
                            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', }}>
                                <Typography component="label"
                                    sx={{
                                        display: 'block',
                                        fontSize: '22px',
                                        fontWeight: 'bold',
                                        width: '120px',
                                        textAlign: 'right'
                                    }}>
                                    Glid No：
                                </Typography>
                                <TextField
                                    style={{ width: '215px', fontSize: '14px', height: "30px" }}
                                    InputProps={{
                                        style: { height: "30px" },
                                    }}
                                    value={gildNo}
                                    onChange={(e) => setGildNo(e.target.value)}
                                    size="small"
                                />
                            </Box>
                        </React.Fragment>
                    )}

                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: searchResult && searchResult.length > 0 ? '20px' : '46px', mt: '44px' }}>
                        <Button
                            variant="outlined"
                            onClick={handleSearch}
                            sx={{
                                width: '165px',
                                height: '40px',
                                fontSize: '20px',
                                border: '2px solid #324F85'
                            }}
                        >
                            検索
                        </Button>
                    </Box>
                    {searchResult && searchResult.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                            <Divider className="!my-5" />
                            <Table sx={{ tableLayout: 'fixed', width: '100%' }} size="small">
                                <TableHead>
                                    <TableRow>
                                        <StyledTableCell>Glid no</StyledTableCell>
                                        <StyledTableCell>HDCP</StyledTableCell>
                                        <StyledTableCell>所属</StyledTableCell>
                                        <StyledTableCell></StyledTableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {searchResult.map((player, index) => (
                                        <TableRow key={`row-${index}`}>
                                            <TableCell align="center">{player.glid_no}</TableCell>
                                            <TableCell align="center">{player.hdcp_index}</TableCell>
                                            <TableCell align="center">{player.home_club_name}</TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="contained"
                                                    onClick={() => {
                                                        dailogClose();
                                                        onApply(player);
                                                    }}
                                                >
                                                    適 用
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </Box>
                    )}
                </Box>
            </DialogContent>
        </Dialog>
    );
};

export default PlayerSearchDialog;
