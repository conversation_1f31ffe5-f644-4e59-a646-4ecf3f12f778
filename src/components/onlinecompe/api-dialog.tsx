import {Dialog, DialogContent, DialogContentText, DialogActions, Button} from "@mui/material";
import { useState, useEffect } from "react";

export interface ApiDialogProps {
    openApiDialog: boolean;
    handleCloseErrorDialog: () => void;
    succeed: boolean;
    apiType: string;
}
const ApiDialog: React.FC<ApiDialogProps> = ({
    openApiDialog,
    handleCloseErrorDialog,
    succeed,
    apiType
}) => {
    const [apiMsg, setApiMsg] = useState("");
    useEffect(() => {
        switch (apiType) {
            case 'createCompe':
                setApiMsg(succeed ? "新規登録成功しました。": "新規登録失敗しました。");
                break;
            case 'defaultSetting':
                setApiMsg(succeed ? "基本設定成功しました。": "基本設定録失敗しました。");
                break;
            case 'updateCompe': 
                setApiMsg(succeed ? "編集保存成功しました。": "編集保存失敗しました。");
                break;
            case 'startguidesettings':
                setApiMsg(succeed ? "スタート案内設定を保存成功しました。": "スタート案内設定を保存失敗しました。");
                break;
            case 'officesettings':
                setApiMsg(succeed ? "システム設定を保存成功しました。": "システム設定を保存失敗しました。");
                break;
            default:
                break;
        }
      }, [succeed]);
    
    
    return (<Dialog
    open={openApiDialog}
    onClose={handleCloseErrorDialog}
    >
        <DialogContent>
            <DialogContentText id="custom-dialog-description">
            {apiMsg} 
            </DialogContentText>
        </DialogContent>
        <DialogActions>
            <Button onClick={handleCloseErrorDialog} autoFocus>
                閉じる
            </Button>
        </DialogActions>
    </Dialog>);
}

export default ApiDialog;
