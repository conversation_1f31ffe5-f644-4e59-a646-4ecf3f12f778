import React, { useState, useRef, useEffect } from "react";
import { Button, TextField, Box, Switch, FormControlLabel, Typography } from "@mui/material";
import SaveIcon from '@mui/icons-material/Save';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useDrag, useDrop } from "react-dnd";
import { deleteQuestion, updateQuestion } from "@/api/caddy-api";
import { CommonUtils } from '@/utils/common-utils';

const ItemTypes = {
    CARD: 'card',
};

const SettingsItem = ({ question, index, moveCard, onDelete, onSave, onDropComplete, componentType, errorHintMsg, editingId, onEdit }: {
    question: any,
    index: any,
    moveCard: any,
    onDelete: any,
    onSave: any,
    onDropComplete: any,
    componentType: any,
    errorHintMsg: string,
    editingId: number | null,
    onEdit: (id: number) => void;
}) => {

    const [questionText, setQuestionText] = useState(question.question);
    const [id, setId] = useState(question.id);
    const [sqlIndex, setSqlIndex] = useState(question.sqlIndex);
    const [isRequired, setIsRequired] = useState(question.isRequired);
    const textFieldRef = useRef<any | null>(null);

    const [error, setError] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");

    useEffect(() => {
        setQuestionText(question.question);
        setIsRequired(question.isRequired);
        setId(question.id);
        setSqlIndex(question.sqlIndex);
    }, [question]);

    const ref = useRef(null);

    const [, drop] = useDrop({
        accept: ItemTypes.CARD,
        hover(item: { type: string, index: any, componentType: any }) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            if (dragIndex !== hoverIndex) {
                moveCard(dragIndex, hoverIndex, item.componentType);
                item.index = hoverIndex;
            }
        },
        drop: () => {
            if (onDropComplete) {
                onDropComplete(componentType, id);
            }
        }
    });

    const [{ isDragging }, drag] = useDrag({
        type: ItemTypes.CARD,
        item: { type: ItemTypes.CARD, index, componentType },
        canDrag: editingId === null,
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    // drag(drop(ref));
    const dragDropRef = (node: HTMLDivElement | null) => {
        if (node) {
            drag(drop(node));
        }
    };

    const handleSave = async () => {
        if (questionText.trim() === "") {
            setError(true)
            setErrorMsg("設問が未入力です。入力をお願いします。")
            return;
        }
        if (questionText.length > 50) {
            setError(true)
            setErrorMsg("文字数が制限を超えています。最大50文字まで入力可能です。")
            return;
        }
        setError(false)
        setErrorMsg("")

        try {
            const success = await updateQuestion(id, questionText, isRequired ? 1 : 2);
            if (success) {
                onSave(id, questionText, isRequired);
            }
        } catch (error) {
            setError(true)
            setErrorMsg("設問の編集に失敗しました。")
        }

    };

    const handleEdit = () => {
        onEdit(id)
        setTimeout(() => {
            if (textFieldRef.current) {
                textFieldRef.current.focus();
            }
        }, 0);
    };

    const handleDelete = async () => {
        const confirmDelete = window.confirm("この質問を削除してもよろしいですか？");
        if (confirmDelete) {
            try {
                const success = await deleteQuestion(id);
                if (success) {
                    onDelete(id);
                    setError(false)
                    setErrorMsg("")
                }
            } catch (error) {
                setError(true)
                setErrorMsg("設問の削除に失敗しました。")
            }
        }
    };

    return (
        <div ref={ref}>
            <Box
                sx={{
                    opacity: isDragging ? 0.5 : 1,
                    margin: '4px 0',
                    backgroundColor: 'white',
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '10px',
                }}
            >
                <Box
                    ref={dragDropRef}
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        border: (error || errorHintMsg) ? '1px solid red' : '1px solid #304A89',
                        borderRadius: '4px',
                        padding: '0 8px',
                        flex: 1,
                        mr: 2,
                        // width: { xs: '100%', sm: 'auto' },
                        marginBottom: { xs: '10px', sm: '0' },
                    }}
                >
                    <Box

                        sx={{
                            cursor: editingId === id ? 'default' : 'move',
                            display: 'flex',
                            alignItems: 'center',
                            padding: '0',
                            color: (error || errorHintMsg) ? "red" : "#304A89",
                            marginRight: '10px',
                        }}
                    >
                        {/*:::設問{index + 1}*/}
                        :::設問{sqlIndex}
                    </Box>
                    <TextField
                        size="small"
                        value={questionText}
                        onChange={(e) => setQuestionText(e.target.value)}
                        placeholder={
                            editingId === id
                                ? componentType === 1
                                    ? "例)本日の" + CommonUtils.getCaddyNameFromLocalStorage() + "の接客態度はいかがでしたか?"
                                    : "例)コースの管理は行き届いていましたか?"
                                : ""
                        }
                        sx={{ flex: 1, '& .MuiOutlinedInput-root': { '& fieldset': { border: 'none' } } }}
                        InputProps={{
                            readOnly: editingId !== id,
                        }}
                        inputRef={textFieldRef}
                        inputProps={{
                            maxLength: 50
                        }}
                    />
                </Box>

                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        alignItems: 'center',
                        justifyContent: { xs: 'center', sm: 'flex-end' },
                        width: 'auto',
                        marginBottom: { xs: '10px', sm: '0' },
                    }}
                >
                    <FormControlLabel
                        control={
                            <Switch
                                checked={isRequired}
                                onChange={(e) => setIsRequired(e.target.checked)}
                                color="primary"
                                disabled={editingId !== id}
                            />
                        }
                        label="必須"
                        sx={{ mb: { xs: '10px', sm: '0' } }}
                    />
                    <Button
                        variant="outlined"
                        disabled={editingId !== id}
                        onClick={handleSave}
                        startIcon={<SaveIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        保存
                    </Button>
                    <Button
                        variant="outlined"
                        disabled={editingId !== null}
                        onClick={handleEdit}
                        startIcon={<EditIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        編集
                    </Button>
                    <Button
                        variant="outlined"
                        disabled={editingId !== null}
                        onClick={handleDelete}
                        startIcon={<DeleteIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        削除
                    </Button>
                </Box>
            </Box>
            {(error) ? (
                <Typography color="red" variant="body2" sx={{ mt: -2, mb: 2, marginTop: 0.5 }}>
                    {errorMsg}
                </Typography>
            ) : (errorHintMsg) ? (
                <Typography color="red" variant="body2" sx={{ mt: -2, mb: 2, marginTop: 0.5 }}>
                    {errorHintMsg}
                </Typography>
            ) : (<div></div>)}

        </div>
    );
};

export default SettingsItem;
