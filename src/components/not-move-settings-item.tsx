import React, { useState, useRef, useEffect, useCallback } from "react";
import { Button, TextField, Box, Switch, FormControlLabel, Typography } from "@mui/material";
import SaveIcon from '@mui/icons-material/Save';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { postQuestion } from "@/api/caddy-api";
import { CommonUtils } from '@/utils/common-utils';

const NotMoveSettingItem = ({ question, index, onSave, type, editingId }: { question: any, index: any, onSave: any, type: any, editingId: number | null }) => {

    const [questionText, setQuestionText] = useState(question.question);
    const [id, setId] = useState(question.id);
    const [isRequired, setIsRequired] = useState(question.isRequired);
    const textFieldRef = useRef(null);

    const [error, setError] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");


    useEffect(() => {
        setQuestionText(question.question);
        setIsRequired(question.isRequired);
        setId(question.id);
    }, [question]);

    const ref = useRef(null);

    const handleSave = async () => {
        if (questionText.trim() === "") {
            setError(true)
            setErrorMsg("設問が未入力です。入力をお願いします。")
            return;
        }
        if (questionText.length > 50) {
            setError(true)
            setErrorMsg("文字数が制限を超えています。最大50文字まで入力可能です。")
            return;
        }
        setError(false)
        setErrorMsg("")
        try {
            const success = await postQuestion(questionText, isRequired ? 1 : 2, type);
            if (success) {
                onSave(id, type, questionText, isRequired);
            }
        } catch (error) {
            setError(true)
            setErrorMsg("設問の追加に失敗しました。")
        }
    };


    return (
        <>
            <Box
                ref={ref}
                sx={{
                    margin: '4px 0',
                    backgroundColor: 'white',
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '10px',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        border: error ? '1px solid red' : '1px solid #304A89',
                        borderRadius: '4px',
                        padding: '0 8px',
                        flex: 1,
                        mr: 2,
                        // width: { xs: '100%', sm: 'auto' },
                        marginBottom: { xs: '10px', sm: '0' },
                    }}
                >
                    <Box
                        sx={{
                            cursor: 'default',
                            display: 'flex',
                            alignItems: 'center',
                            padding: '0',
                            color: error ? "red" : "#304A89",
                            marginRight: '10px',
                        }}
                    >
                        :::設問{index + 1}
                    </Box>
                    <TextField
                        size="small"
                        value={questionText}
                        onChange={(e) => setQuestionText(e.target.value)}
                        placeholder={
                            editingId === id
                                ? type === 1
                                    ? "例)本日の" + CommonUtils.getCaddyNameFromLocalStorage() + "の接客態度はいかがでしたか?"
                                    : "例)コ-スの管理は行き届いていましたか?"
                                : ""
                        }
                        sx={{ flex: 1, '& .MuiOutlinedInput-root': { '& fieldset': { border: 'none' } } }}
                        InputProps={{
                            readOnly: editingId !== id,
                        }}
                        inputRef={textFieldRef}
                        inputProps={{
                            maxLength: 50
                        }}
                    />
                </Box>

                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        alignItems: 'center',
                        justifyContent: { xs: 'center', sm: 'flex-end' },
                        width: 'auto',
                        marginBottom: { xs: '10px', sm: '0' },
                    }}
                >
                    <FormControlLabel
                        control={
                            <Switch
                                checked={isRequired}
                                onChange={(e) => setIsRequired(e.target.checked)}
                                color="primary"
                                disabled={editingId !== id}
                            />
                        }
                        label="必須"
                        sx={{ mb: { xs: '10px', sm: '0' } }}
                    />
                    <Button
                        variant="outlined"
                        disabled={editingId !== id}
                        onClick={handleSave}
                        startIcon={<SaveIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        保存
                    </Button>
                    <Button
                        variant="outlined"
                        disabled={editingId !== null}
                        startIcon={<EditIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        編集
                    </Button>
                    <Button
                        variant="outlined"
                        disabled={editingId !== null}
                        startIcon={<DeleteIcon />}
                        sx={{ mx: 1, mb: { xs: '5px', sm: '0' } }}
                    >
                        削除
                    </Button>
                </Box>
            </Box>
            {error && (
                <Typography color="red" variant="body2" sx={{ mt: -2, mb: 2, marginTop: 0.5 }}>
                    {errorMsg}
                </Typography>
            )}
        </>
    );
};


export default NotMoveSettingItem;
