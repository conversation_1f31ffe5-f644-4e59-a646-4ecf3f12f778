import React, { useState } from 'react';

const SurveyForm: any = ({ questions = [], options = [] }) => {
    const [confirmed, setConfirmed] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState(
        questions.map(() => options.map(() => false))
    );

    const handleOptionClick = (questionIndex: any, optionIndex: any) => {
        setSelectedOptions(prevState =>
            prevState.map((optionArray, qIndex) =>
                qIndex === questionIndex
                    ? optionArray.map((isSelected, oIndex) =>
                        oIndex === optionIndex ? !isSelected : false
                    )
                    : optionArray
            )
        );
    };

    return (
        <div className="max-w-3xl mx-auto p-4 bg-white shadow-lg rounded-lg border-2 border-gray-300" >
            <div className="mb-4 flex justify-between">
                <div className="border-bottom flex items-center" style={{ borderBottom: "solid 1px" }}>
                    <span className="font-bold text-2xl pr-8">重野</span>
                    <span className="font-bold text-2xl pr-14">松之助</span>
                    <span className="font-bold ml-4">様</span>
                </div>

                <div className="text-sm text-gray-600 text-right">
                    クラブの確認がお済みでしたら
                    <br />
                    「確認しました」をタッチしてください
                </div>
            </div>

            <div className="mb-4 flex items-center border-2 border-green-600 rounded-md overflow-hidden">
                <div className="py-2 px-4 flex justify-center border-r-2 border-green-600 basis-1/2 font-bold text-2xl" style={{ backgroundColor: "rgb(200,235,160)" }}>
                    <span>クラブ確認</span>
                </div>
                <div className="flex justify-center items-center py-2 px-4 basis-1/2 text-2xl">
                    <input
                        type="checkbox"
                        checked={confirmed}
                        onChange={() => setConfirmed(!confirmed)}
                        className="mr-2 w-6 h-6 cursor-pointer"
                    />
                    <span>確認しました</span>
                </div>
            </div>

            <div className="bg-yellow-100 p-2 mb-4 text-sm flex justify-center">
                <span className="font-bold">以下のアンケートにご協力ください。</span>
                <span className="font-bold text-red-600">*</span>
                <span className="text-xs">は回答必須項目です。</span>
            </div>

            <div className="overflow-y-auto" style={{ maxHeight: "220px" }}>
                <table className="w-full mb-4">
                    <tbody>
                    {questions.map((question: any, questionIndex: any) => (
                        <tr key={question.id}>
                            <td className="py-2 px-3 font-bold text-center border border-gray-400" style={{ backgroundColor: "rgb(200,235,160)", width: '40px' }}>{questionIndex + 1}</td>
                            <td className="py-2 px-1 text-xs border border-gray-400">
                                {question.question}
                                {question.isRequired && <span className="text-red-500">*</span>}
                            </td>
                            {options.map((option: any, optionIndex) => (
                                <td
                                    key={optionIndex}
                                    className="py-2 w-20 border border-gray-400"
                                    style={{ backgroundColor: selectedOptions[questionIndex][optionIndex] ? "rgb(200,235,160)" : "inherit" ,  cursor: 'pointer'}}
                                    onClick={() => handleOptionClick(questionIndex, optionIndex)}
                                >
                                    <div id={`question-${question.id}`}
                                         className="h-4 text-xs flex justify-center"
                                    >{option.content}</div>
                                </td>
                            ))}
                        </tr>
                    ))}
                    </tbody>
                </table>
            </div>
            <div className="text-sm mb-4 flex justify-center font-bold">
                <span>回答が終わりましたら「送信」ボタンをタッチしてください。ご協力ありがとうございました。</span>
            </div>

            <div className="flex justify-between">
                <button className="rounded-lg border-2 border-red-600 text-white py-1 px-6 rounded font-bold" style={{ background: "linear-gradient(rgb(255,130,130), rgb(255,0,0))" }}>戻る</button>
                <button className="rounded-lg border-2 border-gray-400 text-white py-1 px-6 rounded font-bold" style={{ background: "linear-gradient(rgb(236,236,236), rgb(114,114,114))" }}>送信</button>
                <div className="w-20"></div>
            </div>
        </div>
    );
};

export default SurveyForm;
