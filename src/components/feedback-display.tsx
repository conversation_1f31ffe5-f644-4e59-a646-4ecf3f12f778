import React, { useState } from "react";

interface FeedbackDisplayProps {
    feedback: string;
    id: string;
    handleFeedbackToggle: (id: string) => void;
    expandedFeedback: Record<string, boolean>;
}

const FeedbackDisplay: React.FC<FeedbackDisplayProps> = ({ feedback, id, handleFeedbackToggle, expandedFeedback }) => {
    const isLongFeedback = feedback.length > 50;

    const renderFeedback = (text: string) =>
        text.split('\n').map((line, index) => (
            <React.Fragment key={index}>
                {line}
                <br />
            </React.Fragment>
        ));

    if (!isLongFeedback) {
        return <>{renderFeedback(feedback)}</>;
    }

    return (
        <>
            {expandedFeedback[id] ? (
                <>
                    {renderFeedback(feedback)}
                    <div
                        style={{color: "blue", cursor: "pointer"}}
                        onClick={() => handleFeedbackToggle(id)}
                    >
            一部を表示
          </div>
                </>
            ) : (
                <>
                    {renderFeedback(feedback.slice(0, 50))}
                    <div
                        style={{color: "blue", cursor: "pointer"}}
                        onClick={() => handleFeedbackToggle(id)}
                    >
            全体を表示
          </div>
                </>
            )}
        </>
    );
};

export default FeedbackDisplay;
