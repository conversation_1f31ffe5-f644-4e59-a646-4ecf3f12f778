import React, {useEffect} from "react";
import {
  Pagination,
  PaginationItem,
  Typography,
  TextField,
  MenuItem,
} from "@mui/material";
import { styled } from "@mui/system";
import { useTheme } from "@mui/material/styles";

const FixedPaginationItem = styled(PaginationItem)(({ theme }) => ({
  width: "50px",
  height: "50px",
  margin: "-0.5px",
  borderRadius: "0",
  border: "1px solid",
  borderColor: theme.palette.grey[400],
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: theme.palette.primary.main,
  fontSize: "16px",
  "&.Mui-selected": {
    backgroundColor: theme.palette.primary.main,
    color: "white",
  },
}));

const PaginationIcon = ({ type }: { type: string }) => {
  const theme = useTheme();
  return (
    <span
      style={{
        fontSize: "32px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        color: theme.palette.primary.main,
      }}
    >
      {type === "previous" ? "«" : "»"}
    </span>
  );
};

interface PaginationComponentProps {
  count: number;
  page: number;
  onChange: (event: any, value: number) => void;
  selectedCount: number;
  onCountChange: (event: any) => void;
  counts: number[];
}

const PaginationComponent: React.FC<PaginationComponentProps> = ({
  count,
  page,
  onChange,
  selectedCount,
  onCountChange,
  counts,
}) => {
  const theme = useTheme();

  useEffect(() => {
    if (page !== 1) {
      onChange({ target: { value: 1 } }, 1);
    }
  }, [count]);

  return (
    <div className="flex justify-between items-center mt-4">
      <div className="w-full sm:w-auto flex flex-wrap items-center space-x-4 mt-4">
        <Typography noWrap component="span" variant="h3" color="primary">
          表示件数:
        </Typography>

        <TextField
          select
          variant="outlined"
          size="small"
          name="selectedCount"
          value={selectedCount}
          onChange={onCountChange}
          InputProps={{
            style: { height: "36px" },
          }}
          sx={{
            height: "36px",
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: theme.palette.primary.main,
              },
              "&:hover fieldset": {
                borderColor: theme.palette.primary.dark,
              },
              "&.Mui-focused fieldset": {
                borderColor: theme.palette.primary.main,
              },
            },
            "& .MuiInputBase-input": {
              color: theme.palette.primary.main,
            },
            "& .MuiSelect-icon": {
              color: theme.palette.primary.main,
            },
          }}
        >
          {counts.map((count) => (
            <MenuItem key={count} value={count}>
              {count}
            </MenuItem>
          ))}
        </TextField>
      </div>

      <Pagination
        color="primary"
        variant="outlined"
        shape="rounded"
        count={count}
        page={page}
        siblingCount={2}
        boundaryCount={2}
        onChange={onChange}
        renderItem={(item) => (
          <FixedPaginationItem
            {...item}
            slots={{
              previous: () => <PaginationIcon type="previous" />,
              next: () => <PaginationIcon type="next" />,
            }}
          />
        )}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
        }}
      />
    </div>
  );
};

export default PaginationComponent;
