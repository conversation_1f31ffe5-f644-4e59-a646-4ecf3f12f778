import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';

interface CsvErrorDialogProps {
    open: boolean;
    onClose: () => void;
    btnText: string;
    description: string;
}

export default function CsvErrorDialog({ open, onClose, description, btnText }: CsvErrorDialogProps) {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            aria-labelledby="custom-dialog-title"
            aria-describedby="custom-dialog-description"
        >
            <DialogContent>
                <DialogContentText id="custom-dialog-description">
                    {description}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} autoFocus>
                    {btnText}
                </Button>
            </DialogActions>
        </Dialog>
    );
}
