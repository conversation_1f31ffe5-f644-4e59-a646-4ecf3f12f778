import React from "react";
import {
  TableRow,
  TableCell,
  IconButton,
  Typography,
  Collapse,
  Box,
  Table,
  TableHead,
  TableBody,
} from "@mui/material";
import { styled } from "@mui/system";
import AddBoxOutlinedIcon from "@mui/icons-material/AddBoxOutlined";
import IndeterminateCheckBoxOutlinedIcon from "@mui/icons-material/IndeterminateCheckBoxOutlined";
import { formatDate, formatTime } from "../utils/formatters";
import { Caddy, Question, Survey, Cart } from "../models/caddy-models";
import { CommonUtils } from '@/utils/common-utils';

interface TableRowComponentProps {
  row: Cart;
  rowIndex: number;
  survey: Survey;
  surveyIndex: number;
  questions: Question[];
  caddies: Caddy[];
  expanded: boolean;
  handleRowClick: (index: string) => void;
}

const TableRowComponent: React.FC<TableRowComponentProps> = ({
  row,
  rowIndex,
  survey,
  surveyIndex,
  questions,
  caddies,
  expanded,
  handleRowClick,
}) => {
  return (
    <React.Fragment>
      <TableRow key={`${rowIndex}-${surveyIndex}`}>
        <TableCell>
          {surveyIndex === 0 ? (
            <IconButton
              aria-label="expand row"
              size="small"
              onClick={() => {
                handleRowClick(`${rowIndex}`);
              }}
            >
              {expanded ? (
                <IndeterminateCheckBoxOutlinedIcon color="primary" />
              ) : (
                <AddBoxOutlinedIcon color="primary" />
              )}
            </IconButton>
          ) : null}
        </TableCell>
        <TableCell>
          <Typography variant="h2" align="center">
            {row.cart_no}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="h2" align="center">
            {formatDate(row.played_date)}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="h2" align="center">
            {row.status === 1 ? "OUT" : "IN"}
            <br />
            {formatTime(row.start_time)}
          </Typography>
        </TableCell>
        <TableCell className="hidden-on-small">
          <Typography variant="h2" align="center">
            {survey.name}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="h2" align="center">
            {caddies.find((caddy) => caddy.id === row.caddy_no)?.caddy_name || ""}
          </Typography>
        </TableCell>
        {questions.map((question) => (
          <TableCell key={question.id} className="hidden-on-small">
            <Typography variant="h2" align="center">
              {survey.answers
                .find(
                  (ans) =>
                    ans.index === question.index && ans.type === question.type
                )
                ?.answer.toString() || "未回答"}
            </Typography>
          </TableCell>
        ))}
        <TableCell className="hidden-on-small">
          <Typography variant="h2" align="center">
            {formatDate(survey.answer_time)}
            <br />
            {formatTime(survey.answer_time)}
          </Typography>
        </TableCell>
      </TableRow>
      <TableRow className="hidden-on-full">
        <TableCell
          style={{ paddingBottom: 0, paddingTop: 0 }}
          colSpan={questions.length + 2}
        >
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Box margin={1}>
              <Table size="small" aria-label="purchases">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
                      <Typography
                        sx={{ fontWeight: "bold" }}
                        color="primary"
                        variant="h4"
                        align="center"
                      >
                        名前
                      </Typography>
                    </TableCell>
                    {questions.map((question) => (
                      <TableCell
                        key={question.id}
                        sx={{ borderBottom: `1px solid primary.main` }}
                      >
                        <Typography
                          sx={{ fontWeight: "bold" }}
                          color="primary"
                          variant="h4"
                          align="center"
                        >
                          {question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"} 設問{" "}
                          {question.index}
                        </Typography>
                      </TableCell>
                    ))}
                    <TableCell sx={{ borderBottom: `1px solid primary.main` }}>
                      <Typography
                        sx={{ fontWeight: "bold" }}
                        color="primary"
                        variant="h4"
                        align="center"
                      >
                        回答日時
                      </Typography>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {row.survey.map((sur, surIndex) => (
                    <TableRow key={surIndex}>
                      <TableCell>
                        <Typography variant="h5" align="center">
                          {sur.name}
                        </Typography>
                      </TableCell>
                      {questions.map((question) => (
                        <TableCell key={question.id}>
                          <Typography variant="h5" align="center">
                            {sur.answers
                              .find(
                                (ans) =>
                                  ans.index === question.index &&
                                  ans.type === question.type
                              )
                              ?.answer.toString() || "未回答"}
                          </Typography>
                        </TableCell>
                      ))}
                      <TableCell>
                        <Typography variant="h5" align="center">
                          {formatDate(survey.answer_time)}
                          <br />
                          {formatTime(survey.answer_time)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
};

export default TableRowComponent;
