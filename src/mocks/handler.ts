import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw'

const BaseUrl = "http:/";
const AuthUrl = BaseUrl?.replace("/web", "");
export const handlers = [
    http.get(`${BaseUrl}/question`, () => {
        const questionData = [{
            id: 1,
            index: 1,
            type: 1,
            content: "test conten",
            require: 1
        },{
            id: 2,
            index: 2,
            type: 2,
            content: "test content2",
            require: 2
        },
        ]
        const mockData = {
            code: 200,
            data: questionData
        }
        return HttpResponse.json(mockData)
    }),
    http.get(`${BaseUrl}/questionnaire`, () => {
        const dataRows = {
            code: 200,
            current_page: 1,
            data: [{
                id: 1,
                cart_no: "1",
                caddy_no: 1,
                caddy_id: "test1",
                played_date: "2024-08-08",
                start_time: "2024-08-07",
                status: 1,
                survey: [{
                    id: 1,
                    name: "test1",
                    answer_time: "2024-08-08",
                    answers: [{
                        id: 1,
                        index: 1,
                        type: 1,
                        answer: 1,
                        count: 1
                    }],
                }],
                open: true,
                start_course: "test"
              }],
            from: 1,
            last_page: 1,
            per_page: 5,
            to: 1,
            total: 4,
        }
        const mockData = {
            code: 200,
            data: dataRows
        }
        return HttpResponse.json(dataRows)
    }),
    http.get(`${BaseUrl}/caddylist`, () => {
        const caddyData = [{
            id: 1,
            caddy_no: "1",
            caddy_name: "caddy 1"
        },{
            id: 2,
            caddy_no: "2",
            caddy_name: "caddy 2"
        },{
            id: 3,
            caddy_no: "3",
            caddy_name: "caddy 3"
        }];
        const mockData = {
            code: 200,
            data: caddyData
        }
        return HttpResponse.json(mockData)
    }),
    http.post(`${BaseUrl}/question`, () => {
        const mockData = {
            code: 200,
        }
        return HttpResponse.json(mockData)
    }),
    http.put(`${BaseUrl}/question/:id`, (paramters) => {
        const mockData = {
            code: 200,
        }
        return HttpResponse.json(mockData)
    }),
    http.put(`${BaseUrl}/questionindex`, () => {
        const mockData = {
            code: 200,
        }
        return HttpResponse.json(mockData)
    }),
    http.delete(`${BaseUrl}/question/:id`, (paramters) => {
        const mockData = {
            code: 200,
        }
        return HttpResponse.json(mockData)
    }),
    http.get(`${BaseUrl}/evaluation`, () => {
        const evaluations = [{
            id: 1,
            score: 1,
            stage: 1,
            content: "evaluation test1"
          },{
            id: 2,
            score: 2,
            stage: 2,
            content: "evaluation test2"
          },{
            id: 3,
            score: 3,
            stage: 3,
            content: "evaluation test3"
          },{
            id: 4,
            score: 4,
            stage: 4,
            content: "evaluation test4"
          }]
          const mockData = {
            code: 200,
            data: evaluations
          }
          return HttpResponse.json(mockData)
    }),
    http.put(`${BaseUrl}/evaluation`, (paramaters) => {
        const mockData = {
            code: 200,
        }
        return HttpResponse.json(mockData)
    }),
    http.get(`${BaseUrl}/caddy`, (paramters) => {
        const surveyDatas = [{
            month: "2024-08",
            survey: [{
                caddy_id: "1",
                survey: [{
                    id: 1,
                    answers: [{
                        id: 1,
                        index: 1,
                        type: 1,
                        answer: 12,
                        count: 1,
                    }],
                }],
            },{
                caddy_id: "2",
                survey: [{
                    id: 2,
                    answers: [{
                        id: 2,
                        index: 2,
                        type: 2,
                        answer: 13,
                        count: 1,
                    }],
                }],
            }],
          }] 
          const mockData = {
            code: 200,
            data: surveyDatas
          }
          return HttpResponse.json(mockData)
    }),
    http.get(`${BaseUrl}/statistical`, (paramters) => {
        const statisticalDatas = [{
            month: "2024-08",
            survey: [{
                id: 0,
                answers: [{
                    id: 1,
                    index: 1,
                    type: 1,
                    answer: 12,
                    count: 1,
                }],
            }],
          }];
          const mockData = {
            code: 200,
            data: statisticalDatas
          }
          return HttpResponse.json(mockData)
    }),
    http.post(`${AuthUrl}/access`, () => {
        const tokenData = {
            access_token: "acceseetokentest",
            refresh_token: "refreshtokentest"
        };
        return HttpResponse.json(tokenData)
    }),
    http.post(`${AuthUrl}/refresh`, () => {
        const tokenData = {
            access_token: "acceseetokentest2",
            refresh_token: "refreshtokentest2"
        };
        
          return HttpResponse.json(tokenData)
    }),
]