@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
} */

/* styles/global.css */
@font-face {
  font-family: "Kosugi Maru";
  src: url("/webapp/fonts/KosugiMaru-Regular.ttf") format("truetype");
}

body {
  /*font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Hiragino Kaku Gothic Pro", "メイリオ", sans-serif;*/
  font-family: "メイリオ", "Meiryo", "ＭＳ ゴシック", "Osaka", 'Source Sans Pro', sans-serif;

  /* font-family: "Kosugi Maru", "メイリオ", "Meiryo", "ＭＳ ゴシック", "Osaka", "Source Sans Pro", sans-serif; */

  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@media (max-width: 910px) {
  .hidden-on-small {
    display: none;
  }
}

@media (min-width: 910px) {
  .hidden-on-full {
    display: none;
  }
}

@media print {
  .print-box {
    display: block !important;
  }

  .no-print {
    display: none !important;
  }
}