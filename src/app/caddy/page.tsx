"use client";
import React, { useEffect, useState } from "react";
import { useTheme } from "@mui/material/styles";
import {
    Area,
    AreaChart,
    Bar,
    BarChart,
    CartesianGrid,
    LabelList,
    Legend,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts';
import { Box, Button, Checkbox, Divider, FormControlLabel, Paper, TableContainer, Typography, } from "@mui/material";
import Breadcrumb from "../../components/breadcrumb";
import DataForm from "../../components/caddy/date-form";
import { fetchCaddies, getCaddyEvaluate, getEvaluations, getQuestion, getStatistical } from "@/api/caddy-api";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { Caddy, Evaluation, Question, StatisticalData, SurveyData, SurveyItem, } from "@/models/caddy-models";
import CaddyTable from "@/components/caddy/caddy-table-component";
import CaddyQuesTable from "@/components/caddy/caddy-ques-table-component";
import "../answer/answer.css";
import { MathUtils } from '@/utils/math-utils';
import { DateUtils } from '@/utils/date-utils';
import colors from '../../utils/color';
import ErrorHint from "@/components/error-hint";
import { handleAuthToken } from "../../auth/Auth"
import { Decimal } from 'decimal.js';
import CaddyTableForMobile from "@/components/caddy/caddy-table-component-for-mobile";
import CaddyQuesTableForMobile from "@/components/caddy/caddy-ques-table-component-for-mobile";
import { CommonUtils } from '@/utils/common-utils';
import CsvErrorDialog from "@/components/csv-error-dialog";

const Home = () => {
    const [caddyNameType, setCaddyNameType] = useState<string>("キャディ");
    useEffect(() => setCaddyNameType(CommonUtils.getCaddyNameFromLocalStorage()), [])
    const theme = useTheme();
    const breadcrumbItems = [
        { label: "アンケート", href: "/" },
        { label: caddyNameType + "評価" },
    ];
    const [widthSize, setWidthSize] = useState(true);
    useEffect(() => {
        handleAuthToken().then(
            (value) => {
                if (value) {
                    const handleResize = () => {
                        if (window.innerWidth < 1000) {
                            setWidthSize(false);
                        } else {
                            setWidthSize(true);
                        }
                    };

                    handleResize(); // Initialize the state based on the current window width
                    window.addEventListener("resize", handleResize);
                    return () => window.removeEventListener("resize", handleResize);
                } else {
                    // const login = process.env.BASE_URL?.replace("api/v2/web", "") + "ops/login";
                    // window.location.href = login;
                }
            },
        );
    }, []);
    // error
    const [questingsApi, setQuestingsApi] = useState(true);
    const [questingsApiSuccess, setQuestingsApiSuccess] = useState(true);
    const [evaluationApi, setEvaluationApi] = useState(true);
    const [evaluationApiSuccess, setEvaluationApiSuccess] = useState(true);
    const [caddiesApi, setCaddiesApi] = useState(true);
    const [caddiesApiSuccess, setCaddiesApiSuccess] = useState(true);

    const [caddyListApiSuccess, setCaddyListApiSuccess] = useState(true);
    const [statisticalListApiSuccess, setStatisticalListApiSuccess] = useState(true);
    const handleErrorButtonClick = async (type: string) => {
        if (type === "all") {
            fetchData();
        }
        if (type === "option") {
            await fetchCaddyEvaluateData(DateUtils.initAMonthAgo(filters.year.replace("年度", ""), filters.month.toString().replace("月度", ""))
                , DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
                ), Number(filters.weekday), filters.caddie);
            //get char data
            if (filters.caddie !== "none" && filters.caddie.length > 0) {
                await getChatData(DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
                ), Number(filters.weekday), filters.caddie);
            }
        }
    };
    const getOptionError = () => {
        if (filters.caddie !== "none" && filters.caddie.length > 0) {
            return questingsApi && questingsApiSuccess && (!caddyListApiSuccess || !statisticalListApiSuccess)
        } else {
            return questingsApi && questingsApiSuccess && !caddyListApiSuccess
        }
    }

    //databean
    const TableDataQu = (id: number, count: number, total: number, average: number, compare: number, type: number) => {
        return { id, count, total, average, compare, type };
    };

    const tableData = (index: string, caddyName: String, tableDataQuArray: any) => {
        return {
            index,
            caddyName,
            tableDataQuArray
        };
    };
    //api-var
    const [caddies, setCaddies] = useState<Caddy[]>([]);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [caddyList, setCaddyList] = useState<SurveyData[]>([]);
    const [evaluationsList, setEvaluationsList] = useState<Evaluation[]>([]);
    const [statisticalList, setStatisticalList] = useState<StatisticalData[]>([]);
    //var
    const [starDate, setStarDate] = useState<string>("")
    const [endDate, setEndDate] = useState<string>("")
    const [selectedValues, setSelectedValues] = useState<{ [key: string]: boolean }>({});
    const [lineData, setLineData] = useState<{ name: string; }[]>([]);
    const [charDate, setCharDate] = useState("")
    //api
    const fetchData = async () => {
        try {
            const caddiesData = await fetchCaddies();
            setCaddiesApi(true)
            setCaddiesApiSuccess(true)
            if (caddiesData) {
                setCaddies(caddiesData);
            } else {
                setCaddiesApi(false);
            }
        } catch (error) {
            setCaddiesApiSuccess(false);
        }
        try {
            const questionsData = await getQuestion();
            setQuestingsApi(true)
            setQuestingsApiSuccess(true);
            if (questionsData) {
                setQuestions(questionsData);
            } else {
                setQuestingsApi(false)
            }
        } catch (error) {
            setQuestingsApiSuccess(false);
        }
        try {
            const evaluations = await getEvaluations();
            setEvaluationApi(true)
            setEvaluationApiSuccess(true);
            if (evaluations) {
                setEvaluationsList(evaluations);
            } else {
                setEvaluationApi(false)
            }
        } catch (error) {
            setEvaluationApiSuccess(false);
        }
        await fetchCaddyEvaluateData(DateUtils.getAMonthAgo(), DateUtils.getMonth(), 3, undefined);
    };
    const fetchCaddyEvaluateData = async (startMonth: string, endMonth: string, weekday: number, caddyID: any) => {
        try {
            setCaddyList([])
            const caddyData = await getCaddyEvaluate(startMonth, endMonth, weekday, caddyID);
            setCaddyListApiSuccess(true)
            setStarDate(startMonth)
            setEndDate(endMonth)
            if (caddyData) {
                setCaddyList(caddyData);
            }
        } catch (error) {
            setCaddyListApiSuccess(false)
        }

    };
    const getChatData = async (month: string, weekday: number, caddyId: string, isRefresh: boolean = false) => {
        setCharDate(month)
        try {
            setLineData([]);
            setSelectedValues({});
            const statistical = await getStatistical(DateUtils.getOneYearsAgoMonth(month), month,
                weekday, caddyId);
            setStatisticalListApiSuccess(true)
            if (statistical) setStatisticalList(statistical)
            const initialSelectedValues = generateInitialValues(questions, isRefresh);
            setSelectedValues(initialSelectedValues)
            const lineData = DateUtils.getOneYearLineData(month);
            const updatedLineData = lineData.map(month => ({
                ...month,
                ...Object.keys(initialSelectedValues).reduce((acc: { [key: string]: number }, key: string) => {
                    acc[key] = getStatisticalListAverage(key, DateUtils.formatDateString(month.name)
                        , questions, statistical, evaluationsList);
                    return acc;
                }, {})
            }));
            setLineData(updatedLineData);
        } catch (error) {
            setStatisticalListApiSuccess(false)
        }
    };
    useEffect(() => {
        fetchData();

    }, []);

    //Option var and fun
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    const [optionFilters, setOptionFilters] = useState({
        year: currentYear + "年度",
        month: currentMonth + "月度",
        caddie: "none",
        weekday: "3",
    });

    const [filters, setFilters] = useState({
        year: currentYear + "年度",
        month: currentMonth + "月度",
        caddie: "none",
        weekday: "3",
    });

    const weekdays = [
        { label: "平日のみ", value: 1 },
        { label: "土日祝", value: 2 },
        { label: "指定なし", value: 3 },
    ];
    const getWeekdayLabel = (weekdays: any[], value: string) => {
        const weekday = weekdays.find((day) => day.value === Number(value));
        return weekday ? weekday.label : "指定なし";
    };

    const weekDayLabel = getWeekdayLabel(weekdays, optionFilters.weekday);

    const [open, setOpen] = useState<boolean>(true);

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            if (event.data.type === 'UPDATE_OPEN') {
                setOpen(event.data.open);
            }
        };

        window.addEventListener('message', handleMessage);

        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []);

    useEffect(() => {
        console.log(`Menu is now ${open ? "open" : "closed"}`);
        handleResize();
    }, [open]);

    const [boxWidth, setBoxWidth] = useState<number>(0);

    const handleResize = () => {
        const windowWidth = document.documentElement.clientWidth;
        console.log("updateDimensions-document.documentElement.clientWidth=", document.documentElement.clientWidth);
        if (windowWidth > 910) {
            if (open) {
                setBoxWidth(windowWidth - 320);
            } else {
                setBoxWidth(windowWidth - 128);
            }
        } else {
            setBoxWidth(windowWidth - 112);
        }
    };

    useEffect(() => {
        handleResize();
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    const caddyEmpty = optionFilters.caddie === "none" || optionFilters.caddie.length === 0;
    const emptyFilter = () => {
        setFilters({
            year: currentYear + "年度",
            month: currentMonth + "月度",
            caddie: "none",
            weekday: "3",
        });
        setOptionFilters({
            year: currentYear + "年度",
            month: currentMonth + "月度",
            caddie: "none",
            weekday: "3",
        });
        fetchData();
    };
    const [caddyName, setCaddyName] = useState<string>("");
    const handleSelectData = async () => {
        const nowCaddyId = parseInt(filters.caddie, 10);
        const foundCaddy = caddies.find((caddy) => caddy.id === nowCaddyId);
        setCaddyName(foundCaddy ? foundCaddy.caddy_name : "");
        setOptionFilters(filters);
        await fetchCaddyEvaluateData(DateUtils.initAMonthAgo(filters.year.replace("年度", ""), filters.month.toString().replace("月度", ""))
            , DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
            ), Number(filters.weekday), filters.caddie);
        //get char data
        if (filters.caddie !== "none" && filters.caddie.length > 0) {
            await getChatData(DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
            ), Number(filters.weekday), filters.caddie, true);
        }
    };

    const [openDialog, setOpenDialog] = useState(false);
    const handleClose = () => {
        setOpenDialog(false);
    };
    // CSVファイル出力
    const exportCSVFile = async () => {
        if (fixedEvaluations.length > 0 && questions.length > 0) {
            let cvsContent = CommonUtils.getCaddyNameFromLocalStorage() + "番号,名前";
            
            // ヘッダー追加
            questions.map(question => {
                const questionName = (question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場") + "設問" + question.index;
                cvsContent += "," + questionName + "回答数";
                cvsContent += "," + questionName + "平均点";
            });

            // キャディ評価追加
            fixedEvaluations.map((data: any) => {
                cvsContent += "\n" + (data.index == 0 ? "-" : data.index);
                cvsContent += "," + (data.caddyName || "対象外");
                data.tableDataQuArray.map((question: any) => {
                    cvsContent += "," + ((!data.caddyName || data.caddyName === "") && question.type === 1 ? "対象外" : question.count.toString())
                    cvsContent += "," + ((!data.caddyName || data.caddyName === "") && question.type === 1 ? "-" : (question.average.toString() === "0" ? '-' : question.average.toString()))
                });
            });

            const blob = new Blob(["\ufeff" + cvsContent], { type: "text/csv;charset=utf-8;" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", CommonUtils.getCaddyNameFromLocalStorage() + "評価一覧.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } else {
            setOpenDialog(true);
        }
    };

    const getCaddyName = (caddyNo: String) => {
        const caddy = caddies.find(caddy => caddy.caddy_no === caddyNo);
        if (caddy) {
            return caddy.caddy_name;
        }
        return "";
    }

    const getCaddyIndex = (caddyNo: String) => {
        const index = caddies.findIndex(caddy => caddy.caddy_no === caddyNo);
        return index;
    }

    const getCaddyNo = (caddyName: String) => {
        const caddy = caddies.find(caddy => caddy.caddy_name === caddyName);
        if (caddy) {
            return caddy.caddy_no;
        }
        return "";
    }

    const getCaddyList = () => {
        return [
            { id: 0, caddy_no: "none", caddy_name: "\u00A0" },
            ...caddies
        ].filter(caddie => !(caddie.caddy_no === "0" && caddie.caddy_name === ""));
    }

    //Option component
    const Option = () => {
        return (
            <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white" }}>
                <div className="flex items-center space-x-4">
                    <div
                        style={{
                            borderLeft: `4px solid ${theme.palette.primary.main}`,
                            height: "16px",
                        }}
                    ></div>
                    <Typography
                        noWrap
                        component="span"
                        sx={{ fontWeight: "bold" }}
                        color="primary"
                    >
                        表示条件設定
                    </Typography>
                </div>
                <div className="flex flex-wrap items-center">
                    <div className="w-full sm:w-auto mr-14">
                        <DataForm
                            caddies={getCaddyList().map((caddie) => ({
                                label: caddie.caddy_name,
                                value: caddie.caddy_no,
                            }))}
                            weekdays={weekdays}
                            filters={filters}
                            setFilters={setFilters}
                        />
                    </div>
                    <div className="w-full sm:w-auto flex space-x-4 mt-4">
                        <Button
                            variant="outlined"
                            sx={{ fontWeight: "bold" }}
                            onClick={emptyFilter}
                        >
                            リセット
                        </Button>
                        <Button
                            variant="outlined"
                            sx={{ fontWeight: "bold" }}
                            onClick={handleSelectData}
                        >
                            表示
                        </Button>
                    </div>
                </div>
            </Box>
        )
    }

    //all fun
    function getCaddySurvey(caddyNo: String, date: String) {
        if (caddyList && caddyList.length > 0) {
            for (const monthData of caddyList) {
                if (monthData.month === date) {
                    for (const surveyData of monthData.survey) {
                        if (surveyData.caddy_id.toString() === caddyNo.toString()) {
                            return surveyData.survey;
                        }
                    }
                }
            }
        }
        return null;
    }

    const getCaddyCount = (questionId: number, caddyNo: String, date: String): number => {
        let caddySurvey = getCaddySurvey(caddyNo, date)
        if (caddySurvey) {
            for (const survey of caddySurvey) {
                if (survey.id.toString() === questionId.toString()) {
                    return survey.answers.reduce((sum, answer) => sum + answer.count, 0);
                }
            }
        }
        return 0;
    }

    const getCaddyTotal = (questionId: number, caddyNo: String, date: String): number => {
        let caddySurvey = getCaddySurvey(caddyNo, date)
        if (caddySurvey) {
            const surveyItem = caddySurvey.find(s => s.id === questionId);
            if (surveyItem) {
                let totalScore = 0;
                surveyItem.answers.forEach(answer => {
                    const data1Item = evaluationsList.find(d => d.id === answer.id);
                    if (data1Item) {
                        totalScore += data1Item.score * answer.count;
                    }
                });
                return totalScore;
            }
        }

        return 0;
    }
    const fixedEvaluations = caddies.map((caddy, index) => {
        const caddyNo = caddy.caddy_no === "" ? "0" : caddy.caddy_no;
        let tableDataQuArray: any = [];
        questions.forEach((question, questionIndex) => {
            if (caddyNo === "0") {
                const count1Empty = getCaddyCount(question.id, "", starDate);
                const total1Empty = getCaddyTotal(question.id, "", starDate);

                const count1Zero = getCaddyCount(question.id, "0", starDate);
                const total1Zero = getCaddyTotal(question.id, "0", starDate);

                const countEmpty = getCaddyCount(question.id, "", endDate);
                const totalEmpty = getCaddyTotal(question.id, "", endDate);

                const countZero = getCaddyCount(question.id, "0", endDate);
                const totalZero = getCaddyTotal(question.id, "0", endDate);

                const count1 = count1Empty + count1Zero;
                const total1 = total1Empty + total1Zero;
                const average1 = MathUtils.calculateAverage(total1, count1);

                const count = countEmpty + countZero;
                const total = totalEmpty + totalZero;
                const average = MathUtils.calculateAverage(total, count);

                const difference = new Decimal(average).minus(average1).toFixed(2);

                tableDataQuArray.push(TableDataQu(
                    questionIndex,
                    count,
                    total,
                    average,
                    Number(difference),
                    question.type
                ));
            } else {
                const count1 = getCaddyCount(question.id, caddyNo, starDate)
                const total1 = getCaddyTotal(question.id, caddyNo, starDate)
                const average1 = MathUtils.calculateAverage(total1, count1)

                const count = getCaddyCount(question.id, caddyNo, endDate)
                const total = getCaddyTotal(question.id, caddyNo, endDate)
                const average = MathUtils.calculateAverage(total, count)

                const difference = new Decimal(average).minus(average1).toFixed(2);

                tableDataQuArray.push(TableDataQu(
                    questionIndex,
                    count,
                    total,
                    average,
                    Number(difference),
                    question.type
                ));
            }
        });
        return tableData(caddyNo, caddy.caddy_name, tableDataQuArray);
    });

    const handleCaddyNameClick = async (caddyName: string) => {
        const caddyNo = getCaddyNo(caddyName);
        setFilters((prevFilters) => ({
            ...prevFilters,
            caddie: caddyNo,
        }));
        setOptionFilters((prevFilters) => ({
            ...prevFilters,
            caddie: caddyNo,
        }));
        await fetchCaddyEvaluateData(DateUtils.initAMonthAgo(filters.year.replace("年度", ""), filters.month.toString().replace("月度", ""))
            , DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
            ), Number(filters.weekday), caddyNo);
        await getChatData(DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
        ), Number(filters.weekday), caddyNo, true);
    };

    //all component
    const All = () => {
        return (
            <Box
                borderRadius={2}
                display="flex"
                flexDirection="column"
                sx={{
                    flexGrow: 1,
                    p: 2,
                    marginTop: "20px",
                    backgroundColor: "white",
                }}
            >
                <div className="flex justify-between items-center mb-4">
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            flexWrap: "wrap",
                        }}
                    >
                        <Typography
                            className="title-large-text"
                            style={{ paddingRight: "5px", paddingLeft: "10px" }}
                            color="primary"
                        >
                            {caddyNameType}の個別評価
                        </Typography>
                        <Typography
                            className="title-small-text"
                            style={{ paddingRight: "0px" }}
                            color="primary"
                        >
                            {optionFilters.year.replace("年度", "") +
                                "年" +
                                optionFilters.month.replace("月度", "") +
                                "月度(" +
                                weekDayLabel +
                                ")"}
                        </Typography>
                    </div>
                    <Button
                        variant="outlined"
                        onClick={exportCSVFile}
                        sx={{ fontWeight: "bold", className: "text-right" }}
                    >
                        CSV出力
                    </Button>
                </div>
                <Divider sx={{ bgcolor: "primary.main" }} />
                <Box flex={1} overflow="auto">
                    {widthSize ?
                        <TableContainer style={{ width: boxWidth }} component={Paper}>
                            <CaddyTable
                                caddies={caddies}
                                questions={questions}
                                data={fixedEvaluations}
                                onCaddyNameClick={handleCaddyNameClick}
                            ></CaddyTable>
                        </TableContainer> :
                        <TableContainer style={{ width: boxWidth }} component={Paper}>
                            <CaddyTableForMobile
                                questions={questions}
                                data={fixedEvaluations}
                                onCaddyNameClick={handleCaddyNameClick}
                            ></CaddyTableForMobile>
                        </TableContainer>}
                </Box>
            </Box>
        )
    }

    //one fun
    const generateInitialValues = (data: Question[], isRefresh: boolean) => {
        const result: { [key: string]: boolean } = {};
        data.forEach(item => {
            const key = item.type === 1 ? caddyNameType + `設問${item.index}` : `ゴルフ場設問${item.index}`;
            result[key] = isRefresh ? isRefresh : (selectedValues[key] !== undefined ? selectedValues[key] : true);
        });

        return result;
    };

    const getStatisticalListAverage = (key: any, month: any, questionsData: any, statisticalList: any, evaluations: Evaluation[]) => {
        if (statisticalList) {
            const id = getStatisticalListId(key, questionsData)
            const count = getCharCount(id, month, statisticalList)
            const total = getCharTotal(id, month, statisticalList, evaluations)
            const average = MathUtils.calculateAverage(total, count)
            return average
        } else {
            return 0
        }
    }
    const getStatisticalListId = (key: any, questionsData: Question[]) => {
        let type, index;

        if (key.startsWith('キャディ設問')) {
            type = 1;
            index = parseInt(key.replace('キャディ設問', ''), 10);
        } else if (key.startsWith('コースアテンダント設問')) {
            type = 1;
            index = parseInt(key.replace('コースアテンダント設問', ''), 10);
        } else if (key.startsWith('ゴルフ場設問')) {
            type = 2;
            index = parseInt(key.replace('ゴルフ場設問', ''), 10);
        } else {
            return 0;
        }

        const question = questionsData.find(q => q.type === type && q.index === index);
        return question ? question.id : 0;
    }
    const getCharCount = (id: any, month: any, statisticalList: any): number => {
        let caddySurvey = getCharCaddySurvey(month, statisticalList)
        if (caddySurvey) {
            for (const survey of caddySurvey) {
                if (survey.id.toString() === id.toString()) {
                    return survey.answers.reduce((sum: any, answer: any) => sum + answer.count, 0);
                }
            }
        }
        return 0;
    }

    const getCharTotal = (id: any, month: any, statisticalList: any, evaluations: Evaluation[]): number => {
        let caddySurvey: SurveyItem[] = getCharCaddySurvey(month, statisticalList)
        if (caddySurvey) {
            const surveyItem = caddySurvey.find(s => s.id === id);
            if (surveyItem) {
                let totalScore = 0;
                surveyItem.answers.forEach(answer => {
                    const data1Item = evaluations.find(d => d.id === answer.id);
                    if (data1Item) {
                        totalScore += data1Item.score * answer.count;
                    }
                });
                return totalScore;
            }
        }

        return 0;
    }

    function getCharCaddySurvey(date: String, statisticalList: any) {
        for (const monthData of statisticalList) {
            if (monthData.month === date) {
                return monthData.survey;
            }
        }
        return null;
    }

    //one component
    const One = () => {
        return (
            <Box
                borderRadius={2}
                sx={{ p: 2, marginTop: "20px", backgroundColor: "white" }}
            >
                <div
                    style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "10px",
                        marginBottom: "5px",
                        flexWrap: "wrap",
                    }}
                >
                    <Typography
                        sx={{
                            fontWeight: "bold",
                            borderBottom: "1px solid primary.main",
                        }}
                        style={{ paddingRight: "15px", paddingLeft: "10px" }}
                        color="primary"
                        variant="h1"
                    >
                        {optionFilters.caddie} {getCaddyName(optionFilters.caddie)} の評価
                    </Typography>
                    <Typography
                        className="title-small-text"
                        style={{ paddingRight: "0px" }}
                        color="primary"
                    >
                        {`${optionFilters.year.replace("年度", "")}年${optionFilters.month.replace("月度", "")}月度(${weekDayLabel})`}
                    </Typography>

                </div>
                <Divider sx={{ bgcolor: "primary.main" }} />
                <Box flex={1} overflow="auto">
                    {widthSize ?
                        <TableContainer style={{ width: boxWidth }} component={Paper}>
                            <CaddyQuesTable
                                caddyid={optionFilters.caddie}
                                caddies={caddies}
                                questions={questions}
                                data={fixedEvaluations}
                                caddyindex={getCaddyIndex(optionFilters.caddie)}
                            ></CaddyQuesTable>
                        </TableContainer> :
                        <TableContainer style={{ width: boxWidth }} component={Paper} >
                            <CaddyQuesTableForMobile
                                caddyid={optionFilters.caddie}
                                caddies={caddies}
                                questions={questions}
                                data={fixedEvaluations}
                                caddyindex={getCaddyIndex(optionFilters.caddie)}
                            ></CaddyQuesTableForMobile>
                        </TableContainer>}
                </Box>

            </Box>
        )
    }

    //middin fun
    function getMiddleDataCount(questionId: number, data: SurveyData[]) {
        let count = 0;
        if (data) {
            data.forEach(month => {
                if (month.month == endDate) {
                    month.survey.forEach(survey => {
                        survey.survey.forEach(item => {
                            if (item.id === questionId) {
                                item.answers.forEach(answer => {
                                    count += answer.count;
                                });
                            }
                        });
                    });
                }
            });
        }
        return count;
    }

    function getMiddleDataCounts(questionId: number, data: SurveyData[]) {
        let counts: { [key: string]: number } = {};
        if (data) {
            data.forEach(month => {
                if (month.month == endDate) {
                    month.survey.forEach(survey => {
                        survey.survey.forEach(item => {
                            if (item.id === questionId) {
                                item.answers.forEach(answer => {
                                    if (!counts[answer.id]) {
                                        counts[answer.id] = 0;
                                    }
                                    counts[answer.id] += answer.count;
                                });
                            }
                        });
                    });
                }
            });
        }
        return counts;
    }

    function formatScore(score: any) {
        return parseFloat(score).toString();
    }

    const middleData = questions.map((question, questionIndex) => {
        const typeMap: { [key: number]: string } = { 1: caddyNameType + '設問', 2: 'ゴルフ場設問' };
        const name = `${typeMap[question.type]}${question.index}\n${question.content}`;
        const title = `${typeMap[question.type]}${question.index}${question.content}`;

        const totalCount = getMiddleDataCount(question.id, caddyList);
        const satisfactionCounts = getMiddleDataCounts(question.id, caddyList);

        let counts: { [key: string]: string } = {};
        let totalScore = 0;

        evaluationsList.forEach(satisfaction => {
            const count = satisfactionCounts[satisfaction.id] || 0;
            const percentage = totalCount ? formatScore(((count / totalCount) * 100).toFixed(2)) : "0";
            counts[`${satisfaction.content}(${satisfaction.score}点)`] = percentage;
            totalScore += satisfaction.score * count;
        });

        // const averageScore = totalCount ? (totalScore / totalCount).toFixed(2) : "0.00";
        const averageScore = totalCount ? formatScore((totalScore / totalCount).toFixed(2)) : "0";

        return {
            name,
            title,
            ...counts,
            totalScore: averageScore
        };
    });

    function getSatisfactionContentByScore(stage: any) {
        const satisfaction = evaluationsList.find(item => item.stage === stage);
        if (satisfaction) {
            return `${satisfaction.content}(${satisfaction.score}点)`;
        } else {
            return "";
        }
    }

    const handlePreviousDateClick = async () => {
        await getChatData(DateUtils.getPreviousSixMonths(charDate), Number(filters.weekday), filters.caddie);
    };

    const handleNextDateClick = async () => {
        if (!DateUtils.isDateEqualOrAfter(charDate, currentYear + "-" + currentMonth))
            await getChatData(DateUtils.getValidDate(charDate), Number(filters.weekday), filters.caddie);
    };

    //middin component
    const CustomTooltip = ({ active, payload, label }: { active?: any, payload?: any, label?: any }) => {
        if (active && payload && payload.length) {
            return (
                <div className="middle-chart-tooltip">

                    <p className="label"
                        style={{
                            maxWidth: '200px',
                            wordWrap: 'break-word',
                            whiteSpace: 'pre-wrap',
                            marginBottom: '20px'
                        }}>
                        {`${payload[0].payload.name}`}
                    </p>
                    <p style={{ color: colors[0] }}>{`${payload[0].dataKey} : ${Math.round(Number(payload[0].value))}`}</p>
                    <p style={{ color: colors[1] }}>{`${payload[1].dataKey} : ${Math.round(Number(payload[1].value))}`}</p>
                    <p style={{ color: colors[2] }}>{`${payload[2].dataKey} : ${Math.round(Number(payload[2].value))}`}</p>
                    <p style={{ color: colors[3] }}>{`${payload[3].dataKey} : ${Math.round(Number(payload[3].value))}`}</p>
                    <p style={{ color: colors[4] }}>{`${payload[4].dataKey} : ${Math.round(Number(payload[4].value))}`}</p>
                </div>
            );
        }

        return null;
    };
    // Middle y
    const CustomYAxisTick = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        const parts = payload.value.split('\n');
        const maxLength = 15;

        let firstLine = parts[1];
        let secondLine = '';

        if (firstLine.length > maxLength) {
            firstLine = firstLine.substring(0, maxLength);
            secondLine = parts[1].substring(maxLength);
            if (secondLine.length > maxLength) {
                secondLine = secondLine.substring(0, maxLength - 1) + "...";
            }
        }

        return (
            <text x={0} y={y - 30} fill="#364985" textAnchor="start" dy={16} style={{ fontSize: 14, width: 100 }}>
                {parts[0]}
                {firstLine && <tspan x={0} dy="1em" alignmentBaseline="middle">{firstLine}</tspan>}
                {secondLine && <tspan x={0} dy="1em" alignmentBaseline="middle">{secondLine}</tspan>}
            </text>
        );
    };
    // Middle y 2
    const CustomYAxisTick2 = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        return (
            <text x={0} y={y - 30} fill="#666" textAnchor="start" dy={16} style={{ fontSize: 14, width: 100 }}>
            </text>
        );
    };

    const [yAxisWidth, setYAxisWidth] = useState<number>(35);

    useEffect(() => {
        if (evaluationsList.length > 0) {
            const maxScore = Math.max(...evaluationsList.map(evaluation => evaluation.score));

            const widthMap = [
                { limit: 9999, width: 70 },
                { limit: 999, width: 60 },
                { limit: 99, width: 50 },
                { limit: 9, width: 40 },
            ];

            const calculatedWidth = widthMap.find(({ limit }) => maxScore > limit)?.width || 35;
            setYAxisWidth(calculatedWidth);
        }
    }, [evaluationsList]);

    const MiddleChart = () => {
        return (
            <Box borderRadius={2}
                sx={{ backgroundColor: "white", marginTop: "20px", alignItems: "top" }}>
                <div className='middle-chart-bg'>
                    <ResponsiveContainer width={'100%'} height={widthSize
                        ? 70 * middleData.length + 60
                        : 80 * middleData.length + 60
                    }>
                        <BarChart
                            layout="vertical"
                            data={middleData}
                            margin={{
                                top: 0, right: 20, left: 20, bottom: 0,
                            }}
                            barSize={30}>
                            {widthSize ?
                                <CartesianGrid stroke="#000000" strokeDasharray="0" />
                                :
                                <CartesianGrid stroke="#000000" strokeDasharray="0" vertical={false} />
                            }

                            {widthSize &&
                                <XAxis
                                    type="number"
                                    tickLine={false}
                                    tickCount={10}
                                    interval={0}
                                    domain={[0, 100]}
                                    ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
                                    label={{
                                        value: '(%)', position: 'insideBottomLeft', offset: 0,
                                        dy: -10,
                                        dx: -40,
                                        textAnchor: 'middle',
                                        style: {
                                            fontSize: '15px',
                                            fill: '#000',
                                            // fontWeight: 'bold',
                                        }
                                    }}
                                />}
                            {!widthSize &&
                                <XAxis
                                    type="number"
                                    tickLine={false}
                                    domain={[0, 100]}
                                    label={{
                                        value: '(%)', position: 'insideBottomLeft', offset: 0,
                                        dy: -12,
                                        dx: -30,
                                        textAnchor: 'middle',
                                        style: {
                                            fontSize: '15px',
                                            fill: '#000',
                                            // fontWeight: 'bold',
                                        }
                                    }}
                                />
                            }

                            {widthSize ? <YAxis type="category" tickLine={false}
                                width={200}
                                dataKey="name"
                                tick={<CustomYAxisTick />}
                            /> : <YAxis type="category" tickLine={false}
                                width={10}
                                dataKey="name"
                                tick={<CustomYAxisTick2 />}
                            />}

                            <YAxis
                                type="category"
                                tickLine={false}
                                orientation="right"
                                yAxisId={1}
                                dataKey="totalScore"
                                width={yAxisWidth}
                                tick={{ fontSize: '14px', fontWeight: 'bold', fill: '#ff0000' }}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend layout={widthSize ? 'horizontal' : 'vertical'} />
                            <Bar dataKey={getSatisfactionContentByScore(1)} stackId="a" fill={colors[0]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(1)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                                {!widthSize && <LabelList
                                    dataKey="title"
                                    position="insideLeft"
                                    dy={-22}
                                    dx={-5}
                                    fill="#364985"
                                    fontSize={12}
                                    formatter={(value: any) => value.length > 15 ? value.substring(0, 15) + '...' : value}
                                />}
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(2)} stackId="a" fill={colors[1]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(2)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(3)} stackId="a" fill={colors[2]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(3)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(4)} stackId="a" fill={colors[3]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(4)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(5)} stackId="a" fill={colors[4]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(5)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>

                    {widthSize ?
                        <text
                            x="100%"
                            textAnchor="end"
                            style={{ position: 'absolute', top: -10, right: 25, fontSize: '13px', color: '#000' }}>
                            (平均点)
                        </text>
                        :
                        <text
                            x="100%"
                            textAnchor="end"
                            style={{ position: 'absolute', top: -10, right: 10, fontSize: '13px', color: '#000' }}>
                            (平均点)
                        </text>
                    }
                </div>
            </Box>
        )
    }
    //bottom
    const handleChange = (name: any) => (event: any) => {
        setSelectedValues({
            ...selectedValues,
            [name]: event.target.checked,
        });
    };
    const CheckboxLabel = ({ label, checked, onChange, value, sx = {} }: {
        label: string,
        checked: boolean,
        onChange: (event: any) => void,
        value: string,
        sx: {}
    }) => {
        return (
            <Typography
                component="div"
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: checked ? '#364985' : '#000',
                    fontSize: '18px',
                    fontWeight: checked ? 'bold' : 'normal',
                    ...sx
                }}
            >
                <FormControlLabel
                    label={label}
                    control={
                        <Checkbox
                            checked={checked}
                            onChange={onChange}
                            value={value}
                            sx={{ padding: 0, }}
                        />
                    }
                />
            </Typography>
        );
    };

    const CustomXAxisTickToTop = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        if (!lineData) {
            return
        }
        const firstItemName = lineData[0] ? lineData[0].name : '';
        const parts = payload.value.split('年');
        // const hasYear = parts[1] && /^\d{4}$/.test(parts[1]);
        const containsJanuary = payload.value.includes('年1月');
        const fontSize = 14;
        const isEqualToFirstItemName = payload.value === firstItemName;

        if (containsJanuary || isEqualToFirstItemName) {
            return (
                <text x={x} y={y} fill="#666" textAnchor="middle" dy={16} style={{ fontSize }}>
                    {parts[1]}
                    <tspan x={x - 5} dy="1em" alignment-baseline="middle"> {parts[0] + "年"}</tspan>
                </text>
            );
        }
        return (
            <text x={x} y={y} fill="#666" textAnchor="middle" dy={16} style={{ fontSize }}>
                {parts[1]}
                {/*{payload.value}*/}
            </text>
        );
    };

    const CustomYAxisTickToTop = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        const value = String(payload.value);
        const words = value.match(/.{1,6}/g) || [];
        return (
            <text x={x} y={y} fill="#666" textAnchor="end" style={{ fontSize: '15px', fontFamily: 'Meiryo' }}>
                {words.map((word: string, index: number) => (
                    <tspan x={x} dy={index === 0 ? 0 : 15} key={index}>
                        {word}
                    </tspan>
                ))}
            </text>
        );
    };

    const BottomChart = () => (
        <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: "20px" }}>
            <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                <Typography
                    noWrap
                    variant="h1"
                    sx={{ fontWeight: "bold", marginLeft: '16px' }}
                    color="primary"
                >
                    これまでの評価平均
                </Typography>
                <div className="flex items-center">
                    <Typography
                        color="primary"
                        onClick={handlePreviousDateClick}
                        style={{
                            width: "22px",
                            height: "22px",
                            color: 'white',
                            fontSize: '13px',
                            backgroundColor: "#304a89",
                            borderRadius: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: 0,
                            marginLeft: "10px",
                        }}>

                        ◀
                    </Typography>
                    <Typography
                        style={{ paddingLeft: "10px", paddingRight: '10px', fontSize: '14px', }}
                        color="primary">
                        {DateUtils.getDaysOfPreviousYearToCurrentMonth(charDate)}
                    </Typography>
                    <Typography
                        color="primary"
                        style={{
                            width: "22px",
                            height: "22px",
                            color: 'white',
                            fontSize: '13px',
                            backgroundColor: DateUtils.isDateEqualOrAfter(charDate, currentYear + "-" + currentMonth) ? 'gray' : "#304a89",
                            borderRadius: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: 0,
                        }}
                        onClick={handleNextDateClick}
                    >
                        ▶
                    </Typography>
                </div>
                <Box className="top-checkboxlabel">
                    {selectedValues && Object.keys(selectedValues).map((key) => (
                        <CheckboxLabel
                            key={key}
                            label={key}
                            checked={selectedValues[key]}
                            onChange={handleChange(key)}
                            value={key}
                            sx={{}}
                        />
                    ))}
                </Box>
            </div>
            <Divider sx={{ bgcolor: "primary.main", marginTop: '20px' }} />
            <div>
                <div className="top-chart-title">
                    <span style={{ fontSize: '14px', color: '#364985' }}>平均(点)</span>
                    {/*fontWeight: 'bold'*/}
                </div>
                <div className="top-chart-bg">
                    <ResponsiveContainer width={'100%'} height={'100%'}>
                        <AreaChart data={lineData}
                            margin={{
                                top: 20, right: 0, left: 0, bottom: 0,
                            }}
                        >
                            <CartesianGrid stroke="#364985" vertical={false} strokeDasharray="0" strokeWidth={1} />
                            {widthSize ?
                                <XAxis height={50} dataKey="name" axisLine={{ stroke: 'black', strokeWidth: 2 }}
                                    tickLine={false} allowDuplicatedCategory={false}
                                    padding={{ left: 10, right: 10 }} tick={<CustomXAxisTickToTop />} angle={70}
                                    interval={0} />
                                :
                                <XAxis height={50} dataKey="name" axisLine={{ stroke: 'black', strokeWidth: 2 }}
                                    tickLine={false} allowDuplicatedCategory={false}
                                    padding={{ left: 10, right: 10 }} tick={<CustomXAxisTickToTop />} angle={70} />
                            }

                            <YAxis
                                axisLine={{ stroke: 'black', strokeWidth: 0 }}
                                tickLine={false}
                                tickCount={6}
                                width={60}
                                padding={{ top: 0 }}
                                allowDecimals={false}
                                tick={<CustomYAxisTickToTop />}
                            />
                            <Tooltip />
                            <Legend iconType="square" layout={widthSize ? 'horizontal' : 'vertical'} />
                            {selectedValues && Object.entries(selectedValues).map(([key, value], index) => (
                                value &&
                                <Area
                                    key={key}
                                    type='monotone'
                                    dataKey={key}
                                    strokeWidth={4}
                                    stroke={colors[index]}
                                    fill="#EFF0F7"
                                    fillOpacity={0.3}
                                    animationDuration={0}
                                />
                            ))}

                        </AreaChart>
                    </ResponsiveContainer>
                </div>
            </div>
        </Box>
    );
    // **********************
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box
                id="box-to-watch"
                flex={1}
                overflow="auto"
                sx={{ display: "flex", flexDirection: "column" }}
            >
                <Breadcrumb items={breadcrumbItems} />

                {
                    ((questingsApi && evaluationApi && !caddiesApi)) ? (
                        <ErrorHint isNull={true} screenWidth={widthSize}
                        />)
                        : ((!questingsApiSuccess || !evaluationApiSuccess || !caddiesApiSuccess) || (questingsApi && evaluationApi && !caddiesApi)) ? (
                            <ErrorHint isAll={true} screenWidth={widthSize}
                                onButtonClick={() => handleErrorButtonClick("all")} />
                        )
                            : ((!questingsApi || !evaluationApi) && questingsApiSuccess) ? (
                                <ErrorHint jumpSetting={true} screenWidth={widthSize} />
                            ) : (
                                <div>
                                    <Option />
                                    {getOptionError() ? (
                                        <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                                            <ErrorHint screenWidth={widthSize} onButtonClick={() => handleErrorButtonClick("option")} />
                                        </Box>
                                    ) : (caddyEmpty) ? (
                                        <All />
                                    ) : (
                                        <div>
                                            <One />
                                            <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                                                <div
                                                    style={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        padding: "10px",
                                                        marginBottom: "5px",
                                                        flexWrap: "wrap",
                                                    }}
                                                >
                                                    <Typography
                                                        sx={{
                                                            fontWeight: "bold",
                                                            borderBottom: "1px solid primary.main",
                                                        }}
                                                        style={{ paddingRight: "15px", paddingLeft: "10px" }}
                                                        color="primary"
                                                        variant="h1"
                                                    >
                                                        {optionFilters.caddie} {getCaddyName(optionFilters.caddie)} の評価分布
                                                    </Typography>
                                                    <Typography
                                                        className="title-small-text"
                                                        style={{ paddingRight: "0px" }}
                                                        color="primary">
                                                        {`${optionFilters.year.replace("年度", "")}年${optionFilters.month.replace("月度", "")}月度(${weekDayLabel})`}
                                                    </Typography>
                                                </div>
                                                <Divider sx={{ bgcolor: "primary.main" }} />
                                                <MiddleChart />
                                            </Box>
                                            <BottomChart />
                                        </div>
                                    )}
                                </div>
                            )

                }
                <CsvErrorDialog
                    open={openDialog}
                    onClose={handleClose}
                    btnText="閉じる"
                    description="処理中にエラーが発生しました。しばらくしてからもう一度お試しください。"
                />
            </Box>
        </LocalizationProvider>
    );
};

export default Home;
