"use client";
import React, { useState, useEffect } from "react";
import {
    BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend,
    Line, ResponsiveContainer, ComposedChart, LabelList, AreaChart, Area
} from 'recharts';

import {
    Box, Typography, FormControlLabel, Checkbox, Button,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { useTheme } from "@mui/material/styles";
import Breadcrumb from "@/components/breadcrumb";
import './answer.css';
import { Caddy, Evaluation, Question, StatisticalData, SurveyData, SurveyItem } from "@/models/caddy-models";
import DataForm from "../../components/caddy/date-form";
import ErrorHint from "../../components/error-hint";
import {
    fetchCaddies,
    getCaddyEvaluate,
    getEvaluations,
    getQuestion,
    getStatistical
} from "@/api/caddy-api";
import { DateUtils } from '@/utils/date-utils';
import { MathUtils } from "@/utils/math-utils";
import colors from '../../utils/color';
import { handleAuthToken } from "../../auth/Auth"
import { CommonUtils } from '@/utils/common-utils';

const Home = () => {
    // var
    const theme = useTheme();
    const breadcrumbItems = [
        { label: "アンケート", href: "/" },
        { label: "アンケート結果" },
    ];
    const [widthSize, setWidthSize] = useState(false);
    useEffect(() => {
        handleAuthToken().then(
            (value) => {
                if (value) {
                    const handleResize = () => {
                        if (window.innerWidth < 1000) {
                            setWidthSize(false);
                        } else {
                            setWidthSize(true);
                        }
                    };

                    handleResize(); // Initialize the state based on the current window width
                    window.addEventListener("resize", handleResize);
                    return () => window.removeEventListener("resize", handleResize);
                }
            },
        );
    }, []);
    // error
    const [questingsApi, setQuestingsApi] = useState(true);
    const [questingsApiSuccess, setQuestingsApiSuccess] = useState(true);
    const [evaluationApi, setEvaluationApi] = useState(true);
    const [evaluationApiSuccess, setEvaluationApiSuccess] = useState(true);
    const [caddiesApi, setCaddiesApi] = useState(true);
    const [caddiesApiSuccess, setCaddiesApiSuccess] = useState(true);

    const [topApiSuccess, setTopApiSuccess] = useState(true);
    const [bottomApiSuccess, setBottomApiSuccess] = useState(true);
    const handleErrorButtonClick = async (type: string) => {
        if (type === "all") {
            fetchData();
        }
        if (type === "top") {
            fetchData();
        }
        if (type === "bottom") {
            await fetchCaddyEvaluateData(DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", ""))
                , DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
                ), Number(filters.weekday), filters.caddie === "none" ? "" : filters.caddie);
        }
    };
    const showData = () => {
        return (questingsApi && questingsApiSuccess && evaluationApi && caddiesApi && evaluationApiSuccess && caddiesApiSuccess)
    }
    //api -var
    const [caddies, setCaddies] = useState<Caddy[]>([]);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [selectedValues, setSelectedValues] = useState<{ [key: string]: boolean }>({});
    const [statisticalList, setStatisticalList] = useState<StatisticalData[]>([]);
    const [evaluationsList, setEvaluationsList] = useState<Evaluation[]>([]);
    const [caddyList, setCaddyList] = useState<SurveyData[]>([]);
    //var
    const [lineData, setLineData] = useState<{ name: string; }[]>([]);

    const [starDate, setStarDate] = useState<string>("")
    const [endDate, setEndDate] = useState<string>("")
    // api
    const fetchData = async () => {
        try {
            const caddiesData = await fetchCaddies();
            setCaddiesApi(true)
            setCaddiesApiSuccess(true)
            if (caddiesData) {
                setCaddies(caddiesData);
            } else {
                setCaddiesApi(false);
            }
        } catch (error) {
            setCaddiesApiSuccess(false);
        }
        let questionsData: any;
        try {
            questionsData = await getQuestion();
            setQuestingsApi(true)
            setQuestingsApiSuccess(true);
            if (questionsData) {
                setQuestions(questionsData);
            } else {
                setQuestingsApi(false)
            }
        } catch (error) {
            setQuestingsApiSuccess(false);
        }
        let evaluations: any;
        try {
            evaluations = await getEvaluations();
            setEvaluationApi(true)
            setEvaluationApiSuccess(true);
            if (evaluations) {
                setEvaluationsList(evaluations);
            } else {
                setEvaluationApi(false)
            }
        } catch (error) {
            setEvaluationApiSuccess(false);
        }

        await fetchCaddyEvaluateData(DateUtils.getMonth(), DateUtils.getMonth(), 3);

        let statistical: any;
        try {
            statistical = await getStatistical(DateUtils.getTwoYearsAgoMonth(), DateUtils.getMonth(), 3);
            setTopApiSuccess(true)
            if (statistical) setStatisticalList(statistical)
        } catch (error) {
            setTopApiSuccess(false)
        }

        const initialSelectedValues = generateInitialValues(questionsData);
        //top chart data
        setSelectedValues(initialSelectedValues)
        const lineData = DateUtils.getLineData();
        const updatedLineData = lineData.map(month => ({
            ...month,
            ...Object.keys(initialSelectedValues).reduce((acc: { [key: string]: number }, key: string) => {
                acc[key] = getStatisticalListAverage(key, DateUtils.formatDateString(month.name)
                    , questionsData, statistical, evaluations);
                return acc;
            }, {})
        }));
        setLineData(updatedLineData);
    };
    const fetchCaddyEvaluateData = async (startMonth: string, endMonth: string, weekday: number, caddy_id?: string) => {
        try {
            setCaddyList([])
            const caddyData = await getCaddyEvaluate(startMonth, endMonth, weekday, caddy_id);
            setBottomApiSuccess(true)
            setStarDate(startMonth)
            setEndDate(endMonth)
            if (caddyData) {
                setCaddyList(caddyData);
            }
        } catch (error) {
            setBottomApiSuccess(false)
        }
    };
    useEffect(() => {
        fetchData();

    }, []);
    //api fun
    const getStatisticalListAverage = (key: any, month: any, questionsData: any, statisticalList: any, evaluations: any) => {
        if (statisticalList) {
            const id = getStatisticalListId(key, questionsData)
            const count = getStatisticalCount(id, month, statisticalList)
            const total = getStatisticalTotal(id, month, statisticalList, evaluations)
            const average = MathUtils.calculateAverage(total, count)
            return average
        } else {
            return 0
        }
    }

    const getStatisticalListId = (key: any, questionsData: Question[]) => {
        let type, index;
        if (key.startsWith('キャディ設問')) {
            type = 1;
            index = parseInt(key.replace('キャディ設問', ''), 10);
        } else if (key.startsWith('コースアテンダント設問')) {
            type = 1;
            index = parseInt(key.replace('コースアテンダント設問', ''), 10);
        } else if (key.startsWith('ゴルフ場設問')) {
            type = 2;
            index = parseInt(key.replace('ゴルフ場設問', ''), 10);
        } else {
            return 0;
        }

        const question = questionsData.find(q => q.type === type && q.index === index);
        return question ? question.id : 0;
    }

    const getStatisticalCount = (id: any, month: any, statisticalList: any): number => {
        let caddySurvey = getCaddySurvey(month, statisticalList)
        if (caddySurvey) {
            for (const survey of caddySurvey) {
                if (survey.id.toString() === id.toString()) {
                    return survey.answers.reduce((sum: any, answer: any) => sum + answer.count, 0);
                }
            }
        }
        return 0;
    }

    const getStatisticalTotal = (id: any, month: any, statisticalList: any, evaluations: Evaluation[]): number => {
        let caddySurvey: SurveyItem[] = getCaddySurvey(month, statisticalList)
        if (caddySurvey) {
            const surveyItem = caddySurvey.find(s => s.id === id);
            if (surveyItem) {
                let totalScore = 0;
                surveyItem.answers.forEach(answer => {
                    const data1Item = evaluations.find(d => d.id === answer.id);
                    if (data1Item) {
                        totalScore += data1Item.score * answer.count;
                    }
                });
                return totalScore;
            }
        }

        return 0;
    }

    function getCaddySurvey(date: String, statisticalList: any) {
        for (const monthData of statisticalList) {
            if (monthData.month === date) {
                return monthData.survey;
            }
        }
        return null;
    }

    const generateInitialValues = (data: any) => {
        const result: { [key: string]: boolean } = {};
        if (data == undefined) {
            return result;
        } else {
            const questions = data as Question[];
            questions.forEach(item => {
                if (item.type === 1) {
                    result[CommonUtils.getCaddyNameFromLocalStorage() + `設問${item.index}`] = true;
                } else if (item.type === 2) {
                    result[`ゴルフ場設問${item.index}`] = true;
                }
            });
        }
        return result;
    };

    function getMiddleDataCount(questionId: number, data3: SurveyData[]) {
        let count = 0;
        if (data3) {
            data3.forEach(month => {
                month.survey.forEach(survey => {
                    survey.survey.forEach(item => {
                        if (item.id === questionId) {
                            item.answers.forEach(answer => {
                                count += answer.count;
                            });
                        }
                    });
                });
            });
        }
        return count;
    }

    function getMiddleDataCounts(questionId: number, data3: SurveyData[]) {
        let counts: { [key: string]: number } = {};
        if (data3) {
            data3.forEach(month => {
                month.survey.forEach(survey => {
                    survey.survey.forEach(item => {
                        if (item.id === questionId) {
                            item.answers.forEach(answer => {
                                if (!counts[answer.id]) {
                                    counts[answer.id] = 0;
                                }
                                counts[answer.id] += answer.count;
                            });
                        }
                    });
                });
            });
        }
        return counts;
    }

    function formatScore(score: any) {
        return parseFloat(score).toString();
    }

    // top fun
    const handleChange = (name: any) => (event: any) => {
        setSelectedValues({
            ...selectedValues,
            [name]: event.target.checked,
        });
    };
    // Top component
    const CheckboxLabel = ({ label, checked, onChange, value, sx = {} }: {
        label: string,
        checked: boolean,
        onChange: (event: any) => void,
        value: string,
        sx: {}
    }) => {
        return (
            <Typography
                component="div"
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: checked ? '#364985' : '#000',
                    fontSize: '18px',
                    fontWeight: checked ? 'bold' : 'normal',
                    ...sx
                }}
            >
                <FormControlLabel
                    label={label}
                    control={
                        <Checkbox
                            checked={checked}
                            onChange={onChange}
                            value={value}
                            sx={{ padding: 0, }}
                        />
                    }
                />
            </Typography>
        );
    };
    //top x
    const CustomXAxisTickToTop = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        if (!lineData) {
            return
        }
        const firstItemName = lineData[0] ? lineData[0].name : '';
        const parts = payload.value.split('年');
        // const hasYear = parts[1] && /^\d{4}$/.test(parts[1]);
        const containsJanuary = payload.value.includes('年1月');
        const fontSize = 14;
        const isEqualToFirstItemName = payload.value === firstItemName;

        if (containsJanuary || isEqualToFirstItemName) {
            return (
                <text x={x} y={y} fill="#666" textAnchor="middle" dy={16} style={{ fontSize }}>
                    {parts[1]}
                    <tspan x={x - 5} dy="1em" alignment-baseline="middle"> {parts[0] + "年"}</tspan>
                </text>
            );
        }
        return (
            <text x={x} y={y} fill="#666" textAnchor="middle" dy={16} style={{ fontSize }}>
                {parts[1]}
                {/*{payload.value}*/}
            </text>
        );
    };
    const CustomYAxisTickToTop = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        const value = String(payload.value);
        const words = value.match(/.{1,6}/g) || [];
        return (
            <text x={x} y={y} fill="#666" textAnchor="end" style={{ fontSize: '15px', fontFamily: 'Meiryo' }}>
                {words.map((word: string, index: number) => (
                    <tspan x={x} dy={index === 0 ? 0 : 15} key={index}>
                        {word}
                    </tspan>
                ))}
            </text>
        );
    };

    const TopChart = (props: any) => (
        <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white" }}>
            <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                <div
                    style={{
                        borderLeft: `4px solid ${theme.palette.primary.main}`,
                        height: "16px",
                        flexShrink: 0, // Prevent item from shrinking
                    }}
                ></div>
                <Typography
                    noWrap
                    component="span"
                    sx={{ fontWeight: "bold", marginLeft: '16px' }}
                    color="primary"
                >
                    直近2年間の平均評価推移
                </Typography>
                {topApiSuccess &&
                    <Box className="top-checkboxlabel">
                        {selectedValues && Object.keys(selectedValues).map((key) => (
                            <CheckboxLabel
                                key={key}
                                label={key}
                                checked={selectedValues[key]}
                                onChange={handleChange(key)}
                                value={key}
                                sx={{}}
                            />
                        ))}
                    </Box>}
            </div>
            {!topApiSuccess && <ErrorHint onButtonClick={() => handleErrorButtonClick("top")} screenWidth={widthSize} />}
            {topApiSuccess &&
                <div>
                    <div className="top-chart-title">
                        <span style={{ fontSize: '14px', color: '#364985' }}>平均(点)</span>
                        {/*fontWeight: 'bold'*/}
                    </div>
                    <div className="top-chart-bg">
                        <ResponsiveContainer width={'100%'} height={'100%'}>
                            <AreaChart data={lineData}
                                margin={{
                                    top: 20, right: 0, left: 0, bottom: 0,
                                }}
                            >
                                {/* 去掉竖线虚线，只保留横向实线 */}
                                <CartesianGrid stroke="#364985" vertical={false} strokeDasharray="0" strokeWidth={1} />
                                {widthSize ?
                                    <XAxis height={50} dataKey="name" axisLine={{ stroke: 'black', strokeWidth: 2 }}
                                        tickLine={false} allowDuplicatedCategory={false}
                                        padding={{ left: 10, right: 10 }} tick={<CustomXAxisTickToTop />} angle={70}
                                        interval={0} />
                                    :
                                    <XAxis height={50} dataKey="name" axisLine={{ stroke: 'black', strokeWidth: 2 }}
                                        tickLine={false} allowDuplicatedCategory={false}
                                        padding={{ left: 10, right: 10 }} tick={<CustomXAxisTickToTop />} angle={70} />
                                }

                                <YAxis
                                    axisLine={{ stroke: 'black', strokeWidth: 0 }}
                                    tickLine={false}
                                    tickCount={6}
                                    width={60}
                                    padding={{ top: 0 }}
                                    allowDecimals={false}
                                    tick={<CustomYAxisTickToTop />}
                                />
                                <Tooltip />
                                {/*"circle" 。*/}
                                {/*"square" 。*/}
                                {/*"rect"  */}
                                {/*"line" */}
                                <Legend iconType="square" layout={widthSize ? 'horizontal' : 'vertical'} />
                                {/*{selectedValues.キャディ设问1 &&*/}
                                {/*    <Area type="monotone" dataKey="キャディ设问1" strokeWidth={4} stroke="#666d9b" fill="#EFF0F7"*/}
                                {/*          fillOpacity={0.3} animationDuration={0}/>}*/}
                                {selectedValues && Object.entries(selectedValues).map(([key, value], index) => (
                                    value &&
                                    <Area
                                        key={key}
                                        type='monotone'
                                        // type='linear'
                                        dataKey={key}
                                        strokeWidth={4}
                                        stroke={colors[index]}
                                        fill="#EFF0F7"
                                        fillOpacity={0.3}
                                        animationDuration={0}
                                    />
                                ))}

                            </AreaChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            }
        </Box>
    );
    // option var and fun
    const weekdays = [
        { label: "平日のみ", value: 1 },
        { label: "土日祝", value: 2 },
        { label: "指定なし", value: 3 },
    ];
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    const [optionFilters, setOptionFilters] = useState({
        year: currentYear + "年度",
        month: currentMonth + "月度",
        caddie: "none",
        weekday: "3",
    });
    const [filters, setFilters] = useState({
        year: currentYear + "年度",
        month: currentMonth + "月度",
        caddie: "none",
        weekday: "3",
    });
    const getWeekdayLabel = (weekdays: any[], value: string) => {
        const weekday = weekdays.find((day) => day.value === Number(value));
        return weekday ? weekday.label : "指定なし";
    };
    const weekDayLabel = getWeekdayLabel(weekdays, optionFilters.weekday);
    const emptyFilter = async () => {
        setFilters({
            year: currentYear + "年度",
            month: currentMonth + "月度",
            caddie: "none",
            weekday: "3",
        });
        setOptionFilters({
            year: currentYear + "年度",
            month: currentMonth + "月度",
            caddie: "none",
            weekday: "3",
        });
        await fetchCaddyEvaluateData(DateUtils.initMonth(currentYear.toString(), currentMonth.toString())
            , DateUtils.initMonth(currentYear.toString(), currentMonth.toString())
            , 3, "");
    };
    const [caddyName, setCaddyName] = useState<string>("");
    const handleSelectData = async () => {
        const nowCaddyId = parseInt(filters.caddie, 10);
        const foundCaddy = caddies.find((caddy) => caddy.id === nowCaddyId);
        setCaddyName(foundCaddy ? foundCaddy.caddy_name : "");
        setOptionFilters(filters);
        await fetchCaddyEvaluateData(
            DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", ""))
            , DateUtils.initMonth(filters.year.replace("年度", ""), filters.month.toString().replace("月度", "")
            ), Number(filters.weekday), filters.caddie === "none" ? "" : filters.caddie);
    };

    const getCaddyList = () => {
        return [
            { id: 0, caddy_no: "none", caddy_name: "\u00A0" },
            ...caddies
        ].filter(caddie => !(caddie.caddy_no === "0" && caddie.caddy_name === ""));
    }
    //Option-component
    const MiddleOption = () => {
        return (
            <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                <div className="flex items-center space-x-4">
                    <div
                        style={{
                            borderLeft: `4px solid ${theme.palette.primary.main}`,
                            height: "16px",
                        }}
                    ></div>
                    <Typography
                        noWrap
                        component="span"
                        sx={{ fontWeight: "bold" }}
                        color="primary"
                    >
                        表示条件設定
                    </Typography>
                </div>
                <div className="flex flex-wrap items-center">
                    <div className="w-full sm:w-auto mr-14">
                        <DataForm
                            caddies={getCaddyList().map((caddie) => ({
                                label: caddie.caddy_name,
                                value: caddie.caddy_no,
                            }))}
                            weekdays={weekdays}
                            filters={filters}
                            setFilters={setFilters}
                        />
                    </div>

                    <div className="w-full sm:w-auto flex space-x-4 mt-4">
                        <Button
                            variant="outlined"
                            sx={{ fontWeight: "bold" }}
                            onClick={emptyFilter}
                        >
                            リセット
                        </Button>
                        <Button
                            variant="outlined"
                            sx={{ fontWeight: "bold" }}
                            onClick={handleSelectData}
                        >
                            表示
                        </Button>
                    </div>
                </div>

            </Box>
        )
    }
    // middle fun
    const middleData = questions.map((question, questionIndex) => {
        const typeMap: { [key: number]: string } = { 1: CommonUtils.getCaddyNameFromLocalStorage() + '設問', 2: 'ゴルフ場設問' };
        const name = `${typeMap[question.type]}${question.index}\n${question.content}`;
        const title = `${typeMap[question.type]}${question.index}${question.content}`;

        const totalCount = getMiddleDataCount(question.id, caddyList);
        const satisfactionCounts = getMiddleDataCounts(question.id, caddyList);

        let counts: { [key: string]: string } = {};
        let totalScore = 0;

        evaluationsList.forEach(satisfaction => {
            const count = satisfactionCounts[satisfaction.id] || 0;
            const percentage = totalCount ? formatScore(((count / totalCount) * 100).toFixed(2)) : "0";
            counts[`${satisfaction.content}(${satisfaction.score}点)`] = percentage;
            totalScore += satisfaction.score * count;
        });

        // const averageScore = totalCount ? (totalScore / totalCount).toFixed(2) : "0.00";
        const averageScore = totalCount ? formatScore((totalScore / totalCount).toFixed(2)) : "0";

        return {
            name,
            title,
            ...counts,
            totalScore: averageScore
        };
    });

    function getSatisfactionContentByScore(stage: any) {
        const satisfaction = evaluationsList.find(item => item.stage === stage);
        if (satisfaction) {
            return `${satisfaction.content}(${satisfaction.score}点)`;
        } else {
            return "";
        }
    }

    // Middle component
    // Middle y
    const CustomYAxisTickToMiddle = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        const parts = payload.value.split('\n');
        const maxLength = 15;

        let firstLine = parts[1];
        let secondLine = '';

        if (firstLine.length > maxLength) {
            firstLine = firstLine.substring(0, maxLength);
            secondLine = parts[1].substring(maxLength);
            if (secondLine.length > maxLength) {
                secondLine = secondLine.substring(0, maxLength - 1) + "...";
            }
        }

        return (
            <text x={0} y={y - 30} fill="#364985" textAnchor="start" dy={16} style={{ fontSize: 14, width: 100 }}>
                {parts[0]}
                {firstLine && <tspan x={0} dy="1em" alignmentBaseline="middle">{firstLine}</tspan>}
                {secondLine && <tspan x={0} dy="1em" alignmentBaseline="middle">{secondLine}</tspan>}
            </text>
        );
    };
    // Middle y 2
    const CustomYAxisTick2ToMiddle = ({ x, y, payload }: { x?: any, y?: any, payload?: any }) => {
        return (
            <text x={0} y={y - 30} fill="#666" textAnchor="start" dy={16} style={{ fontSize: 14, width: 100 }}>
            </text>
        );
    };
    const MiddleTitle = () => (
        <div style={{ display: 'flex', alignItems: 'center', padding: '10px', flexWrap: 'wrap' }}>
            <Typography className="title-large-text"
                style={{ paddingRight: '5px', paddingLeft: '10px' }}
                color="primary">
                設問ごとの回答分布
            </Typography>
            <Typography className="title-small-text"
                style={{ paddingRight: '0px' }}
                color="primary">
                {optionFilters.year.replace("年度", "") +
                    "年" +
                    optionFilters.month.replace("月度", "") +
                    "月度(" +
                    weekDayLabel +
                    ")"}
            </Typography>

        </div>
    );
    const CustomTooltipToMiddle = ({ active, payload, label }: { active?: any, payload?: any, label?: any }) => {
        if (active && payload && payload.length) {
            return (
                <div className="middle-chart-tooltip">

                    <p className="label"
                        style={{
                            maxWidth: '200px',
                            wordWrap: 'break-word',
                            whiteSpace: 'pre-wrap',
                            marginBottom: '20px'
                        }}>
                        {`${payload[0].payload.name}`}
                    </p>
                    <p style={{ color: colors[0] }}>{`${payload[0].dataKey} : ${Math.round(Number(payload[0].value))}`}</p>
                    <p style={{ color: colors[1] }}>{`${payload[1].dataKey} : ${Math.round(Number(payload[1].value))}`}</p>
                    <p style={{ color: colors[2] }}>{`${payload[2].dataKey} : ${Math.round(Number(payload[2].value))}`}</p>
                    <p style={{ color: colors[3] }}>{`${payload[3].dataKey} : ${Math.round(Number(payload[3].value))}`}</p>
                    <p style={{ color: colors[4] }}>{`${payload[4].dataKey} : ${Math.round(Number(payload[4].value))}`}</p>
                </div>
            );
        }

        return null;
    };
    const HorizontalLine = () => {
        const lineStyle = {
            width: '100%',
            height: '1px',
            backgroundColor: '#ccc'
        };

        return <div style={lineStyle} />;
    };

    const [yAxisWidth, setYAxisWidth] = useState<number>(35);

    useEffect(() => {
        if (evaluationsList.length > 0) {
            const maxScore = Math.max(...evaluationsList.map(evaluation => evaluation.score));

            const widthMap = [
                { limit: 9999, width: 70 },
                { limit: 999, width: 60 },
                { limit: 99, width: 50 },
                { limit: 9, width: 40 },
            ];

            const calculatedWidth = widthMap.find(({ limit }) => maxScore > limit)?.width || 35;
            setYAxisWidth(calculatedWidth);
        }
    }, [evaluationsList]);

    const MiddleChart = () => {
        return (
            <Box borderRadius={2}
                sx={{ backgroundColor: "white", marginTop: "20px", alignItems: "top" }}>

                <MiddleTitle />
                <HorizontalLine />
                <div className='middle-chart-bg'>
                    <ResponsiveContainer width={'100%'} height={widthSize
                        ? 70 * middleData.length + 60
                        : 80 * middleData.length + 60
                    }>
                        <BarChart
                            layout="vertical"
                            data={middleData}
                            margin={{
                                top: 0, right: 20, left: 20, bottom: 0,
                            }}
                            barSize={30}>
                            {widthSize ?
                                <CartesianGrid stroke="#000000" strokeDasharray="0" />
                                :
                                <CartesianGrid stroke="#000000" strokeDasharray="0" vertical={false} />
                            }

                            {widthSize &&
                                <XAxis
                                    type="number"
                                    tickLine={false}
                                    tickCount={10}
                                    interval={0}
                                    domain={[0, 100]}
                                    ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
                                    label={{
                                        value: '(%)', position: 'insideBottomLeft', offset: 0,
                                        dy: -10,
                                        dx: -40,
                                        textAnchor: 'middle',
                                        style: {
                                            fontSize: '15px',
                                            fill: '#000',
                                            // fontWeight: 'bold',
                                        }
                                    }}
                                />}
                            {!widthSize &&
                                <XAxis
                                    type="number"
                                    tickLine={false}
                                    domain={[0, 100]}
                                    label={{
                                        value: '(%)', position: 'insideBottomLeft', offset: 0,
                                        dy: -12,
                                        dx: -30,
                                        textAnchor: 'middle',
                                        style: {
                                            fontSize: '15px',
                                            fill: '#000',
                                            // fontWeight: 'bold',
                                        }
                                    }}
                                />
                            }

                            {widthSize ? <YAxis type="category" tickLine={false}
                                width={200}
                                dataKey="name"
                                tick={<CustomYAxisTickToMiddle />}
                            /> : <YAxis type="category" tickLine={false}
                                width={10}
                                dataKey="name"
                                tick={<CustomYAxisTick2ToMiddle />}
                            />}

                            <YAxis
                                type="category"
                                tickLine={false}
                                orientation="right"
                                yAxisId={1}
                                dataKey="totalScore"
                                width={yAxisWidth}
                                tick={{ fontSize: '14px', fontWeight: 'bold', fill: '#ff0000' }}
                            />
                            <Tooltip content={<CustomTooltipToMiddle />} />
                            <Legend layout={widthSize ? 'horizontal' : 'vertical'} />
                            <Bar dataKey={getSatisfactionContentByScore(1)} stackId="a" fill={colors[0]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(1)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                                {!widthSize && <LabelList
                                    dataKey="title"
                                    position="insideLeft"
                                    dy={-22}
                                    dx={-5}
                                    fill="#364985"
                                    fontSize={12}
                                    formatter={(value: any) => value.length > 15 ? value.substring(0, 15) + '...' : value}
                                />}
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(2)} stackId="a" fill={colors[1]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(2)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(3)} stackId="a" fill={colors[2]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(3)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(4)} stackId="a" fill={colors[3]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(4)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                            <Bar dataKey={getSatisfactionContentByScore(5)} stackId="a" fill={colors[4]}
                                animationDuration={0}>
                                <LabelList dataKey={getSatisfactionContentByScore(5)} position="insideRight"
                                    fill="#ffffff"
                                    formatter={(value: any) => {
                                        if (!widthSize || value === '0' || value == 0) return "";
                                        const roundedValue = Math.round(Number(value));
                                        return roundedValue.toString();
                                    }} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>

                    {widthSize ?
                        <text
                            x="100%"
                            textAnchor="end"
                            style={{ position: 'absolute', top: -10, right: 25, fontSize: '13px', color: '#000' }}>
                            (平均点)
                        </text>
                        :
                        <text
                            x="100%"
                            textAnchor="end"
                            style={{ position: 'absolute', top: -10, right: 10, fontSize: '13px', color: '#000' }}>
                            (平均点)
                        </text>
                    }
                </div>
            </Box>
        )
    }
    //bottom component
    const BottomChart = () => {
        function calculateSurveyData(surveyId: any) {
            const result: { name: String, 回答数: number, 総点数: number }[] = [];

            evaluationsList.forEach(item => {
                let answerCount = 0;
                let totalPoints = 0;
                if (caddyList) {
                    caddyList.forEach(monthData => {
                        if (monthData && monthData.survey) {
                            monthData.survey.forEach(caddy => {
                                if (caddy && caddy.survey) {
                                    caddy.survey.forEach(survey => {
                                        if (survey.id === surveyId && survey.answers) {
                                            survey.answers.forEach(answer => {
                                                if (answer.id === item.id) {
                                                    answerCount += answer.count;
                                                    totalPoints += answer.count * item.score;
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
                result.push({ name: String(item.stage), 回答数: answerCount, 総点数: totalPoints });
            });

            return result;
        }

        return (
            <div className="flex-container">
                {questions && Object.entries(questions).map(([key, question], index) => (
                    question &&
                    <div key={index}
                        className={`bottom-chart-div ${index === length - 1 && 'new-row'}`}>
                        <Typography className="title-text-line title-large-text"
                            color="primary">{`${question.type === 1 ? CommonUtils.getCaddyNameFromLocalStorage() : "ゴルフ場"}設問${question.index
                                }` + ":" + question.content}</Typography>
                        <Typography className="title-text-line title-small-text"
                            color="primary">{DateUtils.getMonthRange(optionFilters.year.replace("年度", ""), optionFilters.month.toString().replace("月度", "")) + "(" + weekDayLabel + ")"}</Typography>

                        <div className='bottom-chart-bg' style={{ position: 'relative' }}>
                            <ResponsiveContainer width={'100%'} height={'100%'}>
                                <ComposedChart
                                    data={calculateSurveyData(question.id)}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid stroke="#000000" vertical={false} strokeDasharray="0" />
                                    <XAxis dataKey="name" tickLine={false}
                                        dy={12}
                                        axisLine={{ stroke: 'black', strokeWidth: 2 }}
                                        label={{
                                            value: '評価', position: 'insideBottomLeft', offset: 0,
                                            dy: 0,
                                            dx: -25,
                                            textAnchor: 'middle',
                                            style: {
                                                fontSize: '15px',
                                                fill: '#364985',
                                                // fontWeight: 'bold',
                                            }
                                        }} />

                                    <YAxis
                                        tickLine={false}
                                        axisLine={{ stroke: 'black', strokeWidth: 0 }}
                                        orientation="left"
                                        tickCount={6}
                                        width={45}
                                        allowDecimals={false}
                                        tick={{ fontSize: '13px' }}
                                    />

                                    <YAxis
                                        tickLine={false}
                                        axisLine={{ stroke: 'black', strokeWidth: 0 }}
                                        orientation="right"
                                        yAxisId={1}
                                        tickCount={6}
                                        width={45}
                                        allowDecimals={false}
                                        tick={{ fontSize: '13px' }}
                                    />

                                    <Tooltip />
                                    <Legend verticalAlign="top" height={50} />
                                    <Bar dataKey="回答数" fill="#364985" barSize={25} animationDuration={0} />
                                    <Line
                                        type="linear"
                                        dataKey="総点数"
                                        stroke="#aebe78"
                                        strokeWidth={3}
                                        activeDot={{ r: 8 }}
                                        yAxisId={1}
                                        animationDuration={0}
                                    />
                                    <text
                                        x={10}
                                        y={50}
                                        fill="#364985"
                                        style={{ fontSize: '15px', color: '#364985' }}
                                        textAnchor="start">
                                        回答数
                                    </text>


                                </ComposedChart>
                            </ResponsiveContainer>
                            {widthSize ?
                                <text
                                    x="100%"
                                    textAnchor="end"
                                    style={{
                                        position: 'absolute',
                                        top: 35,
                                        right: 30,
                                        fontSize: '15px',
                                        color: '#364985'
                                    }}>
                                    総点数
                                </text>
                                :
                                <text
                                    x="100%"
                                    textAnchor="end"
                                    style={{
                                        position: 'absolute',
                                        top: 35,
                                        right: 10,
                                        fontSize: '15px',
                                        color: '#364985'
                                    }}>
                                    総点数
                                </text>
                            }

                        </div>
                    </div>
                ))}
            </div>

        );
    };
    //************************
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            {showData() ? (
                <Box
                    flex={1}
                    overflow="auto"
                    sx={{ display: "flex", flexDirection: "column" }}>
                    <Breadcrumb items={breadcrumbItems} />
                    <TopChart />
                    <MiddleOption />
                    {!bottomApiSuccess &&
                        <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                            <ErrorHint onButtonClick={() => handleErrorButtonClick("bottom")} screenWidth={widthSize} />
                        </Box>
                    }
                    {bottomApiSuccess && <MiddleChart />}
                    {bottomApiSuccess && <BottomChart />}
                </Box>
            )
                : ((questingsApi && evaluationApi && !caddiesApi)) ? (
                    <Box
                        flex={1}
                        overflow="auto"
                        sx={{ display: "flex", flexDirection: "column" }}>
                        <Breadcrumb items={breadcrumbItems} />
                        <ErrorHint isNull={true} screenWidth={widthSize} />
                    </Box>
                )
                    : ((!questingsApiSuccess || !evaluationApiSuccess || !caddiesApiSuccess) || (questingsApi && evaluationApi && !caddiesApi)) ? (
                        <Box
                            flex={1}
                            overflow="auto"
                            sx={{ display: "flex", flexDirection: "column" }}>
                            <Breadcrumb items={breadcrumbItems} />
                            <ErrorHint isAll={true} screenWidth={widthSize}
                                onButtonClick={() => handleErrorButtonClick("all")} />
                        </Box>
                    ) : ((!questingsApi || !evaluationApi) && questingsApiSuccess) ? (
                        <Box
                            flex={1}
                            overflow="auto"
                            sx={{ display: "flex", flexDirection: "column" }}>
                            <Breadcrumb items={breadcrumbItems} />
                            <ErrorHint jumpSetting={true} screenWidth={widthSize} />
                        </Box>
                    )
                        : (
                            <div></div>
                        )
            }
        </LocalizationProvider>
    );
};

export default Home;