/*top*/
.top-chart-title {
    text-align: left;
    margin-top: 20px;
    margin-bottom: 5px;
    margin-left: 20px;
}
.top-chart-bg {
    height: 440px;
    padding-left: 5px;
    padding-right: 30px;
}
.top-checkboxlabel{
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
    flex-wrap: wrap;
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: flex-start;
    padding: 0;
    height: auto;
    max-height: 100%;
}

.bottom-chart-div {
    flex: 0 1 calc(50% - 10px );
    background-color: white;
    box-sizing: border-box;
    /*aspect-ratio: 1 / 1;*/
    height: auto;
    border-radius: 10px;
    padding: 20px;
}

.new-row {
}

.flex-container > .bottom-chart-div {
    margin-top: 20px;
}

.flex-container > .bottom-chart-div:nth-child(odd) {
    margin-right: 10px;
}

.flex-container > .bottom-chart-div:nth-child(even) {
    margin-left: 10px;
}

/*bottom*/
.title-text-line {
    width: 100%;
    word-wrap: break-word;
}

.title-large-text {
    font-size: 15px !important;
    font-weight: bold !important;
}

.title-small-text {
    font-size: 13px !important;
}

.bottom-chart-bg {
    margin-top: -15px;
    height: 440px;
    padding-right: 10px;
    padding-left: 10px;
}
.middle-csv-button{
    display: flex !important;
    justify-content: flex-end !important;
    margin-left: auto !important;
    flex-wrap: wrap !important;
}

/*Middle*/
.middle-chart-bg {
    margin-top: 30px;
    /*height: 360px;*/
    height: auto;
    position: relative;
    padding: 10px 30px 10px 20px;
}

.middle-chart-tooltip{
    background: white;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.middle-option-btn{
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
    flex-wrap: wrap;
}


@media screen and (max-width: 910px) {
    .top-chart-title {
        text-align: left;
        margin-top: 20px;
        margin-bottom: 5px;
        margin-left: 0;
    }
    .top-chart-bg {
        height: 440px;
        padding-left: 0;
        padding-right: 0;
    }
    .top-checkboxlabel{
        display: flex;
        padding-left: 30px;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    .bottom-chart-div {
        flex: 0 1 100%;
        height: auto;
    }
    .flex-container > .bottom-chart-div {
        margin-top: 20px;
    }

    .flex-container > .bottom-chart-div:nth-child(even) {
        margin-left: 0;
    }

    .flex-container > .bottom-chart-div:nth-child(odd) {
        margin-right: 0;
    }
    /*bottom*/
    .title-large-text {
        font-size: 14px !important;
    }

    .title-small-text {
        font-size: 12px !important;
    }

    .bottom-chart-bg {
        height: 330px;
        padding-right: 0;
        padding-left: 0;
    }
    .middle-chart-bg {
        /*height: 440px;*/
        height: auto;;
        padding: 10px;
    }
    .middle-csv-button{
        display: flex !important;
        justify-content: flex-start !important;
        flex-wrap: wrap !important;
    }
    .middle-option-btn{
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

}