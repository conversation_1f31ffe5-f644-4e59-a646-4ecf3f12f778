"use client";
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  Suspense,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Al<PERSON>,
  Box,
  Pagination,
  PaginationItem,
  Typography,
  Button,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Menu,
  MenuItem,
  List,
  ListItem,
  Modal,
  CircularProgress,
} from "@mui/material";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { styled } from "@mui/material/styles";
import { useTheme } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import { useSearchParams } from "next/navigation";
import dayjs from "dayjs";
import { ScoreCourse } from "@/models/compe/resp/ranking-resp";
import { leaderboardRankingShared, rankingTypeShared } from "@/api/online-compe-api";
import SettingModal from "@/components/onlinecompe/setting-modal";

// Loading component to display while suspense is resolving
const ContentLoading = () => {
  return <div>Loading...</div>;
};

const methods = [
  { label: "記号", value: 1 },
  { label: "数字", value: 2 },
];

interface CourseData {
  course_name: string;
  hole: string;
  score_gross: number;
  score_net: number;
  course_hdcp: string;
  start_hole: string;
  total_holes: number;
  hole_scores: {
    hole_number: string;
    course_index: string;
    hole_index: string;
    score: string;
    stroke: string;
    used_hdcp: string;
    start: string;
  }[];
}

interface CombinedPlayerData {
  player_no: string;
  player_name: string;
  pos: string;
  net: string;
  is_tied: number;
  score: string;
  hole: string;
  today: string;
  hdcp_index: string;
  hdcp: string;
  courseData: {
    [key: string]: CourseData;
  };
}

const fontFamily = `"Kosugi Maru", "メイリオ", "Meiryo", "ＭＳ ゴシック", "Osaka", "Source Sans Pro", sans-serif`;

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#324F85",
    color: "#fff",
    border: "1px solid #ddd",
    padding: 0,
    textAlign: "center",
  },
  [`&.${tableCellClasses.body}`]: {
    border: "1px solid #ddd",
    textAlign: "center",
    padding: "4px 6px",
    fontFamily,
    fontSize: 16 * window.innerWidth / 1920,
  },
}));

const StyledTableRow = styled(motion.tr)(() => ({
  "&:nth-of-type(even)": {
    backgroundColor: "#E9FCFF",
  },
}));

const FixedPaginationItem = styled(PaginationItem)(({ theme }) => ({
  width: "40px",
  height: "40px",
  margin: "-0.5px",
  borderRadius: "0",
  border: "1px solid",
  borderColor: theme.palette.grey[400],
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  // color: theme.palette.primary.main,
  color: "#324F85",
  fontSize: "16px",
  "&.Mui-selected": {
    // backgroundColor: theme.palette.primary.main,
    backgroundColor: "#324F85",
    color: "white",
  },
}));

const PaginationIcon = ({ type }: { type: string }) => {
  const theme = useTheme();
  return (
    <span
      style={{
        fontSize: "32px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        color: theme.palette.primary.main,
      }}
    >
      {type === "previous" ? "«" : "»"}
    </span>
  );
};

const renderField = (field: string, stroke: string) => {
  if (stroke === "*") return <span style={{ color: "black" }}>*</span>;
  if (field === "-") return null;
  else if (parseInt(field) <= -3) return <span style={{ color: "red" }}>★</span>;
  else if (field === "-2") return <span style={{ color: "red" }}>◎</span>;
  else if (field === "-1") return <span style={{ color: "red" }}>○</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>-</span>;
  else if (field === "1") return <span style={{ color: "blue" }}>△</span>;
  else if (field === "2") return <span style={{ color: "blue" }}>□</span>;
  else if (field === "3") return <span style={{ color: "blue" }}>■</span>;
  else if (parseInt(field) > 3)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

const renderField2 = (field: string, stroke: string) => {
  if (field === "-") return null;
  else if (parseInt(field) < 0)
    return <span style={{ color: "red" }}>{field}</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>{field}</span>;
  else if (parseInt(field) > 0)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

const HolebyholeContent = () => {
  const style = {};
  const searchParams = useSearchParams();
  // プレイヤーマップ
  const [playerMap, setPlayerMap] = useState<Map<string, CombinedPlayerData>>(
    new Map()
  );
  const [courses, setCourses] = useState<ScoreCourse[]>([]);
  const [maxHoles, setMaxHoles] = useState<number>(0);
  // 該当ページ数
  const [currentPage, setCurrentPage] = useState(1);
  // 総項目数
  const [total, setTotal] = useState(0);
  // 一つのページのデータ数
  const itemsPerPage = 20;
  // 集計方法リスト
  const [rankingTypeList, setRankingTypeList] = useState<string[]>([]);
  // 集計方法
  const [rankinType, setRankinType] = useState("");
  // 集計方法切替ref
  const scoreSelectRef = useRef<HTMLSelectElement>(null);
  // スコア
  const [scoreValue, setScoreValue] = useState(1);
  // スコア表示切替ref
  const rankingTypeSelectRef = useRef<HTMLSelectElement>(null);
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState("");
  // 全画面状態
  const [isFullscreen, setIsFullscreen] = useState(false);
  // 全画面ref
  const containerRef = useRef<HTMLDivElement>(null);
  // コンペ名
  const [compeName, setCompeName] = useState<string | null>(null);
  // コンペNo
  const [compeNo, setCompeNo] = useState<number | null>(null);
  // 共有キー
  const [shareKey, setShareKey] = useState<string | null>(null);
  // モーダル状態
  const [openSetting, setOpenSetting] = useState(false);
  // 設定タイプ
  const [settingType, setSettingType] = useState<string>("");
  // 選択時間
  const [selectTime, setSelectTime] = useState<string | null>(null);
  // 最終更新時間
  const [updatedTime, setUpdatedTime] = useState<string | null>(null);
  // タイマーID
  const [timerId, setTimerId] = useState<ReturnType<typeof setInterval> | null>(
    null
  );
  // アンカーエル
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  // 設定メニュー制御
  const open = Boolean(anchorEl);
  // ロード
  const [isLoading, setIsLoading] = useState(true);
  // アニメーション
  const [isAnimating, setIsAnimating] = useState(false);

  const [width, setWidth] = useState(1920);

  useEffect(() => {
    // width should less than 1920
    const handleResize = () => setWidth(Math.min(window.innerWidth, 1920));
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 集計方法情報
  const getRankingType = async () => {
    try {
      await rankingTypeShared(shareKey!, compeNo?.toString()!).then(
        async (i) => {
          const types = i.data.ranking_type;
          console.log("types", types);
          // remove types which contains peoria from types
          const filteredTypes = types.filter((type) => !type.includes("peoria"));
          setRankingTypeList(filteredTypes);
          setRankinType(filteredTypes[0]);
        }
      );
    } catch (error) {
      setErrorMsg("該当データはありません。");
    }
  };

  // 順位情報
  const getLeaderboardRanking = async () => {
    try {
      const { data } = await leaderboardRankingShared(
        shareKey!,
        rankinType,
        compeNo?.toString()!
      );
      if (data) {
        setUpdatedTime(data?.updated_at);
        const newPlayerMap = new Map<string, CombinedPlayerData>();
        data.rankings.forEach((ranking) => {
          data.courses.forEach((course) => {
            if (!newPlayerMap.has(ranking.player_no)) {
              newPlayerMap.set(ranking.player_no, {
                player_no: ranking.player_no,
                player_name: ranking.player_name,
                pos: ranking.pos,
                net: ranking.par_net > 0 ? `+${ranking.par_net}` : ranking.par_net.toString(),
                is_tied: ranking.is_tied,
                score: ranking.par_gross > 0 ? `+${ranking.par_gross}` : ranking.par_gross.toString(),
                hole: ranking.hole,
                today: ranking.score_gross.toString(),
                hdcp: ranking.course_hdcp,
                hdcp_index: ranking.hdcp_index,
                courseData: {},
              });
            }

            const playerData = newPlayerMap.get(ranking.player_no)!;

            if (course) {
              const aMap = new Map<string, string>();
              (course.holes || []).forEach((item) => {
                aMap.set(item.hole_index, item.used_hdcp);
              });
              const combined = (ranking.hole_score.filter(
                (item) => item.course_index === course.course_index
              ) || []).map((item) => {
                const used_hdcp = aMap.get(item.hole_index) || "0";
                let start = "";
                if (
                  item.score === "-" ||
                  (item.score === "0" && item.stroke === "0")
                ) {
                  start = "";
                } else {
                  // しょう
                  const quotient = Math.floor(Number(ranking.course_hdcp)) >= 0 ? Math.floor(Number(ranking.course_hdcp) / 18) : 0;
                  // よすう
                  const remainder = Number(ranking.course_hdcp) % 18;
                  // ★個数
                  start = "★".repeat(
                    quotient + (remainder >= Number(used_hdcp) ? 1 : 0)
                  );
                }
                return { ...item, used_hdcp, start };
              });

              playerData.courseData[course.course_name] = {
                course_name: course.course_name,
                hole: ranking.hole,
                score_gross: ranking.score_gross,
                score_net: ranking.score_net,
                course_hdcp: ranking.course_hdcp,
                start_hole: course.start_hole,
                total_holes: course.holes.length,
                hole_scores: combined,
              };
            }
          });
        });
        setTotal([...newPlayerMap.values()].length);
        setPlayerMap(newPlayerMap);
        setCourses([...new Set(data.courses)]);
        setMaxHoles(Math.max(...data.courses.map((c) => c.holes.length)));
      }
    } catch (error) { }
  };

  useEffect(() => {
    if (compeNo && shareKey) {
      getRankingType();
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };
      document.addEventListener("fullscreenchange", handleFullscreenChange);
      return () => {
        document.removeEventListener(
          "fullscreenchange",
          handleFullscreenChange
        );
      };
    }
  }, [compeNo, shareKey]);

  useEffect(() => {
    if (rankinType && shareKey && compeNo) {
      // 順位情報
      getLeaderboardRanking();
      // 800msのデータ読み込み時間をシミュレート
      const timer = setTimeout(() => setIsLoading(false), 800);
      // コンポーネントアンマウント時にタイマーをクリア
      return () => clearTimeout(timer);
    }
  }, [rankinType]);

  useEffect(() => {
    // コンペ名
    const compeName = searchParams.get("compeName");
    setCompeName(compeName ?? null);
    // コンペNo
    const compeNo = searchParams.get("compeNo");
    setCompeNo(compeNo ? Number(compeNo) : null);
    // 共有キー
    const key = searchParams.get("key");
    setShareKey(key ?? null);
  }, [searchParams]);

  // ヘッダー セルのインターフェース
  interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: "center";
    width?: number;
  }

  // ヘッダー 定義
  const columns: readonly Column[] = [
    { id: "pos", label: "RANK", align: "center", width: 80, minWidth: 70 * width / 1920 },
    { id: "name", label: "NAME", align: "center", width: 250, minWidth: 200 * width / 1920 },
    { id: "score", label: rankinType === "gross" ? "GROSS" : "NET", align: "center", width: 80, minWidth: 70 * width / 1920 },
    { id: "hole", label: "HOLE", align: "center", width: 80, minWidth: 70 * width / 1920 },
    { id: "hdcp", label: "HDCP", align: "center", width: 80, minWidth: 70 * width / 1920 },
  ];

  // 全画面切り替え
  const toggleFullscreen = async () => {
    if (!isFullscreen) {
      await enterFullscreen();
    } else {
      await exitFullscreen();
    }
  };

  // 全画面移動
  const enterFullscreen = async () => {
    try {
      const e = containerRef.current || document.documentElement;
      if (e.requestFullscreen) {
        await e.requestFullscreen();
      }
    } catch (error) { }
  };

  // 全画面退出
  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      }
    } catch (error) { }
  };

  // タイマーを解除する
  useEffect(() => {
    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, [timerId]);

  // 設定メニュー
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // 設定メニュー閉じる
  const handleClose = () => {
    setAnchorEl(null);
  };

  // 設定選択
  const handleSeting = (type: string) => {
    setAnchorEl(null);
    setSettingType(type);
    setOpenSetting(true);
  };

  // 設定モーダルを保存
  const onSettingSave = (value: string) => {
    setOpenSetting(false);
    setSelectTime(value);
    if (timerId !== null) {
      clearInterval(timerId);
    }
    setTimerId(null);
    if (Number(value)) {
      const timer = setInterval(() => {
        setCurrentPage((prev) => {
          const count = total > 0 ? Math.ceil(total / itemsPerPage) : 1;
          if (prev >= count) {
            return 1;
          }
          return prev + 1;
        });
      }, Number(value));
      setTimerId(timer);
    } else {
      return;
    }
  };

  // 設定モーダルを閉じる
  const handleSettingClose = () => {
    setOpenSetting(false);
  };

  // 集計方法切替ボタン
  const handleRankingClick = () => {
    const value = rankingTypeSelectRef.current?.value;
    if (value !== undefined) {
      setRankinType(value);
    }
  };

  // スコア表示切替ボタン
  const handleScoreClick = () => {
    const value = scoreSelectRef.current?.value;
    if (value !== undefined) {
      setScoreValue(Number(value));
    }
  };

  // ページを変更する
  const handleSelectedPage = (_: unknown, value: number) => {
    setCurrentPage(value);
  };

  // 現在のページで表示されている順位情報
  const getCurrentPageData = useCallback(() => {
    const playerArray = Array.from(playerMap.values());
    const startIndex = (currentPage - 1) * itemsPerPage;
    return playerArray.slice(startIndex, startIndex + itemsPerPage);
  }, [playerMap, currentPage]);

  // タイマーを設定 - データ更新と自動ページ送り
  useEffect(() => {
    let dataInterval: NodeJS.Timeout | null = null;

    const updateRankings = () => {
      // ロード中は更新を実行しない
      if (isLoading) return;
      // アニメーション状態を開始
      setIsAnimating(true);
      // 順位情報
      getLeaderboardRanking();
      // 800msのデータ読み込み時間をシミュレート
      setTimeout(() => setIsAnimating(false), 8000);
    };

    const startInterval = () => {
      // 10秒ごとにデータを更新
      dataInterval = setInterval(updateRankings, 10000);
    };

    if (!isLoading) {
      updateRankings();
      startInterval();
    }

    // コンポーネントアンマウント時にタイマーをクリア
    return () => {
      if (dataInterval !== null) {
        clearInterval(dataInterval);
      }
    };
  }, [rankinType, isLoading]);

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      style={{
        height: "100vh",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        backgroundColor: "#fff",
      }}
    >
      {getCurrentPageData().length > 0 ? (
        <>
          <Box
            sx={{
              height: "50px",
              background: "#324F85",
              color: "#fff",
              display: "flex",
              alignItems: "center",
              px: 2,
            }}
          >
            <Typography variant="h1">LEADER BOARD - {compeName}</Typography>
            <Box
              sx={{
                ml: "auto",
                display: "flex",
                alignItems: "center",
              }}
            >
              <p>Updated : {dayjs(updatedTime).format('YYYY-MM-DD HH:mm')}</p>
              <div>
                <div
                  onClick={handleClick}
                  style={{ cursor: "pointer", margin: "0 10px" }}
                >
                  <img src="/webapp/images/compe_ic_setting.png" />
                </div>
                <Menu
                  id="basic-menu"
                  anchorEl={anchorEl}
                  open={open}
                  onClose={handleClose}
                  MenuListProps={{
                    "aria-labelledby": "basic-button",
                  }}
                >
                  <MenuItem onClick={() => handleSeting("pagetime")}>
                    自動ページ送り
                  </MenuItem>
                  <MenuItem onClick={() => handleSeting("email")}>
                    リンク共有
                  </MenuItem>
                </Menu>
              </div>
              <div onClick={toggleFullscreen} style={{ cursor: "pointer" }}>
                <img
                  src="/webapp/images/compe_ic_full_screen.png"
                  style={{ width: "36px", height: "36px" }}
                />
              </div>
            </Box>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              height: "calc(100vh - 50px)",
              px: 4,
              py: 1,
            }}
          >
            <Box
              display="flex"
              flexDirection="column"
              alignItems="end"
              marginBottom={1}
            >
              <div className="gap-4 flex items-center">
                <Typography>集計方法切替</Typography>
                <TextField
                  select
                  variant="outlined"
                  size="small"
                  // value={rankinType}
                  // onChange={(event) => setRankinType(event.target.value)}
                  defaultValue={rankingTypeList[0]}
                  inputRef={rankingTypeSelectRef}
                  InputProps={{
                    style: { width: "130px", height: "36px" },
                  }}
                >
                  {rankingTypeList.map((i) => (
                    <MenuItem key={i} value={i}>
                      {
                        i === "gross"
                          ? "グロス" :
                          i === "handy"
                            ? "ネット"
                            : i === "peoria"
                              ? "ぺリア"
                              : i === "new-peoria"
                                ? "新ペリア"
                                : i === "new-new-peoria"
                                  ? "新新ペリア"
                                  : i}
                    </MenuItem>
                  ))}
                </TextField>
                <Button
                  variant="contained"
                  sx={{
                    backgroundColor: "#324F85",
                  }}
                  onClick={handleRankingClick}
                >
                  切替
                </Button>
              </div>
              <div className="gap-4 flex items-center mt-2">
                <Typography>スコア表示切替</Typography>
                <TextField
                  select
                  variant="outlined"
                  size="small"
                  // value={scoreValue}
                  // onChange={(event) =>
                  //   setScoreValue(Number(event.target.value))
                  // }
                  defaultValue={methods[0].value}
                  inputRef={scoreSelectRef}
                  InputProps={{
                    style: { width: "130px", height: "36px" },
                  }}
                >
                  {methods.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
                <Button
                  variant="contained"
                  sx={{
                    backgroundColor: "#324F85",
                  }}
                  onClick={handleScoreClick}
                >
                  切替
                </Button>
              </div>
            </Box>
            <Box display="flex" marginBottom={1} marginTop={0}>
              <Typography>{Array.from(playerMap.values()).length}名</Typography>
              <Typography ml={3}>★...ハンディキャップ適用</Typography>
              {scoreValue === 1 && (
                <List
                  className="flex"
                  style={{
                    fontSize: 16 * width / 1920,
                    height: "24px",
                    marginLeft: 24,
                    padding: 0,
                  }}
                >
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "red", fontFamily }}>★ </span>: アルバトロス
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "red", fontFamily }}>◎ </span>: イーグル
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "red", fontFamily }}>○ </span>: バーディー
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ fontFamily }}>－ </span>: パー
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "blue", fontFamily }}>△ </span>: ボギー
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "blue", fontFamily }}>□ </span>: ダブルボギー
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span style={{ color: "blue", fontFamily }}>■ </span>: トリプルボギー
                  </ListItem>
                  <ListItem sx={{ width: "auto", paddingLeft: 0 }}>
                    <span>数字 </span>: オーバーパー
                  </ListItem>
                </List>
              )}
            </Box>
            <Box display="flex" flexDirection="column" flexGrow={1}>
              <Box flex={1} overflow="auto">
                <TableContainer
                  component={Paper}
                  sx={{
                    overflow: "hidden",
                    height: isFullscreen ? 876 : 746, // 固定した高さにより跳ねるのを防ぐ
                    minHeight: isFullscreen ? 876 : 746,
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow
                        sx={{
                          height: isFullscreen ? 64 * width / 1920 : 54 * width / 1920,
                          "& .MuiTableCell-root": {
                            fontSize: isFullscreen ? 18 * width / 1920 : 16 * width / 1920,
                          },
                        }}
                      >
                        {columns.map((column) => (
                          <StyledTableCell
                            key={column.id}
                            align={column.align}
                            sx={{
                              minWidth: column.minWidth,
                              width: column.width,
                            }}
                          >
                            {column.label}
                          </StyledTableCell>
                        ))}
                        {courses.map((course) => (
                          <React.Fragment key={course.course_index}>
                            {course.holes.map((hole) => (
                              <StyledTableCell
                                key={hole.hole_index}
                                sx={{ width: 85, minWidth: 50 * width / 1920 }}
                              >
                                <p style={{ borderBottom: "1px solid #ddd" }}>
                                  {Number(hole.hole_index) + + Number(course.start_hole)}
                                </p>
                                <p>{hole.used_par}</p>
                              </StyledTableCell>
                            ))}
                            <StyledTableCell sx={{ width: 120, minWidth: 80 * width / 1920 }}>
                              <p>{course.course_name}</p>
                              <p>
                                {course.holes.reduce(
                                  (sum, hole) => sum + Number(hole.used_par),
                                  0
                                )}
                              </p>
                            </StyledTableCell>
                          </React.Fragment>
                        ))}
                        <StyledTableCell sx={{ width: 80, minWidth: 70 * width / 1920 }}>
                          <p>TODAY</p>
                          <p>
                            {courses
                              .flatMap((course) => course.holes)
                              .reduce(
                                (sum, hole) => sum + parseInt(hole.used_par),
                                0
                              )}
                          </p>
                        </StyledTableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody>
                      <AnimatePresence mode="sync">
                        {getCurrentPageData().map((player, index) => {
                          return (
                            <StyledTableRow
                              key={`${player.player_no}-${currentPage}`} // player_noとページ番号を結合してユニークキーを生成する
                              layout
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              transition={{
                                duration: 0.4,
                                delay: index * 0.03,
                                ease: "easeOut",
                              }}
                              style={{
                                display: "table-row",
                                height: isFullscreen ? 40.5 * width / 1920 : 34.5 * width / 1920,
                                fontSize: isFullscreen ? 18 * width / 1920 : 16 * width / 1920,
                              }}
                            >
                              <StyledTableCell>
                                <div
                                  style={{
                                    width: "48px",
                                    height: "22px",
                                    backgroundColor:
                                      Number(player.pos) > 3
                                        ? "#324F85"
                                        : "#FFB300",
                                    color:
                                      Number(player.pos) > 3 ? "#fff" : "#000",
                                    borderRadius: "18px",
                                    lineHeight: "22px",
                                    margin: "0 auto",
                                  }}
                                >
                                  {player.pos
                                    ? `${player.pos}`
                                    : "-"}
                                </div>
                              </StyledTableCell>
                              <StyledTableCell
                                sx={{ textAlign: "left !important" }}
                              >
                                {player.player_name}
                              </StyledTableCell>
                              <StyledTableCell>{rankinType === "gross" ? player.score : player.net}</StyledTableCell>
                              <StyledTableCell>{player.hole}</StyledTableCell>
                              <StyledTableCell>{player.hdcp}</StyledTableCell>
                              {courses.map((course) => {
                                const courseData =
                                  player.courseData[course.course_name];
                                return (
                                  <React.Fragment key={course.course_index}>
                                    {Array.from(
                                      { length: maxHoles },
                                      (_, i) => {
                                        const holeScore = courseData?.hole_scores?.find(
                                          (hs) => parseInt(hs.hole_index) === i
                                        );
                                        return (
                                          <StyledTableCell
                                            key={i}
                                            sx={{ position: "relative" }}
                                          >
                                            <p
                                              style={{
                                                fontSize: 9,
                                                position: "absolute",
                                                bottom: 0,
                                                left: 0,
                                                height: "10px",
                                                overflow: "hidden",
                                                color: "#5A94FF",
                                              }}
                                            >
                                              {holeScore?.start}
                                            </p>
                                            <p>
                                              {holeScore
                                                ? scoreValue === 1
                                                  ? renderField(
                                                    holeScore.score,
                                                    holeScore.stroke
                                                  )
                                                  : renderField2(
                                                    holeScore.score,
                                                    holeScore.stroke
                                                  )
                                                : ""}
                                            </p>
                                          </StyledTableCell>
                                        );
                                      }
                                    )}
                                    <StyledTableCell>
                                      {courseData?.hole_scores.reduce(
                                        (sum, hole) => sum + Number(hole.score),
                                        0
                                      )}
                                    </StyledTableCell>
                                  </React.Fragment>
                                );
                              })}
                              <StyledTableCell>{player.today}</StyledTableCell>
                            </StyledTableRow>
                          );
                        })}
                      </AnimatePresence>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
              <Pagination
                color="primary"
                variant="outlined"
                shape="rounded"
                count={total > 0 ? Math.ceil(total / itemsPerPage) : 1}
                page={currentPage}
                siblingCount={2}
                boundaryCount={2}
                // showFirstButton
                // showLastButton
                onChange={handleSelectedPage}
                renderItem={(item) => (
                  <FixedPaginationItem
                    {...item}
                    slots={{
                      previous: () => <KeyboardArrowLeftIcon />,
                      next: () => <KeyboardArrowRightIcon />,
                      // first: () => <KeyboardDoubleArrowLeftIcon />,
                      // last: () => <KeyboardDoubleArrowRightIcon />,
                    }}
                  />
                )}
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  my: 1,
                }}
              />
            </Box>
          </Box>
        </>
      ) : (
        <Alert severity="error" className="mb-2">
          該当データはありません。
        </Alert>
      )}
      <Modal open={openSetting} onClose={handleSettingClose}>
        <SettingModal
          settingType={settingType}
          onClose={handleSettingClose}
          onSettingSave={onSettingSave}
          compeNo={Number(compeNo)}
          shareKey={shareKey ?? ""}
          selectTime={selectTime}
        />
      </Modal>
    </Box>
  );
};

// Wrapper component with Suspense
const Holebyhole = () => {
  return (
    <Suspense fallback={<ContentLoading />}>
      <HolebyholeContent />
    </Suspense>
  );
};

export default Holebyhole;
