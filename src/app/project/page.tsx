"use client";
import React, { useState, useEffect, Suspense } from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Alert, TextField } from "@mui/material";
import ForwardRoundedIcon from "@mui/icons-material/ForwardRounded";
import { useSearchParams, useRouter } from "next/navigation";
import { leaderboardRankingShared, rankingTypeShared } from "@/api/online-compe-api";

// Loading component to display while suspense is resolving
const ContentLoading = () => {
  return <div>Loading...</div>;
};


const ProjectorContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  // 共有キーエラー
  const [sharedKeyError, setSharedKeyError] = useState(false);
  // 共有キーエラーメッセージ
  const [sharedKeyErrorMsg, setSharedKeyErrorMsg] = useState(" ");
  // エラーメッセージ
  const [errorMsg, setErrorMsg] = useState("");
  // コンペ名
  const [compeName, setCompeName] = useState<string | null>(null);
  // コンペNo
  const [compeNo, setCompeNo] = useState<number | null>(null);

  useEffect(() => {
    // コンペ名
    const compeName = searchParams.get("compeName");
    setCompeName(compeName ? compeName : null);
    // コンペNo
    const compeNo = searchParams.get("compeNo");
    setCompeNo(compeNo ? Number(compeNo) : null);
  }, [searchParams]);

  const handleAutoClick = async () => {
    const shareKey = document.getElementById("shareKey") as HTMLInputElement;
    if (!shareKey.value || !/^\d{6}$/.test(shareKey.value)) {
      setSharedKeyError(true);
      setSharedKeyErrorMsg("※6桁の半角数値で入力してください。");
    } else {
      setSharedKeyError(false);
      setSharedKeyErrorMsg(" ");

      try {
        await rankingTypeShared(shareKey.value, compeNo?.toString()!).then(
          async (i) => {
            const types = i.data.ranking_type;
            const { data } = await leaderboardRankingShared(
              shareKey.value,
              types[0],
              compeNo?.toString()!
            );
            if (data) {
              router.push(
                `/project/detail?compeNo=${compeNo}&compeName=${compeName}&key=${shareKey.value}`
              );
            } else {
              setSharedKeyError(true);
              setErrorMsg("該当データはありません。");
            }
          }
        );
      } catch (error) {
        setSharedKeyError(true);
        setErrorMsg("該当データはありません。");
      }
    }
  };

  return (
    <>
      {compeNo && compeName ? (
        <Box
          sx={{
            height: "100vh",
            backgroundColor: "#fff",
          }}
        >
          <Box
            sx={{
              height: "50px",
              background: "#324F85",
              color: "#fff",
              display: "flex",
              alignItems: "center",
              px: 2,
            }}
          >
            <Typography variant="h1">LEADER BOARD - {compeName}</Typography>
          </Box>
          {errorMsg && (
            <Alert
              severity="error"
              onClose={() => {
                setErrorMsg("");
              }}
              className="w-1/2 m-auto mt-2"
            >
              {errorMsg}
            </Alert>
          )}
          <div style={{ height: "150px" }}></div>
          <Typography className="text-center w-full">
            このコンペのリーダボード閲覧するには
            <br />
            共有キーを入力してください
          </Typography>

          <div className="flex items-start justify-center gap-2 py-4">
            <TextField
              error={sharedKeyError}
              helperText={sharedKeyErrorMsg}
              id="shareKey"
              variant="outlined"
              autoComplete="off"
              size="small"
              required
              color={sharedKeyError ? "error" : "primary"}
              InputProps={{
                style: { width: "500px", height: "36px" },
              }}
            />
            <Button
              variant="contained"
              onClick={handleAutoClick}
              startIcon={<ForwardRoundedIcon />}
              sx={{
                minWidth: "32px",
                padding: "6px 0px",
                marginTop: "2px",
                "& .MuiButton-startIcon": {
                  marginRight: "0px",
                  marginLeft: "0px",
                },
              }}
            ></Button>
          </div>
        </Box>
      ) : (
        <Box
          sx={{
            height: "100vh",
            backgroundColor: "#fff",
          }}
        >
          Loading...
        </Box>
      )}
    </>
  );
};

// Wrapper component with Suspense
const Projector = () => {
  return (
    <Suspense fallback={<ContentLoading />}>
      <ProjectorContent />
    </Suspense>
  );
};

export default Projector;
