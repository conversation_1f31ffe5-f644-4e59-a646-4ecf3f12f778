"use client";
import { useSearchParams, useRouter } from "next/navigation";
import { Suspense } from "react";


const ContentLoading = () => {
    return <div>Loading...</div>;
};

const StartTimesContent = () => {


    const router = useRouter();
    const searchParams = useSearchParams();

    const echizenhama = [
        { no: 1, status: "Tee", cart: 10, scores: [110, 120, 130, 140] },
        { no: 2, status: "Tee", cart: 11, scores: [150, 160, 170, 180] },
        { no: 3, status: "info", cart: 13, scores: [190, 111, 121, 131] },
        { no: 4, status: "---", cart: 14, scores: [141, 151, 161, 171] },
        { no: 5, status: "---", cart: 15, scores: [181, 191, 112, 122] },
        { no: 6, status: "---", cart: 16, scores: [132, 142, 152, 162] },
        { no: 7, status: "---", cart: 17, scores: [172, 182, 192, 113] },
        { no: 8, status: "---", cart: 18, scores: [123, 133, 143, 153] },
    ]

    const kaku<PERSON><PERSON> = [
        { no: 1, status: "Tee", cart: 40, scores: [163, 173, 183, 193] },
        { no: 2, status: "Tee", cart: 41, scores: [114, 124, 134, 144] },
        { no: 3, status: "info", cart: 42, scores: [154, 164, 174, 184] },
        { no: 4, status: "---", cart: 43, scores: [194, 115, 125, 135] },
        { no: 5, status: "---", cart: 44, scores: [145, 155, 165, 175] },
        { no: 6, status: "---", cart: 45, scores: [185, 195, 116, 126] },
        { no: 7, status: "---", cart: 46, scores: [136, 146, 156, 166] },
        { no: 8, status: "---", cart: 47, scores: [176, 186, 196, 117] },
    ]

    return (
        <div className="min-h-screen bg-black text-white p-0">
            <div className="px-4 py-3 border-b-[3px] border-yellow-400">
                <div className="flex items-center justify-between mb-2">
                    <div className="text-3xl font-bold">10:25</div>
                    <h1 className="text-2xl font-bold text-center flex-1">スタートのご案内</h1>
                    <div className="w-20"></div>
                </div>
            </div>

            <div className="grid md:grid-cols-2 gap-0 border-b-[3px] border-yellow-400">
                {/* Echizenhama IN */}
                <div className="border-r border-gray-600">
                    <h2 className="text-xl font-bold px-4 py-3 bg-[#3a3a3a] border-b-2 border-white">越前浜 IN</h2>
                    <div className="overflow-x-auto">
                        <table className="w-full text-base border-collapse">
                            <thead>
                                <tr className="bg-[#555555] border-b border-gray-600">
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">No</th>
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">Status</th>
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">Cart</th>
                                    <th className="px-3 py-2 text-center font-normal text-sm" colSpan={4}>
                                        Player
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {echizenhama.map((row) => (
                                    <tr
                                        key={row.no}
                                        className={`border-b border-gray-600 ${row.status === "Tee"
                                            ? "bg-yellow-400 text-black"
                                            : row.status === "info"
                                                ? "bg-[#90EE90] text-black"
                                                : "bg-[#2a2a2a]"
                                            }`}
                                    >
                                        <td className="px-3 py-3 font-bold text-lg border-r border-gray-600">{row.no}</td>
                                        <td className="px-3 py-3 border-r border-gray-600">
                                            {row.status === "info" ? (
                                                <div className="flex items-center justify-center w-8 h-8 bg-yellow-400 rounded-full">
                                                    <span className="text-black font-bold text-lg">i</span>
                                                </div>
                                            ) : (
                                                <span className="font-bold text-lg">{row.status}</span>
                                            )}
                                        </td>
                                        <td className="px-3 py-3 font-bold text-lg border-r border-gray-600">{row.cart}</td>
                                        {row.scores.map((score, idx) => (
                                            <td
                                                key={idx}
                                                className={`px-2 py-3 text-center font-bold text-lg ${idx < 3 ? 'border-r border-gray-600' : ''} ${row.status === "info" && idx === 0 ? "text-yellow-500" : ""
                                                    }`}
                                            >
                                                {score}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Kakudayama IN */}
                <div>
                    <h2 className="text-xl font-bold px-4 py-3 bg-[#3a3a3a] border-b-2 border-white">角田山 IN</h2>
                    <div className="overflow-x-auto">
                        <table className="w-full text-base border-collapse">
                            <thead>
                                <tr className="bg-[#555555] border-b border-gray-600">
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">No</th>
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">Status</th>
                                    <th className="px-3 py-2 text-left font-normal text-sm border-r border-gray-600">Cart</th>
                                    <th className="px-3 py-2 text-center font-normal text-sm" colSpan={4}>
                                        Player
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {kakudayama.map((row) => (
                                    <tr
                                        key={row.no}
                                        className={`border-b border-gray-600 ${row.status === "Tee"
                                            ? "bg-yellow-400 text-black"
                                            : row.status === "info"
                                                ? "bg-[#90EE90] text-black"
                                                : "bg-[#2a2a2a]"
                                            }`}
                                    >
                                        <td className="px-3 py-3 font-bold text-lg border-r border-gray-600">{row.no}</td>
                                        <td className="px-3 py-3 border-r border-gray-600">
                                            {row.status === "info" ? (
                                                <div className="flex items-center justify-center w-8 h-8 bg-yellow-400 rounded-full">
                                                    <span className="text-black font-bold text-lg">i</span>
                                                </div>
                                            ) : (
                                                <span className="font-bold text-lg">{row.status}</span>
                                            )}
                                        </td>
                                        <td className="px-3 py-3 font-bold text-lg border-r border-gray-600">{row.cart}</td>
                                        {row.scores.map((score, idx) => (
                                            <td
                                                key={idx}
                                                className={`px-2 py-3 text-center font-bold text-lg ${idx < 3 ? 'border-r border-gray-600' : ''} ${row.status === "info" && idx === 0 ? "text-yellow-500" : ""
                                                    }`}
                                            >
                                                {score}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div className="px-4 py-4 bg-black">
                <div className="flex gap-4 items-start">
                    <div className="flex-shrink-0 flex items-center justify-center w-14 h-14 bg-yellow-400 rounded-full">
                        <span className="text-black font-bold text-3xl">i</span>
                    </div>
                    <div className="flex-1 pt-1">
                        <p className="text-lg font-bold mb-1 text-yellow-400">
                            スタート時間15分前にはティーイングエリアへお越しください。
                        </p>
                        <p className="text-sm leading-relaxed">
                            ご自身のロッカー番号が表示されていない場合は、お手数ですがスタッフまでお声がけください
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
}

// Wrapper component with Suspense
const StartTimesPage = () => {
    return (
        <Suspense fallback={<ContentLoading />}>
            <StartTimesContent />
        </Suspense>
    );
};

export default StartTimesPage;
