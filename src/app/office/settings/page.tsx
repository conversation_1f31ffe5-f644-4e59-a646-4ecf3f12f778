"use client";
import React, { useEffect, useState } from "react";
import { Box, Button, FormControlLabel, Typography, RadioGroup, Radio } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { CardReaderType, EnableStartGuide, EnableQuestionnaire, EnableSelfScorePrint } from "@/models/compe/enum-types";
import ApiDialog from "@/components/onlinecompe/api-dialog"
import { OfficeSettings } from "@/models/officesettings/resp/office-settings";
import { getOfficeSettings, updateOfficeSettings } from "@/api/office-settings-api";

const Home = () => {

    const itemStyle = {
        textAlign: 'right',
        marginRight: '20px',
        width: '160px',
        fontWeight: 'bold',
        fontSize: '16px'
    };

    const [enableSelfScorePrint, setEnableSelfScorePrint] = useState<number>(0);
    const [enableQuestionnaire, setEnableQuestionnaire] = useState<number>(0);
    const [enableStartGuide, setEnableStartGuide] = useState<number>(0);
    const [cardReaderType, setCardReaderType] = useState<number>(0);

    const handleEnableSelfScorePrintChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEnableSelfScorePrint(Number(event.target.value));
    };
    const handleEnableQuestionnaireChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEnableQuestionnaire(Number(event.target.value));
    };
    const handleEnableStartGuideChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEnableStartGuide(Number(event.target.value));
    };
    const handleCardReaderTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setCardReaderType(Number(event.target.value));
    };
    
    const checkOfficeSettingsData = () => {
        postOfficeSettings(createOfficeSettingsData());
    }
    const createOfficeSettingsData = (): OfficeSettings => {
        return {
            enable_self_score_print: Number(enableSelfScorePrint),
            enable_questionnaire: Number(enableQuestionnaire),
            enable_start_guide: Number(enableStartGuide),
            card_reader_type: Number(cardReaderType),
        }
    }
    const [apiSucceed, setApiSucceed] = useState<boolean>(false);
    const [openApiDialog, setOpenApiDialog] = useState<boolean>(false)
    const handleCloseErrorDialog = () => {
        setOpenApiDialog(false);
    };
    useEffect(() => {
        fetchOfficeSettings();
    }, []);
    const fetchOfficeSettings = async () => {
        try {
            const result = await getOfficeSettings();
            if (Object.keys(result).length > 0) {
                setEnableSelfScorePrint(result.enable_self_score_print);
                setEnableQuestionnaire(result.enable_questionnaire);
                setEnableStartGuide(result.enable_start_guide);
                setCardReaderType(result.card_reader_type);
            }
        } catch (error) {

        }
    }

    //システム設定をサーバに送る
    const postOfficeSettings = async (officeSettings: OfficeSettings) => {
        try {
            const res = await updateOfficeSettings(officeSettings);
            setApiSucceed(res);
            setOpenApiDialog(true)
        } catch (error) {
            setApiSucceed(false);
            setOpenApiDialog(true)
        }
    }

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box sx={{ display: "flex", flexDirection: "column", backgroundColor: '#f9f9f9', margin: "-24px" }}>
                <Box sx={{ display: 'flex', height: '65px', alignItems: 'center', backgroundColor: '#f3f3f3', flexDirection: "row", borderRadius: "5px", marginLeft: "18px", marginRight: "18px", marginTop: "25px", paddingLeft: "32px" }}>
                    <img src="/webapp/images/compe_ic_menu.png" style={{ width: '25px', height: '17px' }} />
                    <Typography variant="h2" sx={{ fontWeight: 'normal', marginLeft: "14px" }}>
                        システム設定
                    </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px", alignItems: 'center' }}>
                    
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                セルフスコア印刷機能
                            </Typography>
                            <RadioGroup
                                value={enableSelfScorePrint}
                                onChange={handleEnableSelfScorePrintChange}
                                row
                            >
                                <FormControlLabel
                                    value={EnableSelfScorePrint.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="OFF"

                                />
                                <FormControlLabel
                                    value={EnableSelfScorePrint.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="ON"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>

                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                アンケート機能
                            </Typography>
                            <RadioGroup
                                value={enableQuestionnaire}
                                onChange={handleEnableQuestionnaireChange}
                                row
                            >
                                <FormControlLabel
                                    value={EnableQuestionnaire.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="OFF"

                                />
                                <FormControlLabel
                                    value={EnableQuestionnaire.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="ON"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>

                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                スタート案内機能
                            </Typography>
                            <RadioGroup
                                value={enableStartGuide}
                                onChange={handleEnableStartGuideChange}
                                row
                            >
                                <FormControlLabel
                                    value={EnableStartGuide.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="OFF"

                                />
                                <FormControlLabel
                                    value={EnableStartGuide.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="ON"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>

                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                カードリード種類
                            </Typography>
                            <RadioGroup
                                value={cardReaderType}
                                onChange={handleCardReaderTypeChange}
                                row
                            >
                                <FormControlLabel
                                    value={CardReaderType.None}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="なし"

                                />
                                <FormControlLabel
                                    value={CardReaderType.QRCode}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="バーコードリーダ"
                                    sx={{ marginLeft: '20px' }}
                                />
                                <FormControlLabel
                                    value={CardReaderType.ICCard}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="ICカードリーダ"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                    </Box>
                </Box>

                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        marginTop: '10px',
                    }}
                >
                    <Button variant="contained" sx={{
                        marginBottom: '60px'
                    }}
                        onClick={checkOfficeSettingsData}>
                        保存
                    </Button>
                </Box>
                <ApiDialog
                    openApiDialog={openApiDialog}
                    handleCloseErrorDialog={handleCloseErrorDialog}
                    succeed={apiSucceed}
                    apiType={"officesettings"}>
                </ApiDialog>
            </Box>
        </LocalizationProvider>
    );
}

export default Home;
