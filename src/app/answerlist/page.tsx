"use client";
import React, { useState, useEffect, useRef } from "react";
import { useTheme } from "@mui/material/styles";
import {
    Box,
    Typography,
    Button,
    TableContainer,
    Paper,
    Divider,
} from "@mui/material";
import Breadcrumb from "../../components/breadcrumb";
import FilterForm from "../../components/filterform";
import PaginationComponent from "../../components/pagination-component";
import AnswerTableComponent from "../../components/answer-table-component";
import {
    Caddy, Evaluation, Lowest,
    Question,
    Questionnaire,
} from "@/models/caddy-models";
import { fetchRows, fetchCaddies, getQuestion, getEvaluations, getCsvUrlAndDownload } from "@/api/caddy-api";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import ErrorHint from "@/components/error-hint";
import { handleAuthToken } from "../../auth/Auth"
import CsvErrorDialog from "@/components/csv-error-dialog";

const Home = () => {
    const theme = useTheme();
    const breadcrumbItems = [
        { label: "アンケート", href: "/" },
        { label: "アンケート回答一覧" },
    ];
    const [widthSize, setWidthSize] = useState(true);
    useEffect(() => {
        handleAuthToken().then(
            (value) => {
                if (value) {
                    const handleResize = () => {
                        if (window.document.documentElement.clientWidth < 910) {
                            setWidthSize(false);
                        } else {
                            setWidthSize(true);
                        }
                    };

                    handleResize(); // Initialize the state based on the current window width
                    window.addEventListener("resize", handleResize);
                    return () => window.removeEventListener("resize", handleResize);
                } else {
                    // const login = process.env.BASE_URL?.replace("api/v2/web", "") + "ops/login";
                    // window.location.href = login;
                }
            },
        );
    }, []);
    const [openDialog, setOpenDialog] = useState(false);
    const handleClose = () => {
        setOpenDialog(false);
    };
    //error
    const [questingsApi, setQuestingsApi] = useState(true);
    const [questingsApiSuccess, setQuestingsApiSuccess] = useState(true);
    const [evaluationApi, setEvaluationApi] = useState(true);
    const [evaluationApiSuccess, setEvaluationApiSuccess] = useState(true);
    const [caddiesApi, setCaddiesApi] = useState(true);
    const [caddiesApiSuccess, setCaddiesApiSuccess] = useState(true);

    const [dataSuccess, setDataSuccess] = useState(true);
    const handleErrorButtonClick = async (type: string) => {
        if (type === "all") {
            await fetchData();
            await loadData();
        }
        if (type === "api") {
            loadData();
        }
    };
    const showData = () => {
        return (questingsApi && questingsApiSuccess && evaluationApi && caddiesApi && evaluationApiSuccess && caddiesApiSuccess)
    }

    const dateNow = new Date();
    const [filters, setFilters] = useState({
        startDate: `${dateNow.getFullYear() - 2}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
        endDate: `${dateNow.getFullYear()}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
        caddie: "none",
        weekday: "3",
    });
    const weekdays = [
        { label: "平日のみ", value: 1 },
        { label: "土日祝", value: 2 },
        { label: "指定なし", value: 3 },
    ];
    const counts = [10, 50, 100];
    const [caddies, setCaddies] = useState<Caddy[]>([]);
    const [rows, setRows] = useState<Questionnaire>({
        current_page: 0,
        data: [],
        lowest: [],
        from: 0,
        last_page: 0,
        per_page: 0,
        to: 0,
        total: 0,
    });
    const [questions, setQuestions] = useState<Question[]>([]);
    const [selectedCount, setSelectedCount] = useState(10);
    const [selectedPage, setSelectedPage] = useState(1);
    const [evaluationsList, setEvaluationsList] = useState<Evaluation[]>([]);

    const setRowsData = (rowsData: Questionnaire | undefined) => {
        if (rowsData) {
            setRows(rowsData);
        } else {
            setRows({
                current_page: 0,
                data: [],
                lowest: [],
                from: 0,
                last_page: 0,
                per_page: 0,
                to: 0,
                total: 0,
            });
        }
    };

    const handleSelectChange = async (event: any) => {
        setSelectedCount(event.target.value);
        try {
            const rowsData = await fetchRows(
                selectedPage,
                selectedCount,
                filters.startDate,
                filters.endDate,
                filters.caddie,
                filters.weekday
            );
            setDataSuccess(true)
            setRowsData(rowsData);
        } catch (error) {
            setDataSuccess(false)
        }
    };

    const fetchData = async () => {
        try {
            const caddiesData = await fetchCaddies();
            setCaddiesApi(true)
            setCaddiesApiSuccess(true)
            if (caddiesData) {
                setCaddies(caddiesData);
            } else {
                setCaddiesApi(false);
            }
        } catch (error) {
            setCaddiesApiSuccess(false);
        }

        try {
            const questionsData = await getQuestion();
            setQuestingsApi(true)
            setQuestingsApiSuccess(true);
            if (questionsData) {
                setQuestions(questionsData);
            } else {
                setQuestingsApi(false)
            }
        } catch (error) {
            setQuestingsApiSuccess(false);
        }
        try {
            const evaluations = await getEvaluations();
            setEvaluationApi(true)
            setEvaluationApiSuccess(true);
            if (evaluations) {
                setEvaluationsList(evaluations);
            } else {
                setEvaluationApi(false)
            }
        } catch (error) {
            setEvaluationApiSuccess(false);
        }

    };

    useEffect(() => {
        fetchData();
    }, []);

    const loadData = async () => {
        try {
            const rowsData = await fetchRows(
                selectedPage,
                selectedCount,
                filters.startDate,
                filters.endDate,
                filters.caddie,
                filters.weekday
            );
            setDataSuccess(true)
            console.log("rowsData", rowsData)
            if (rowsData) {
                setRows({
                    current_page: rowsData.current_page,
                    data: rowsData.data,
                    lowest: rowsData.lowest,
                    from: rowsData.from,
                    last_page: rowsData.last_page,
                    per_page: rowsData.per_page,
                    to: rowsData.to,
                    total: rowsData.total,
                })
            } else {
                setRows({
                    current_page: 0,
                    data: [],
                    lowest: [],
                    from: 0,
                    last_page: 0,
                    per_page: 0,
                    to: 0,
                    total: 0,
                });
            }
        } catch (error) {
            setDataSuccess(false)
        }
    };

    useEffect(() => {
        loadData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCount, selectedPage]);

    const handleApplyFilters = async () => {
        try {
            const rowsData = await fetchRows(
                selectedPage,
                selectedCount,
                filters.startDate,
                filters.endDate,
                filters.caddie,
                filters.weekday
            );
            setDataSuccess(true)
            setRowsData(rowsData);
        } catch (error) {
            setDataSuccess(false)
        }
    };

    const handleResetFilters = async () => {
        setFilters({
            startDate: `${dateNow.getFullYear() - 2}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
            endDate: `${dateNow.getFullYear()}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
            caddie: "none",
            weekday: "3"
        });
        const rowsData = await fetchRows(
            selectedPage,
            selectedCount,
            `${dateNow.getFullYear() - 2}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
            `${dateNow.getFullYear()}-${String(dateNow.getMonth() + 1).padStart(2, '0')}-${String(dateNow.getDate()).padStart(2, '0')}`,
            "none",
            "3"
        );
        setRowsData(rowsData);
    };

    const handleSelectedPage = (event: any, value: number) => {
        setSelectedPage(value);
    };

    const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>(
        {}
    );

    const handleRowClick = (index: string) => {
        setExpandedRows((prevState) => ({
            ...prevState,
            [index]: !prevState[index],
        }));
    };

    const [open, setOpen] = useState<boolean>(true);

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            if (event.data.type === 'UPDATE_OPEN') {
                setOpen(event.data.open);
            }
        };

        window.addEventListener('message', handleMessage);

        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []);

    useEffect(() => {
        console.log(`Menu is now ${open ? "open" : "closed"}`);
        handleResize();
    }, [open]);

    const [boxWidth, setBoxWidth] = useState<number>(0);

    const handleResize = () => {
        const windowWidth = document.documentElement.clientWidth;
        // console.log("updateDimensions-window.innerWidth=", window.innerWidth);
        console.log("updateDimensions-document.documentElement.clientWidth=", document.documentElement.clientWidth);
        if (windowWidth > 910) {
            if (open) {
                setBoxWidth(windowWidth - 320);
            } else {
                setBoxWidth(windowWidth - 128);
            }
        } else {
            setBoxWidth(windowWidth - 112);
        }
    };

    useEffect(() => {
        handleResize();
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);


    const downFile = async () => {
        try {
            await getCsvUrlAndDownload(
                filters.startDate,
                filters.endDate,
                filters.caddie,
                filters.weekday
            );
        } catch (error) {
            setOpenDialog(true);
        }
    }

    const getCaddyList = () => {
        return [
            { id: 0, caddy_no: "none", caddy_name: "\u00A0" },
            ...caddies
        ].filter(caddie => !(caddie.caddy_no === "0" && caddie.caddy_name === ""));
    }
    const fixLowestEvaluationIds = (lowest: Lowest[], evaluationsList: Evaluation[]) => {
        if (!Array.isArray(lowest)) {
            return [];
        }
        return lowest.map((item) => {
            const evaluation = evaluationsList.find(evaluation => evaluation.score === item.score);
            return {
                ...item,
                evaluation_id: evaluation ? evaluation.id : item.evaluation_id
            };
        });
    };

    const fixedLowest = fixLowestEvaluationIds(rows.lowest, evaluationsList);


    const getMaxScoreId = () => {
        if (evaluationsList.length === 0) return -1;
        const maxScoreEvaluation = evaluationsList.reduce((max, evaluation) =>
            evaluation.score > max.score ? evaluation : max
        );
        return maxScoreEvaluation.id;
    };

    const processRowsData = (rows: Questionnaire, lowest: Lowest[]) => {
        const maxScoreId = getMaxScoreId();
        return rows.data.map((row) => {
            const matchingSurveys = row.survey.filter(survey =>
                survey.answers?.some(answer =>
                    lowest?.some(low =>
                        low.question_id === answer.id && low.evaluation_id === answer.answer
                        && answer.answer !== maxScoreId
                    )
                )
            );

            const sortedSurveys = [
                ...matchingSurveys,
                ...row.survey.filter(survey => !matchingSurveys.includes(survey))
            ];

            const showLowest = matchingSurveys.length >= 2;

            return {
                ...row,
                survey: sortedSurveys,
                showLowest,
            };
        });
    };

    const fixedRows = processRowsData(rows, fixedLowest);


    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box
                id="box-to-watch"
                flex={1}
                overflow="auto"
                sx={{ display: "flex", flexDirection: "column" }}
            >
                <Breadcrumb items={breadcrumbItems} />
                {(showData()) &&
                    <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white" }}>
                        <div className="flex items-center space-x-4">
                            <div
                                style={{
                                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                                    height: "16px",
                                }}
                            ></div>
                            <Typography
                                noWrap
                                component="span"
                                sx={{ fontWeight: "bold" }}
                                color="primary"
                            >
                                絞り込み条件設定
                            </Typography>
                        </div>
                        <div className="flex flex-wrap items-center" style={{ marginTop: "15px" }}>
                            <div className="w-full sm:w-auto mr-14">
                                <FilterForm
                                    caddies={getCaddyList().map((caddie) => ({
                                        label: caddie.caddy_name,
                                        value: Number(caddie.caddy_no),
                                    }))}
                                    weekdays={weekdays}
                                    filters={filters}
                                    setFilters={setFilters}
                                />
                            </div>
                            <div className="w-full sm:w-auto flex space-x-4 mt-4">
                                <Button
                                    variant="outlined"
                                    onClick={handleApplyFilters}
                                    sx={{ fontWeight: "bold" }}
                                >
                                    絞り込み
                                </Button>
                                <Button
                                    variant="outlined"
                                    onClick={handleResetFilters}
                                    sx={{ fontWeight: "bold" }}
                                >
                                    リセット
                                </Button>
                            </div>
                        </div>
                    </Box>
                }



                {(showData() && dataSuccess) &&
                    <Box
                        borderRadius={2}
                        display="flex"
                        flexDirection="column"
                        sx={{
                            flexGrow: 1,
                            p: 2,
                            marginTop: "20px",
                            backgroundColor: "white",
                        }}
                    >
                        <div className="flex justify-between items-center mb-4">
                            <Typography noWrap component="span" variant="h3">
                                回答数 {rows.total.toString()} 件
                            </Typography>
                            <Button
                                variant="outlined"
                                onClick={downFile}
                                sx={{ fontWeight: "bold", className: "text-right" }}
                            >
                                CSV出力
                            </Button>
                        </div>
                        <Divider sx={{ bgcolor: "primary.main" }} />
                        <Box flex={1} overflow="auto">
                            <TableContainer style={{ width: boxWidth }} component={Paper}>
                                <AnswerTableComponent
                                    rows={fixedRows}
                                    lowest={fixedLowest}
                                    caddies={caddies}
                                    questions={questions}
                                    evaluations={evaluationsList}
                                    maxScoreId={getMaxScoreId()}
                                ></AnswerTableComponent>
                            </TableContainer>
                        </Box>
                        <Divider />
                        <PaginationComponent
                            count={rows.last_page}
                            page={rows.current_page}
                            onChange={handleSelectedPage}
                            selectedCount={selectedCount}
                            onCountChange={handleSelectChange}
                            counts={counts}
                        />
                    </Box>
                }
                {
                    ((questingsApi && evaluationApi && !caddiesApi)) ? (
                        <ErrorHint isNull={true} screenWidth={widthSize}
                        />)
                        : ((!questingsApiSuccess || !evaluationApiSuccess || !caddiesApiSuccess) || (questingsApi && evaluationApi && !caddiesApi)) ? (
                            <ErrorHint isAll={true} screenWidth={widthSize}
                                onButtonClick={() => handleErrorButtonClick("all")} />
                        )
                            : ((!questingsApi || !evaluationApi) && questingsApiSuccess) ? (
                                <ErrorHint jumpSetting={true} screenWidth={widthSize} />
                            ) : (!dataSuccess) ? (
                                <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                                    <ErrorHint onButtonClick={() => handleErrorButtonClick("api")} screenWidth={widthSize} />
                                </Box>
                            ) : (
                                <div></div>
                            )
                }

                <CsvErrorDialog
                    open={openDialog}
                    onClose={handleClose}
                    btnText="閉じる"
                    description="処理中にエラーが発生しました。しばらくしてからもう一度お試しください。"
                />
            </Box>
        </LocalizationProvider>
    );
};

export default Home;
