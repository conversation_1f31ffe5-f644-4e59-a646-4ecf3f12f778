"use client";
import React, { useState, useEffect, useRef } from "react";
import {
    <PERSON>,
    Typography,
    FormControlLabel,
    Checkbox,
    Button,
    TextField,
    Radio,
    RadioGroup,
    MenuItem,
    Collapse,
    IconButton,
    Portal,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { MobileDatePicker } from "@mui/x-date-pickers/MobileDatePicker";
import InputAdornment from '@mui/material/InputAdornment';
import { Dayjs } from "dayjs";
import dayjs from "@/utils/dayjs"
import CourseSelectComponent from "@/components/onlinecompe/course-select-component"
import { SelectOptions } from "@/components/onlinecompe/course-select-component"
import PriorityItem from "@/components/onlinecompe/priority-item"
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import ApiDialog from "@/components/onlinecompe/api-dialog"
import HideHoleDialog from "@/components/onlinecompe/hide-hole-dialog"
import HideHolePageErrorDialog from "@/components/onlinecompe/hide-hole-page-error-dialog"
import { HideHoleTableProps, HideCourseProps, HideHoleProps } from "@/components/onlinecompe/hide-hole-table-component"
import { getOnlineCompeLatestNo, uploadImg, getCourses, createOnlineCompe, getDefaultSetting } from "@/api/online-compe-api"
import { OnlineCompeDefaultSetting } from "@/models/compe/req/online-compe-default-setting";
import { UploadImgResp } from "@/models/compe/resp/online-compe-creation";
import { Course, Hole } from "@/models/course/resp/course-resp";
import {
    OnlineCompe,
    Basic,
    CompeSetting,
    CompeTypeSetting,
    OtherSetting,
    Duration,
    Handy,
    Peoria,
    PrivateSetting,
    HiddenHoleSetting
} from "@/models/compe/req/online-compe-creation";
import { LeadBoardChange, NaviEntry, Mail, HdcpTypes, Calculation, MarkerSetting } from "@/models/compe/enum-types";
import { useRouter } from "next/navigation";
const Home = () => {
    const lineStyle = {
        border: '1px solid #999',
        marginTop: '20px',
        marginBottom: '20px',
        marginLeft: '32px'
    };
    const itemStyle = {
        textAlign: 'right',
        marginRight: '20px',
        width: '160px',
        fontWeight: 'bold',
        fontSize: '16px'
    };
    const compeStyle = {
        textAlign: 'right',
        marginRight: '10px',
        width: '180px',
        fontWeight: 'bold',
        fontSize: '16px',
        height: '24px'
    };
    const router = useRouter();
    const [compeDataError, setCompeDataError] = useState<string[]>([]);
    const checkCompeData = () => {
        let errorList: string[] = [];
        if (competitionName.trim() === '') {
            errorList.push('compeName');
        }
        if (companyName.trim() === '') {
            errorList.push('companyName');
        }
        if (ranking === '') {
            errorList.push('ranking')
        }
        if (selectedCourses.filter(item => item === '').length === 4) {
            errorList.push('course')
        }
        if (compeTypeValue === undefined) {
            errorList.push('compeType')
        }
        if (atBats === undefined && pairChecked) {
            errorList.push('atBats')
        }
        checkSelectedCourseDuplication();
        if (errorList.length === 0) {
            postOnlineCompe(createCompeData());
        }
        setCompeDataError(errorList);
    }
    const createCompeData = (): OnlineCompe => {
        const compeDuration: Duration = {
            from: startDate.format(),
            to: endDate.format()
        };
        const basicData: Basic = {
            compe_no: compeNo,
            compe_name: competitionName,
            duration: compeDuration,
            target_office_type: 0,
            target_office: localStorage.getItem('office_key') ?? null,
            target_office_list: null,
            organizer: companyName,
            participation_fee: Number(fee.replaceAll(",", "")),
            promotional_image: imgUrl.current ? imgUrl.current : '',
            old_compe: null
        }
        const compeSettingData: CompeSetting = {
            entry_from_navi: naviEntryPermission,
            ranking_aggregation: Number(ranking),
            round: roundType,
            prize_setting: [{ type: '' }],
        }
        const compeTypeSettingData: CompeTypeSetting = {
            type: 1,
            handy: handyChecked ? getHandySetting() : null,
            peoria: pairChecked ? getPeriaSetting() : null,
        }
        const otherSettingData: OtherSetting = {
            marker_setting: markerSetting,
            leadboard_change: {
                type: leaderBoard,
                default: leaderBoard === LeadBoardChange.Abled ? leaderBoardType : defaultBoardType,
            }
        }
        const privateSettingData: PrivateSetting = {
            course_setting: {
                "1": selectedCourses[0],
                "2": selectedCourses[1],
                "3": selectedCourses[2],
                "4": selectedCourses[3],
            },
            hidden_hole: getHiddenHole(),
        }
        const compeData: OnlineCompe = {
            basic: basicData,
            compe_setting: compeSettingData,
            compe_type_setting: compeTypeSettingData,
            other_setting: otherSettingData,
            private_setting: privateSettingData,
        }
        return {
            basic: basicData,
            compe_setting: compeSettingData,
            compe_type_setting: compeTypeSettingData,
            other_setting: otherSettingData,
            private_setting: privateSettingData,
        };
    }
    const getHiddenHoleIndex = (holes: HideHoleProps[]): number[] => {
        const indexList: number[] = [];
        holes.map(hole => {
            if (hole.isSelected) {
                indexList.push(Number(hole.id) - 1)
            }
        });
        return indexList;
    }
    const getHiddenHole = (): HiddenHoleSetting[] => {
        const hiddenHoleSettingList: HiddenHoleSetting[] = [];
        hideHole.courses.map(course => {
            const hiddenHoleSetting: HiddenHoleSetting = {
                course_index: course.courseIndex,
                course_name: course.name,
                hidden_hole_index: getHiddenHoleIndex(course.holes)
            }
            hiddenHoleSettingList.push(hiddenHoleSetting);
        })
        return hiddenHoleSettingList;
    }
    const getPriorityItems = (type: string, itemIndex: number): string => {
        switch (type) {
            case "handy":
                const handyItem = priorityItems.filter(item => item.index === itemIndex);
                if (handyItem.length > 0) {
                    return handyItem[0].value;
                } else {
                    return ""
                }
            case "peria":
                const periaItem = periaPriorityItems.filter(item => item.index === itemIndex);
                if (periaItem.length > 0) {
                    return periaItem[0].value;
                } else {
                    return ""
                }
            default:
                return "";
        }
    }
    const getHandySetting = (): Handy => {
        const handyData: Handy = {
            distribution: delivery,
            ranking_order: {
                "1": getPriorityItems('handy', 1),
                "2": getPriorityItems('handy', 2),
                "3": getPriorityItems('handy', 3),
                "4": getPriorityItems('handy', 4),
            },
            handicap: {
                type: hdcpType,
                hdcp_date: hdcpDate.format("YYYY-MM-DD"),
                hdcp_allowance: hdcpAllowance
            },
            net_computation_type: calculation,
        }
        return handyData;
    }
    const getPeriaSetting = (): Peoria => {
        const periaData: Peoria = {
            distribution: rankingMail,
            ranking_order: {
                "1": getPriorityItems('peria', 1),
                "2": getPriorityItems('peria', 2),
                "3": getPriorityItems('peria', 3),
                "4": getPriorityItems('peria', 4),
            },
            aggregation_method: {
                type: Number(totalling)
            },
            par_limit: {
                type: atBats ?? 0,
                par_n: atBatsNValue === "" ? -1 : Number(atBatsNValue),
                par_x: atBatsXValue === "" ? -1 : Number(atBatsXValue),
            },
            handicap_upper_limit: {
                men: manHandiValue === "" ? null : Number(manHandiValue),
                women: womanHandiValue === "" ? null : Number(womanHandiValue),
            }
        }
        return periaData;
    }
    //API情報を取得
    const fetchCompeData = async () => {
        const tasks = [getCompeNo(), getCoursesData(), getCompeDefaultSetting()];
        try {
            await Promise.all(tasks);
        } catch (error) {

        }
    }
    //コンペNo取得
    const getCompeNo = async () => {
        try {
            const lastNum = await getOnlineCompeLatestNo();
            if (lastNum.compe_no > 0) {
                setCompeNo(lastNum.compe_no);
            }
        } catch (error) {

        }
    }
    //基本設定情報を取得
    const getCompeDefaultSetting = async () => {
        try {
            const result = await getDefaultSetting();
            if (Object.keys(result).length > 0) {
                setCompeDefaultSeting(result)
            }
        } catch (error) {

        }
    }
    const setCompeDefaultSeting = (defaultSetting: OnlineCompeDefaultSetting) => {
        setNaviEntryPermission(defaultSetting.compe_setting.entry_from_navi);
        setRanking(defaultSetting.compe_setting.ranking_aggregation.toString());
        setRoundType(defaultSetting.compe_setting.round);
        setLeaderBoard(defaultSetting.other_setting.leadboard_change.type);
        if (defaultSetting.other_setting.leadboard_change.type === 1) {
            setLeaderBoardType(defaultSetting.other_setting.leadboard_change.default);
        } else {
            setDefaultBoardType(defaultSetting.other_setting.leadboard_change.default);
        }
        setHdcpType(defaultSetting.handicap.type);
        setMarkerSetting(defaultSetting.other_setting.marker_setting);
    }
    //コース取得
    const getCoursesData = async () => {
        try {
            const coursesList = await getCourses();
            if (Object.keys(coursesList).length !== 0) {
                setCoursesDataList(coursesList.data);
            }
        } catch (error) {

        }
    }
    //新規したコンペ情報をサーバに送る
    const postOnlineCompe = async (compe: OnlineCompe) => {
        try {
            const res = await createOnlineCompe(compe);
            setOpenApiDialog(true)
            setCreateCompeSucceed(res);
        } catch (error) {
            setOpenApiDialog(true)
            setCreateCompeSucceed(false);
        }
    }
    const [coursesDataList, setCoursesDataList] = useState<Course[]>();
    useEffect(() => {
        fetchCompeData();
        setGolfName(localStorage.getItem('office_name') ?? '');
    }, []);
    useEffect(() => {
        const courseNameList: SelectOptions[] = [];
        const selectedCourses: string[] = [];
        coursesDataList?.map(course => {
            courseNameList.push({ value: course.course_name, label: course.course_name });
            selectedCourses.push(course.course_name);
        });
        const coursesForHideHole: HideCourseProps[] = [];
        selectedCourses.map((item, index) => {
            const courseData = coursesDataList?.filter(course => course.course_name === item);
            if (courseData && courseData.length > 0) {
                const course: HideCourseProps = {
                    courseIndex: courseData[0].course_index,
                    name: item,
                    holes: getHideCourseholes(courseData[0].holes),
                    onHoleSelected: onHoleSelected
                }
                coursesForHideHole.push(course);
            }
        });
        courseNameList.unshift({ value: '', label: '\u00A0' });
        setHideHole({ courses: coursesForHideHole });
        setCourseOptions(courseNameList);
    }, [coursesDataList])
    //アップロード画像のurlを取得
    const getImgUrl = async (formData: FormData): Promise<string> => {
        try {
            const resp: UploadImgResp = await uploadImg(formData, compeNo);
            return resp.data.url;
        } catch (error) {
            return "";
        }
    }
    const uploadImgToApi = async () => {
        if (imgFile.current !== undefined) {
            const formData = new FormData();
            formData.append('file', imgFile.current);
            imgUrl.current = await getImgUrl(formData);
        }
    }
    const [openApiDialog, setOpenApiDialog] = useState<boolean>(false)
    const [createCompeSucceed, setCreateCompeSucceed] = useState<boolean>(false)
    const handleCloseErrorDialog = () => {
        if (createCompeSucceed) {
            router.push("/onlinecompe/list");
        }
        setOpenApiDialog(false);
    };
    //コンペ基本情報 start
    const [startDate, setStartDate] = useState(dayjs().tz().startOf('day'));
    const [endDate, setEndDate] = useState(dayjs().tz().endOf('day'));
    const [hdcpDate, setHdcpDate] = useState(dayjs().tz());
    const handleDateChange = (name: string, value: Dayjs | null) => {
        if (!value) return;
        switch (name) {
            case 'startDate':
                setStartDate(value.tz().startOf('day'));
                break;
            case 'endDate':
                setEndDate(value.tz().endOf('day'));
                break;
            case 'hdcpDate':
                setHdcpDate(value.tz());
                break;
            default:
                break;
        }
    }
    const [hdcpAllowance, setHdcpAllowance] = useState<number>(100);
    const handleHdcpAllowanceChange = (event: any) => {
        const inputValue = event.target.value;
        if (inputValue === "" || /^[0-9\b]+$/.test(inputValue)) {
            setHdcpAllowance(inputValue === "" ? 0 : parseInt(inputValue, 10));
        }
    };
    const [golfName, setGolfName] = useState('');
    const [compeNo, setCompeNo] = useState(0);
    const [fee, setFee] = useState('');
    const [companyName, setCompanyName] = useState('');
    const [competitionName, setCompetitionName] = useState('');
    const handleCompetitionNameChange = (event: any) => {
        setCompetitionName(event.target.value);
        if (event.target.value.trim() !== '') {
            setCompeDataError(prev => prev.filter(error => error !== 'compeName'))
        }
    };
    const handleCompanyNameChange = (event: any) => {
        setCompanyName(event.target.value);
        if (event.target.value.trim() !== '') {
            setCompeDataError(prev => prev.filter(error => error !== 'companyName'))
        }
    };
    const handleFeeChange = (event: any) => {
        let inputValue = event.target.value;
        inputValue = inputValue.replace(/[^0-9.]/g, '');
        if (inputValue.trim() !== '') {
            setCompeDataError(prev => prev.filter(error => error !== 'fee'))
        }
        const formattedValue = inputValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        setFee(formattedValue);
    };
    const [golfType, setGolfType] = useState('self');
    const handleGolfChange = (event: any) => {
        setGolfType(event.target.value);
    };
    const [selectedFile, setSelectedFile] = useState('');
    const imgFile = useRef<File>();
    const imgUrl = useRef<string>();

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const maxSize: number = 5 * 1024 * 1024;
        if (event.target.files && event.target.files.length > 0) {
            const inputFile = event.target.files[0];
            if (inputFile.size > maxSize) {
                setCompeDataError(prev => [...prev, 'file', 'overSize']);
            } else {
                setSelectedFile(event.target.files[0].name);
                imgFile.current = event.target.files[0];
                setCompeDataError(prev => prev.filter(error => error !== 'file' && error !== 'overSize'))
                uploadImgToApi();
            }
        }
    };
    const fileInputRef = useRef<HTMLInputElement>(null);
    //コンペ基本情報 end
    //コンペ設定 start
    const [naviEntryPermission, setNaviEntryPermission] = useState<NaviEntry>(NaviEntry.Disabled)
    const handleNaviEntryPermissionChange = (event: any) => {
        setNaviEntryPermission(Number(event.target.value));
    };
    const [roundType, setRoundType] = useState('1');
    const handleRoundChange = (event: any) => {
        setRoundType(event.target.value);
    };
    const [ranking, setRanking] = useState('');
    const rankingOptions = [
        { value: '0', label: '全ホール表示' },
        { value: '1', label: '最終3ホールを隠す' },
        { value: '2', label: '最終6ホールを隠す' },
        { value: '3', label: '最終9ホールを隠す' },
    ];
    const handleRankingChange = (event: any) => {
        setRanking(event.target.value);
        setCompeDataError(prev => prev.filter(error => error !== 'ranking'))
    };
    const [courseOptions, setCourseOptions] = useState<SelectOptions[]>([]);
    const [selectedCourses, setSelectedCourses] = useState<string[]>(["", "", "", ""])
    const getHideCourseholes = (holes: Hole[]): HideHoleProps[] => {
        const hideCourseholes: HideHoleProps[] = [];
        const indexList: string[] = [];
        for (const hole of holes) {
            if (!indexList.includes(hole.hole_index)) {
                indexList.push(hole.hole_index);
                hideCourseholes.push({ id: String(Number(hole.hole_index) + 1), isSelected: false, strokes: hole.used_par });
            }
        }
        return hideCourseholes;
    };
    useEffect(() => {
        const courseList = selectedCourses.filter((item) => item !== "");
        const coursesForHideHole: HideCourseProps[] = [];
        if (courseList.length === 0) {
            for (const course of courseOptions) {
                courseList.push(course.value);
            }
        }
        courseList.map((item, index) => {
            const courseData = coursesDataList?.filter(course => course.course_name === item);
            if (courseData && courseData.length > 0) {
                const course: HideCourseProps = {
                    courseIndex: courseData[0].course_index,
                    name: item,
                    holes: getHideCourseholes(courseData[0].holes),
                    onHoleSelected: onHoleSelected
                }
                coursesForHideHole.push(course);
            }
        });
        checkSelectedCourseDuplication();
        setHideHole({ courses: coursesForHideHole });
    }, [selectedCourses]);
    const [selectedCourseDuplicate, setSelectedCourseDuplicate] = useState<boolean[]>([false, false, false, false]);
    const [firestSelectedCourse, setFirestSelectedCourse] = useState("");
    const removeSelectedCourseError = () => {
        setCompeDataError(prev => prev.filter(error => error !== 'course'));
    }
    const handleFirstCourseChange = (event: any) => {
        setSelectedCourses(prev => prev.map((item, index) => index === 0 ? event.target.value : item));
        removeSelectedCourseError();
        setFirestSelectedCourse(event.target.value);
    };
    const [secondSelectedCourse, setSecondSelectedCourse] = useState("");
    const handleSecondCourseChange = (event: any) => {
        setSelectedCourses(prev => prev.map((item, index) => index === 1 ? event.target.value : item));
        removeSelectedCourseError();
        setSecondSelectedCourse(event.target.value);
    };
    const [thirdSelectedCourse, setThirdSelectedCourse] = useState("");
    const handleThirdCourseChange = (event: any) => {
        setSelectedCourses(prev => prev.map((item, index) => index === 2 ? event.target.value : item));
        removeSelectedCourseError();
        setThirdSelectedCourse(event.target.value);
    };
    const [fourthSelectedCourse, setFourthSelectedCourse] = useState("");
    const handleFourthCourseChange = (event: any) => {
        setSelectedCourses(prev => prev.map((item, index) => index === 3 ? event.target.value : item));
        removeSelectedCourseError();
        setFourthSelectedCourse(event.target.value);
    };
    //選択したコースが重複していないか
    const checkSelectedCourseDuplication = () => {
        const map = new Map<string, number[]>();
        const trimCourses = selectedCourses.filter(item => item !== '');
        if (trimCourses.length === 0) {
            return;
        }
        selectedCourses.forEach((item, index) => {
            if (item === '') return;
            if (!map.has(item)) {
                map.set(item, [index]);
            } else {
                map.get(item)!.push(index);
            }
        });
        const duplicateIndexes: number[] = [];
        map.forEach(indexes => {
            if (indexes.length > 1) {
                duplicateIndexes.push(...indexes);
            }
        });
        setSelectedCourseDuplicate(prev => prev.map((item, index) => duplicateIndexes.includes(index) ? true : false))
    }
    const [compeTypeValue, setCompeTypeValue] = useState<number>(1);
    const handleCompeTypeValue = (event: any) => {
        setCompeTypeValue(event.target.value);
        setCompeDataError(prev => prev.filter(error => error !== 'compeType'))
    };
    //コンペ設定 end
    const [delivery, setDelivery] = useState<Mail>(Mail.Abled);
    const handleDeliveryChange = (event: any) => {
        setDelivery(Number(event.target.value));
    };
    const [hdcpType, setHdcpType] = useState<HdcpTypes>(HdcpTypes.WHS);
    const handleHdcpTypeChange = (event: any) => {
        setHdcpType(Number(event.target.value));
    };
    const [calculation, setCalculation] = useState<Calculation>(Calculation.Hdcp);
    const handleCalculationChange = (event: any) => {
        setCalculation(Number(event.target.value));
    };
    const [totalling, setTotalling] = useState('0');
    const totallingOptions = [
        { value: '0', label: 'ペリア(6H)' },
        { value: '1', label: '新ペリア(12H)' },
        { value: '2', label: '新新ペリア(9H)' },
    ];
    const handleTotallingChange = (event: any) => {
        setTotalling(event.target.value);
    };
    const atBatsOptions = [
        { label: '制限なし', value: 0, N: false, X: false },
        { label: 'PAR×2', value: 1, N: false, X: false },
        { label: 'PAR×2-1', value: 2, N: false, X: false },
        { label: 'PAR×3', value: 3, N: false, X: false },
        { label: 'PAR×3-1', value: 4, N: false, X: false },
        { label: 'PAR+X', value: 5, N: false, X: true },
        { label: 'X', value: 6, N: false, X: true },
        { label: 'PAR×N+X', value: 7, N: true, X: true }
    ];
    const [atBats, setAtBats] = useState<number>();
    const [showAtBatsN, setShowAtBatsN] = useState(false);
    const [showAtBatsX, setShowAtBatsX] = useState(false);
    const [atBatsNValue, setAtBatsNValue] = useState("");
    const [atBatsXValue, setAtBatsXValue] = useState("");
    const handleAtBatsNValueChange = (event: any) => {
        setAtBatsNValue(event.target.value);
    };
    const handleAtBatsXValueChange = (event: any) => {
        setAtBatsXValue(event.target.value);
    };
    const handleAtBatsChange = (event: any) => {
        setAtBats(Number(event.target.value));
        const option = atBatsOptions.find(option => option.value === Number(event.target.value));
        setCompeDataError(prev => prev.filter(error => error !== 'atBats'))
        setShowAtBatsN(option?.N ? option?.N : false);
        setShowAtBatsX(option?.X ? option?.X : false);
    }

    const priorityItemOptions = [
        'HDCP',
        '生年月日',
        '性別',
        'カウントバック',
        'マッチング・カード方式',
        'スタートホールからのグロス',
        '最終コースからのグロス'
    ];
    const [priorityItems, setPriorityItems] = useState([
        { id: "item1", index: 1, value: "生年月日" },
        { id: "item2", index: 2, value: "性別" },
        { id: "item3", index: 3, value: "カウントバック" },
        { id: "item4", index: 4, value: "HDCP" },
    ]);
    const movePriority = (fromIndex: number, toIndex: number) => {
        setPriorityItems((prev) => {
            const newItems = [...prev];
            const movedItem = newItems.find((item) => item.index === fromIndex);
            const targetItem = newItems.find((item) => item.index === toIndex);
            if (!movedItem || !targetItem) return prev;
            [movedItem.index, targetItem.index] = [targetItem.index, movedItem.index];
            return newItems.sort((a, b) => a.index - b.index);
        })
    };
    const handlePriorityChange = (id: string, value: string) => {
        setPriorityItems((prev) =>
            prev.map((item) => (item.id === id ? { ...item, value } : item))
        );
    };
    const [periaPriorityItems, setPeriaPriorityItems] = useState([
        { id: "item1", index: 1, value: "生年月日" },
        { id: "item2", index: 2, value: "性別" },
        { id: "item3", index: 3, value: "カウントバック" },
        { id: "item4", index: 4, value: "HDCP" },
    ]);
    const movePeriaPriority = (fromIndex: number, toIndex: number) => {
        setPeriaPriorityItems((prev) => {
            const newItems = [...prev];
            const movedItem = newItems.find((item) => item.index === fromIndex);
            const targetItem = newItems.find((item) => item.index === toIndex);
            if (!movedItem || !targetItem) return prev;
            [movedItem.index, targetItem.index] = [targetItem.index, movedItem.index];
            return newItems.sort((a, b) => a.index - b.index);
        })
    };
    const handlePeriaPriorityChange = (id: string, value: string) => {
        setPeriaPriorityItems((prev) =>
            prev.map((item) => (item.id === id ? { ...item, value } : item))
        );
    };
    const compeType = [
        { value: 1, label: '個人戦' },
    ];
    const [handyChecked, setHandyChecked] = useState(false);
    const [pairChecked, setPairChecked] = useState(false);
    const [rankingMail, setRankingMail] = useState<Mail>(Mail.Disabled);
    const handleRankingMailChange = (event: any) => {
        setRankingMail(Number(event.target.value));
    };

    const [manHandiValue, setManHandiValue] = useState("");
    const [womanHandiValue, setWomanHandiValue] = useState("");

    const handleManHandiChange = (e: any) => {
        const inputValue = e.target.value;
        if (inputValue === "") {
            setManHandiValue("");
            return;
        }
        if (/^-?\d*$/.test(inputValue)) {
            const isNegative = inputValue.startsWith('-');
            let numPart = isNegative ? inputValue.slice(1) : inputValue;
            if (numPart === "" && isNegative) {
                setManHandiValue(inputValue);
                return;
            }
            numPart = numPart.replace(/^0+/, '');
            if (numPart === '') numPart = '0';
            const newValue = isNegative ? `-${numPart}` : numPart;
            setManHandiValue(newValue);
        }
    };
    const manHandleIncrement = () => {
        if (manHandiValue === "") {
            setManHandiValue('0');
        } else {
            setManHandiValue((prev) => (Number(prev) + 1).toString());
        }
    };
    const manHandleDecrement = () => {
        if (manHandiValue === "") {
            setManHandiValue('0');
        } else {
            setManHandiValue((prev) => (Number(prev) - 1).toString());
        }
    };
    const handleWomanHandiChange = (e: any) => {
        const inputValue = e.target.value;
        if (inputValue === "") {
            setWomanHandiValue("");
            return;
        }
        if (/^-?\d*$/.test(inputValue)) {
            const isNegative = inputValue.startsWith('-');
            let numPart = isNegative ? inputValue.slice(1) : inputValue;
            if (numPart === "" && isNegative) {
                setWomanHandiValue(inputValue);
                return;
            }
            numPart = numPart.replace(/^0+/, '');
            if (numPart === '') numPart = '0';
            const newValue = isNegative ? `-${numPart}` : numPart;
            setWomanHandiValue(newValue);
        }
    };
    const womanHandleIncrement = () => {
        if (womanHandiValue === '') {
            setWomanHandiValue('0')
        } else {
            setWomanHandiValue((prev) => (Number(prev) + 1).toString());
        }
    };
    const womanHandleDecrement = () => {
        if (womanHandiValue === '') {
            setWomanHandiValue('0')
        } else {
            setWomanHandiValue((prev) => (Number(prev) - 1).toString());
        }
    };
    const [openHideHoleDialog, setOpenHideHoleDialog] = useState(false);
    const showHideHoleDialog = () => {
        setOpenHideHoleDialog(true);
    };
    const closeHideHoleDialog = () => {
        setOpenHideHoleDialog(false);
    };
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const showErrorDialog = () => {
        setOpenErrorDialog(true);
    };
    const closeErrorDialog = () => {
        setOpenErrorDialog(false);
    };
    const selectedHoleNumber = useRef<number>(3);
    useEffect(() => {
        if (totalling == '0') {
            selectedHoleNumber.current = 3;
            setHideHoleTitle('ペリア(6H)');
        } else if (totalling == '1') {
            setHideHoleTitle('新ペリア(12H)');
            selectedHoleNumber.current = 6;
        } else {
            setHideHoleTitle('新新ペリア(9H)');
            selectedHoleNumber.current = 5;
        }
    }, [totalling]);
    const [hideHoleTitle, setHideHoleTitle] = useState("");
    const [hideHoleSetSucceed, setHideHoleSetSucceed] = useState(false);
    const cancelHideHoleSetting = () => {
        setHideHoleSetSucceed(false);
        closeHideHoleDialog();
    }
    const onHoleSelected = (courseName: string, holeId: string) => {
        setHideHole(prev => ({
            ...prev,
            courses: prev.courses.map(course =>
                courseName !== course.name ?
                    course : {
                        ...course,
                        holes: course.holes.map(hole =>
                            holeId !== hole.id ?
                                hole : {
                                    ...hole,
                                    isSelected: !hole.isSelected
                                }
                        )
                    }
            )
        })
        );
    };
    const checkHideHole = () => {
        for (const course of hideHole.courses) {
            const holes = course.holes.filter((hole) => hole.isSelected);
            if (holes.length > selectedHoleNumber.current) {
                showErrorDialog();
                break
            }
        }
    }
    const checkHideHoleSetting = () => {
        let isSelectedSucceed = true;
        switch (selectedHoleNumber.current) {
            case 3:
            case 6:
                for (const course of hideHoleRef.current.courses) {
                    const holes = course.holes.filter((hole) => hole.isSelected);
                    if (holes.length !== selectedHoleNumber.current) {
                        isSelectedSucceed = false;
                        break;
                    }
                }
                break;
            case 5:
                isSelectedSucceed = checkSelectedHoleWith9(hideHoleRef.current.courses);
                break
            default:
                isSelectedSucceed = false;
                break;

        }
        if (isSelectedSucceed) {
            closeHideHoleDialog();
            setHideHoleSetSucceed(true);
        } else {
            showErrorDialog();
        }
    }
    const checkSelectedHoleWith9 = (courses: HideCourseProps[]): boolean => {
        switch (courses.length) {
            case 1:
                const holes = courses[0].holes.filter((hole) => hole.isSelected);;
                if (holes.length !== 5) {
                    return false;
                }
                return true;
            default:
                const coursesSelectedHoleNumber: number[] = [];
                for (const course of courses) {
                    const holes = course.holes.filter((hole) => hole.isSelected);
                    coursesSelectedHoleNumber.push(holes.length)
                }
                if (coursesSelectedHoleNumber.filter(num => num === 4).length !== 1 ||
                    coursesSelectedHoleNumber.filter(num => num === 5).length !== (courses.length - 1)) {
                    return false;
                }
                return true;

        }
    }

    const [hideHole, setHideHole] = useState<HideHoleTableProps>({
        courses: [{
            courseIndex: "",
            name: "",
            holes: [{
                id: "",
                isSelected: false,
                strokes: "",
            }],
            onHoleSelected: onHoleSelected,
        }]
    });
    useEffect(() => {
        hideHoleRef.current = hideHole;
        checkHideHole();
    }, [hideHole]);

    const hideHoleRef = useRef<HideHoleTableProps>(hideHole);
    const getRandomNumber = (count: number, max: number): number[] => {
        const arr = Array.from({ length: max }, (_, i) => i + 1);
        for (let i = arr.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [arr[i], arr[j]] = [arr[j], arr[i]];
        }
        return arr.slice(0, count);
    }

    const getCourseRandomeHoleNumber = (courseNumber: number, index: number): number[] => {
        let randomArray: number[] = [];
        if (courseNumber == 1) {
            randomArray = getRandomNumber(selectedHoleNumber.current, 9)
        } else {
            if (selectedHoleNumber.current !== 5) {
                randomArray = getRandomNumber(selectedHoleNumber.current, 9);
            } else {
                if (index == 0) {
                    randomArray = getRandomNumber(4, 9);
                } else {
                    randomArray = getRandomNumber(5, 9);
                }
            }
        }
        return randomArray;
    }
    const randomSelecteHole = () => {
        setHideHole(prev => {
            const updatedCourses = prev.courses.map((course, index) => {
                const randomArray = getCourseRandomeHoleNumber(prev.courses.length, index);
                const updateHoles = course.holes.map(hole => ({
                    ...hole,
                    isSelected: randomArray.includes(Number(hole.id)),
                }));
                return {
                    ...course,
                    holes: updateHoles
                }
            })
            return {
                ...prev,
                courses: updatedCourses,
            };
        }
        );
    }
    //その他設定 start
    const [markerSetting, setMarkerSetting] = useState<MarkerSetting>(MarkerSetting.Disabled);
    const handleMarkerSettingChange = (event: any) => {
        setMarkerSetting(Number(event.target.value));
    };
    const [leaderBoard, setLeaderBoard] = useState<LeadBoardChange>(LeadBoardChange.Abled);
    const handleLeaderBoardChange = (event: any) => {
        setLeaderBoard(Number(event.target.value));
    };
    const boardOptions = [
        { value: 'net', label: 'ネットをデフォルト表示' },
        { value: 'gross', label: 'グロスをデフォルト表示' },
    ];
    const [leaderBoardType, setLeaderBoardType] = useState("net");
    const handleLeaderBoardTypeChange = (event: any) => {
        setLeaderBoardType(event.target.value);
    };
    const [defaultBoardType, setDefaultBoardType] = useState("gross");
    const handleDefaultBoardTypeChange = (event: any) => {
        setDefaultBoardType(event.target.value);
    };
    //その他設定 end
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box
                sx={{ display: "flex", flexDirection: "column", backgroundColor: '#f9f9f9', margin: "-24px" }}
            >
                <Box sx={{ display: 'flex', height: '65px', alignItems: 'center', backgroundColor: '#f3f3f3', flexDirection: "row", borderRadius: "5px", marginLeft: "18px", marginRight: "18px", marginTop: "25px", paddingLeft: "32px" }}>
                    <img src="/webapp/images/compe_ic_menu.png" style={{ width: '25px', height: '17px' }} />
                    <Typography variant="h2" sx={{ fontWeight: 'normal', marginLeft: "14px" }}>
                        新規コンペ登録
                    </Typography>
                </Box>
                <Box sx={{ display: 'flex', flex: 1, flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        コンペ<br />
                        基本情報
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                開催日
                            </Typography>
                            <MobileDatePicker
                                value={startDate}
                                onChange={(newValue) => handleDateChange("startDate", newValue)}
                                format="YYYY-MM-DD"
                                slotProps={{
                                    textField: {
                                        size: "small",
                                        InputProps: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <img src="/webapp/images/compe_ic_calendar.png" style={{ height: '15.24px', width: '20.67px', marginLeft: '-5px' }}></img>
                                                </InputAdornment>
                                            ),
                                            style: { width: "150px", height: "30px" },
                                        },
                                    },
                                }}>
                            </MobileDatePicker>
                            <div style={{ marginLeft: '10px', marginRight: '10px' }}>
                                ~
                            </div>
                            <MobileDatePicker
                                value={endDate}
                                onChange={(newValue) => handleDateChange("endDate", newValue)}
                                format="YYYY-MM-DD"
                                minDate={startDate}
                                slotProps={{
                                    textField: {
                                        size: "small",
                                        InputProps: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <img src="/webapp/images/compe_ic_calendar.png" style={{ height: '15.24px', width: '20.67px', marginLeft: '-5px' }}></img>
                                                </InputAdornment>
                                            ),
                                            style: { width: "150px", height: "30px" },
                                        },
                                    },
                                }}>
                            </MobileDatePicker>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                No
                            </Typography>
                            <div>
                                {compeNo}
                            </div>
                        </Box>
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginLeft: '20px',
                            marginTop: '18px',
                            marginRight: '20px'
                        }}>
                            <Typography component="div" sx={itemStyle}>
                                コンペ名
                            </Typography>
                            <TextField
                                value={competitionName}
                                onChange={handleCompetitionNameChange}
                                error={compeDataError?.includes('compeName')}
                                style={{ width: '644px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    style: { height: "30px" },
                                }}
                                size="small"
                                inputProps={{
                                    maxLength: 30
                                }}>

                            </TextField>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                対象ゴルフ場
                            </Typography>
                            <RadioGroup
                                value={golfType}
                                onChange={handleGolfChange}
                                row
                            >
                                <FormControlLabel
                                    value="self"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000 ',
                                        },
                                    }} />}
                                    label="自コース"

                                />
                                {/* <FormControlLabel 
                            value="group" 
                            control={<Radio sx={{
                                color: '#9e9e9e', 
                                '&.Mui-checked': {
                                  color: '#ff5722',
                                },
                              }}/>} 
                            label="グループコース" 
                            sx={{marginLeft: '20px'}}
                            /> */}
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}></Typography>
                            <Typography sx={{ fontWeight: 'bold' }}> ゴルフ場</Typography>
                            <Typography sx={{ marginLeft: '20px', width: 'auto' }}> {golfName}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                主催者
                            </Typography>
                            <TextField
                                value={companyName}
                                error={compeDataError?.includes('companyName')}
                                onChange={handleCompanyNameChange}
                                style={{ width: '644px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    inputMode: "numeric",
                                    style: { height: "30px" },
                                }}
                                size="small"
                            ></TextField>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                参加料金
                            </Typography>
                            <TextField
                                value={fee}
                                onChange={handleFeeChange}
                                style={{ width: '150px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    inputMode: "numeric",
                                    style: { height: "30px" },
                                }}
                                size="small"
                            ></TextField>
                            <Typography sx={{ marginLeft: "10px" }}>円(税込)</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px', marginBottom: '20px' }}>
                            <Typography component="div" sx={itemStyle}>宣伝画像登録</Typography>
                            <input
                                type="file"
                                accept="image/jpeg"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                name="ファイルを選択"
                                style={{ display: 'none' }}
                            />
                            <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                border: '1px solid',
                                borderColor: compeDataError?.includes('file') ? 'red' : '#CCCCCC',
                                borderTopLeftRadius: '4px',
                                borderBottomLeftRadius: '4px',
                                width: '228px',
                                height: "30px"
                            }}>
                                <TextField size="small"
                                    sx={{ width: '100%', '& .MuiOutlinedInput-root': { '& fieldset': { border: 'none' } } }}
                                    disabled
                                    value={selectedFile}></TextField>
                            </Box>
                            <Button
                                variant="text"
                                onClick={() => fileInputRef.current?.click()}
                                sx={{
                                    height: '30px',
                                    border: '1px solid #CCCCCC',
                                    borderTopLeftRadius: '0px',
                                    borderBottomLeftRadius: '0px',
                                    borderTopRightRadius: '4px',
                                    borderBottomRightRadius: '4px',
                                    backgroundColor: '#CCCCCC',
                                    color: 'black',
                                    width: "120px",
                                    fontSize: '14px'
                                }}
                            >
                                ファイルを選択
                            </Button>
                            <Typography component="div" sx={{ fontSize: '12px', ml: '10px' }}>
                                ＊ナビからコンペにエントリーする際、<br></br>  コンペの詳細情報として表示されます

                            </Typography>
                            {/* {selectedFile && <img src={URL.createObjectURL(selectedFile)} alt="preview" />} */}
                        </Box>
                        {compeDataError?.includes('overSize') &&
                            <Typography sx={{ color: 'red', marginLeft: '200px', marginTop: '-20px', fontSize: '12px', marginBottom: '20px' }}>
                                5M以下のJPG画像を選択してください
                            </Typography>}
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flex: 1, flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        コンペ設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ナビからエントリー
                            </Typography>
                            <RadioGroup
                                value={naviEntryPermission}
                                onChange={handleNaviEntryPermissionChange}
                                row
                            >
                                <FormControlLabel
                                    value={NaviEntry.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="許可しない"

                                />
                                <FormControlLabel
                                    value={NaviEntry.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="許可する"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ランキング集計
                            </Typography>
                            <TextField
                                select
                                error={compeDataError?.includes('ranking')}
                                value={ranking}
                                size="small"
                                name="ranking"
                                variant="outlined"
                                style={{ width: '190px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    style: { height: "30px" },
                                }}
                                onChange={handleRankingChange}
                            >
                                {rankingOptions.map((option, index) => (
                                    <MenuItem key={index} value={option.value} style={{ fontSize: '14px' }}>
                                        {option.label}
                                    </MenuItem>
                                ))}
                            </TextField>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ラウンド
                            </Typography>
                            <RadioGroup
                                value={roundType}
                                onChange={handleRoundChange}
                                row
                            >
                                <FormControlLabel
                                    value="0.5"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="0.5ラウンド"

                                />
                                <FormControlLabel
                                    value="1"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="1ラウンド"
                                    sx={{ marginLeft: '20px' }}
                                />
                                <FormControlLabel
                                    value="1.5"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="1.5ラウンド"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'top', marginLeft: '20px', marginTop: '18px', marginBottom: '25px' }}>
                            <Typography component="div" sx={itemStyle} style={{ marginTop: '5px' }}>
                                使用コース
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                                <CourseSelectComponent
                                    index={1}
                                    errorList={compeDataError}
                                    duplication={selectedCourseDuplicate}
                                    selectedOption={firestSelectedCourse}
                                    roundType={roundType}
                                    onOptionChange={handleFirstCourseChange}
                                    options={courseOptions}></CourseSelectComponent>
                                <CourseSelectComponent
                                    index={2}
                                    errorList={compeDataError}
                                    duplication={selectedCourseDuplicate}
                                    selectedOption={secondSelectedCourse}
                                    roundType={roundType}
                                    onOptionChange={handleSecondCourseChange}
                                    options={courseOptions}
                                ></CourseSelectComponent>
                                <CourseSelectComponent
                                    index={3}
                                    errorList={compeDataError}
                                    duplication={selectedCourseDuplicate}
                                    selectedOption={thirdSelectedCourse}
                                    roundType={roundType}
                                    onOptionChange={handleThirdCourseChange}
                                    options={courseOptions}
                                ></CourseSelectComponent>
                                <CourseSelectComponent
                                    index={4}
                                    errorList={compeDataError}
                                    duplication={selectedCourseDuplicate}
                                    selectedOption={fourthSelectedCourse}
                                    roundType={roundType}
                                    onOptionChange={handleFourthCourseChange}
                                    options={courseOptions}
                                ></CourseSelectComponent>
                            </Box>
                        </Box>
                        {/* <Box sx= {{display: 'flex', flexDirection: 'row', alignItems: 'top', marginLeft: '20px', marginTop: '18px'}}>
                        <Typography component="div" sx={itemStyle} style={{marginTop: '5px'}}>
                            入賞設定
                        </Typography>
                        <Button
                        endIcon={<img src={"/webapp/images/compe_ic_prize.png" } alt="logo" width={"14px"} height={"12.64px"} />}
                        style={{width: '120px', height: '30px', backgroundColor: '#384f81', fontSize: "12px", color: 'white', marginBottom: '25px'}}>
                            入賞条件設定
                        </Button>
                    </Box> */}
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flex: 1, flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        競技方式
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={{ marginLeft: '70px', marginRight: '10px', fontSize: '16px', fontWeight: 'bold' }} >
                                コンペタイプ
                            </Typography>
                            <TextField
                                select
                                error={compeDataError?.includes('compeType')}
                                value={compeTypeValue}
                                size="small"
                                name="type"
                                variant="outlined"
                                style={{ width: '190px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    style: { height: "30px" },
                                }}
                                onChange={handleCompeTypeValue}
                            >
                                {compeType.map((option, index) => (
                                    <MenuItem key={index} value={option.value} style={{ fontSize: '14px' }}>
                                        {option.label}
                                    </MenuItem>
                                ))}
                            </TextField>
                        </Box>
                        <Box
                            sx={{
                                borderBottom: '1px dashed #C5C5C5',
                                width: '750px',
                                marginLeft: '90px',
                                display: "flex",
                                flexDirection: "column",
                                marginTop: '16px',
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        icon={<img src={"/webapp/images/compe_ic_un_checked.png"} style={{ width: '21px', height: '21px' }} />}
                                        checkedIcon={<img src={"/webapp/images/compe_ic_checked.png"} style={{ width: '21px', height: '21px' }} />}
                                        checked={handyChecked}
                                        onChange={(e) => setHandyChecked(e.target.checked)
                                        }
                                    />
                                }
                                label={<Typography component="div" sx={{ fontSize: '16px', fontWeight: 'bold' }} >
                                    ハンディ
                                </Typography>}
                            />
                            <Collapse in={handyChecked}>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center' }}>
                                    <Typography component="div" sx={compeStyle} >
                                        ランキング配信
                                    </Typography>
                                    <RadioGroup
                                        value={delivery}
                                        onChange={handleDeliveryChange}
                                        row
                                    >
                                        <FormControlLabel
                                            value={Mail.Abled}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="配信する"
                                        />
                                        <FormControlLabel
                                            value={Mail.Disabled}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="配信しない"
                                            sx={{ marginLeft: '20px' }}
                                        />
                                    </RadioGroup>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'top', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        同点時の優先順位
                                    </Typography>
                                    <DndProvider backend={HTML5Backend}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '0px' }}>
                                            {priorityItems.map((item) => (
                                                <PriorityItem
                                                    key={item.id}
                                                    id={item.id}
                                                    item={item}
                                                    index={item.index}
                                                    itemStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                                                    options={priorityItemOptions}
                                                    moveItem={movePriority}
                                                    onOptionChange={handlePriorityChange}
                                                ></PriorityItem>
                                            ))}
                                        </Box>
                                    </DndProvider>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        使用ハンディキャップ
                                    </Typography>
                                    <RadioGroup
                                        value={hdcpType}
                                        onChange={handleHdcpTypeChange}
                                        row
                                    >
                                        <FormControlLabel
                                            value={HdcpTypes.WHS}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="HDCP Index(WHS)"
                                        />
                                        <FormControlLabel
                                            value={HdcpTypes.Private}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="プライベートハンディキャップ"
                                            sx={{ marginLeft: '20px' }}
                                        />
                                    </RadioGroup>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <MobileDatePicker
                                        disabled={hdcpType !== HdcpTypes.WHS}
                                        value={hdcpDate}
                                        onChange={(newValue) => handleDateChange("hdcpDate", newValue)}
                                        format="YYYY-MM-DD"
                                        slotProps={{
                                            textField: {
                                                size: "small",
                                                InputProps: {
                                                    startAdornment: (
                                                        <InputAdornment position="start">
                                                            {hdcpType !== HdcpTypes.WHS ?
                                                                <img src="/webapp/images/compe_ic_calendar_disabled.png" style={{ height: '15.24px', width: '20.67px', marginLeft: '-5px' }}></img> :
                                                                <img src="/webapp/images/compe_ic_calendar.png" style={{ height: '15.24px', width: '20.67px', marginLeft: '-5px' }}></img>}
                                                        </InputAdornment>
                                                    ),
                                                    style: { width: "150px", height: "30px" },
                                                },
                                            },
                                        }}
                                        sx={{ marginLeft: '190px' }}
                                    >
                                    </MobileDatePicker>
                                    <Typography sx={{ marginLeft: '10px', fontSize: '16px', color: hdcpType === HdcpTypes.WHS ? '#000000' : 'gray' }}>
                                        時点のHDCPを使う
                                    </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <TextField
                                        disabled={hdcpType !== HdcpTypes.WHS}
                                        value={hdcpAllowance}
                                        onChange={handleHdcpAllowanceChange}
                                        type="text"
                                        inputProps={{
                                            inputMode: "numeric",
                                            pattern: "[0-9]*",
                                            maxLength: 3,
                                        }}
                                        style={{ width: '60px', fontSize: '14px', height: "30px", textAlign: "right" }}
                                        InputProps={{
                                            style: { height: "30px" },
                                            sx: { input: { textAlign: 'right' } }
                                        }}
                                        sx={{ marginLeft: '190px' }}
                                        size="small"
                                    />
                                    <Typography sx={{ marginLeft: '10px', fontSize: '16px', color: hdcpType === HdcpTypes.WHS ? '#000000' : 'gray' }}>
                                        % HDCPアローワンス
                                    </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        NET計算方法
                                    </Typography>
                                    <RadioGroup
                                        value={calculation}
                                        onChange={handleCalculationChange}
                                        row
                                    >
                                        <FormControlLabel
                                            value={Calculation.Hdcp}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="HDCPナンバーで割り振り"
                                        />
                                        <FormControlLabel
                                            value={Calculation.Point}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="按分方式"
                                            sx={{ marginLeft: '20px' }}
                                        />
                                    </RadioGroup>
                                </Box>
                            </Collapse>
                        </Box>
                        <Box
                            sx={{
                                borderBottom: '1px dashed #C5C5C5',
                                width: '750px',
                                marginLeft: '90px',
                                display: "flex",
                                flexDirection: "column",
                                marginBottom: '22px'
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        icon={<img src={"/webapp/images/compe_ic_un_checked.png"} style={{ width: '21px', height: '21px' }} />}
                                        checkedIcon={<img src={"/webapp/images/compe_ic_checked.png"} style={{ width: '21px', height: '21px' }} />}
                                        checked={pairChecked}
                                        onChange={(e) => setPairChecked(e.target.checked)
                                        }
                                    />
                                }
                                label={<Typography component="div" sx={{ fontSize: '16px', fontWeight: 'bold' }} >
                                    ペリア
                                </Typography>}
                            />
                            <Collapse in={pairChecked}>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        集計方法
                                    </Typography>
                                    <TextField
                                        select
                                        value={totalling}
                                        size="small"
                                        name="totalling"
                                        variant="outlined"
                                        style={{ width: '160px', fontSize: '14px', height: "30px" }}
                                        InputProps={{
                                            style: { height: "30px" },
                                        }}
                                        onChange={handleTotallingChange}
                                    >
                                        {totallingOptions.map((option) => (
                                            <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                                {option.label}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                    <Button
                                        endIcon={<img src={"/webapp/images/compe_ic_prize.png"} alt="logo" width={"14px"} height={"12.64px"} />}
                                        style={{ width: '120px', height: '30px', backgroundColor: '#384f81', fontSize: "12px", color: 'white', marginLeft: '10px' }}
                                        onClick={showHideHoleDialog}>
                                        隠しホール設定
                                    </Button>
                                    {hideHoleSetSucceed
                                        ? <img src={"/webapp/images/compe_ic_correct.png"} alt="logo" style={{ width: '10px', height: '6.64px', marginLeft: '12px' }}></img>
                                        : <></>}
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        打数制限
                                    </Typography>
                                    <TextField
                                        select
                                        value={atBats}
                                        error={compeDataError?.includes('atBats')}
                                        size="small"
                                        name="totalling"
                                        variant="outlined"
                                        style={{ width: '160px', fontSize: '14px', height: "30px" }}
                                        InputProps={{
                                            style: { height: "30px" },
                                        }}
                                        onChange={handleAtBatsChange}
                                    >
                                        {atBatsOptions.map((option) => (
                                            <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                                {option.label}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                    {showAtBatsN ?
                                        <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginLeft: '23px' }}>
                                            <Typography component="div" sx={{ fontSize: '16px', display: "inline-flex", alignItems: "center" }} >
                                                N=
                                            </Typography>
                                            <TextField
                                                value={atBatsNValue}
                                                onChange={handleAtBatsNValueChange}
                                                style={{ width: '50px', fontSize: '16px', height: "30px", marginLeft: "5px" }}
                                                InputProps={{
                                                    style: { height: "30px" },
                                                }}
                                                size="small">

                                            </TextField>
                                        </Box>
                                        : <></>
                                    }
                                    {showAtBatsX ?
                                        <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginLeft: showAtBatsN ? '15px' : '23px' }}>
                                            <Typography component="div" sx={{ fontSize: '16px', display: "inline-flex", alignItems: "center" }} >
                                                X=
                                            </Typography>
                                            <TextField
                                                value={atBatsXValue}
                                                onChange={handleAtBatsXValueChange}
                                                style={{ width: '50px', fontSize: '16px', height: "30px", marginLeft: "5px" }}
                                                InputProps={{
                                                    style: { height: "30px" },
                                                }}
                                                size="small">
                                            </TextField>
                                        </Box>
                                        : <></>
                                    }
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        ハンデ上限設定
                                    </Typography>
                                    <div style={{ fontSize: '16px' }}>
                                        男性
                                    </div>
                                    <TextField
                                        value={manHandiValue}
                                        onChange={handleManHandiChange}
                                        placeholder="上限なし"
                                        type="text"
                                        inputProps={{
                                            inputMode: "numeric",
                                            pattern: "[0-9]*",
                                        }}
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <div style={{ display: "flex", flexDirection: "column" }}>
                                                        <IconButton
                                                            onClick={manHandleIncrement}
                                                            size="small"
                                                            style={{ marginTop: '15px' }}
                                                        >
                                                            <img src="/webapp/images/compe_ic_arrow_up.png" />
                                                        </IconButton>
                                                        <IconButton
                                                            onClick={manHandleDecrement}
                                                            size="small"
                                                            style={{ marginBottom: '15px' }}
                                                        >
                                                            <img src="/webapp/images/compe_ic_arrow_down.png" />
                                                        </IconButton>
                                                    </div>
                                                </InputAdornment>
                                            ),
                                            style: { width: "116px", height: "30px" },
                                        }}
                                        sx={{
                                            width: '116px',
                                            height: '30px',
                                            '& .MuiInputBase-input::placeholder': {
                                                color: 'black',
                                                opacity: 1,
                                                transition: 'opacity 0.2s',
                                            },
                                            '& .MuiInputBase-input:focus::placeholder': {
                                                opacity: 0,
                                            },
                                        }}
                                    />
                                    <div style={{ fontSize: '16px', marginLeft: '11px' }}>
                                        女性
                                    </div>
                                    <TextField
                                        value={womanHandiValue}
                                        onChange={handleWomanHandiChange}
                                        placeholder="上限なし"
                                        type="text"
                                        inputProps={{
                                            inputMode: "numeric",
                                            pattern: "[0-9]*",
                                        }}
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <div style={{ display: "flex", flexDirection: "column" }}>
                                                        <IconButton
                                                            onClick={womanHandleIncrement}
                                                            size="small"
                                                            style={{ marginTop: '15px' }}
                                                        >
                                                            <img src="/webapp/images/compe_ic_arrow_up.png" />
                                                        </IconButton>
                                                        <IconButton
                                                            onClick={womanHandleDecrement}
                                                            size="small"
                                                            style={{ marginBottom: '15px' }}
                                                        >
                                                            <img src="/webapp/images/compe_ic_arrow_down.png" />
                                                        </IconButton>
                                                    </div>
                                                </InputAdornment>
                                            ),
                                            style: { width: "116px", height: "30px" },
                                        }}
                                        sx={{
                                            width: '116px',
                                            height: '30px',
                                            '& .MuiInputBase-input::placeholder': {
                                                color: 'black',
                                                opacity: 1,
                                                transition: 'opacity 0.2s',
                                            },
                                            '& .MuiInputBase-input:focus::placeholder': {
                                                opacity: 0,
                                            },
                                        }}
                                    />
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'center', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        ランキング配信
                                    </Typography>
                                    <RadioGroup
                                        value={rankingMail}
                                        onChange={handleRankingMailChange}
                                        row
                                    >
                                        <FormControlLabel
                                            value={Mail.Abled}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="配信する"
                                        />
                                        <FormControlLabel
                                            value={Mail.Disabled}
                                            control={<Radio sx={{
                                                color: '#9e9e9e',
                                                '&.Mui-checked': {
                                                    color: '#FF8000',
                                                },
                                            }} />}
                                            label="配信しない"
                                            sx={{ marginLeft: '20px' }}
                                        />
                                    </RadioGroup>
                                </Box>
                                <Box sx={{ display: 'flex', direction: 'row', alignItems: 'top', marginTop: "12px" }}>
                                    <Typography component="div" sx={compeStyle} >
                                        同点時の優先順位
                                    </Typography>
                                    <DndProvider backend={HTML5Backend}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '0px' }}>
                                            {periaPriorityItems.map((item, index) => (
                                                <PriorityItem
                                                    key={item.id}
                                                    id={item.id}
                                                    item={item}
                                                    index={item.index}
                                                    itemStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                                                    options={priorityItemOptions}
                                                    moveItem={movePeriaPriority}
                                                    onOptionChange={handlePeriaPriorityChange}
                                                ></PriorityItem>
                                            ))}
                                        </Box>
                                    </DndProvider>
                                </Box>
                            </Collapse>
                        </Box>
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flex: 1, flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        その他設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                マーカー設定
                            </Typography>
                            <RadioGroup
                                value={markerSetting}
                                onChange={handleMarkerSettingChange}
                                row
                            >
                                <FormControlLabel
                                    value={MarkerSetting.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="しない"

                                />
                                <FormControlLabel
                                    value={MarkerSetting.Free}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="自由設定"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginBottom: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                リーダーボード切替
                            </Typography>
                            <RadioGroup
                                value={leaderBoard}
                                onChange={handleLeaderBoardChange}
                                row
                                sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                            >
                                <FormControlLabel
                                    value={LeadBoardChange.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="する"

                                />
                                <TextField
                                    select
                                    disabled={leaderBoard !== LeadBoardChange.Abled}
                                    value={leaderBoardType}
                                    size="small"
                                    name="totalling"
                                    variant="outlined"
                                    style={{
                                        height: "30px",
                                        width: '200px',
                                    }}
                                    InputProps={{
                                        style: {
                                            width: '200px',
                                            height: "30px",
                                            fontSize: '14px',
                                        },
                                    }}
                                    onChange={handleLeaderBoardTypeChange}
                                >
                                    {boardOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </TextField>
                                <FormControlLabel
                                    value={LeadBoardChange.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="しない"
                                    sx={{ marginLeft: '20px' }}
                                />
                                <TextField
                                    select
                                    disabled={leaderBoard !== LeadBoardChange.Disabled}
                                    value={defaultBoardType}
                                    size="small"
                                    name="totalling"
                                    variant="outlined"
                                    style={{ width: '200px', height: "30px" }}
                                    InputProps={{
                                        style: {
                                            height: "30px",
                                            width: '200px',
                                            fontSize: '14px',
                                        },
                                    }}
                                    onChange={handleDefaultBoardTypeChange}
                                >
                                    {boardOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </TextField>
                            </RadioGroup>
                        </Box>
                    </Box>
                </Box>
                <Box sx={{
                    display: "flex",
                    marginLeft: "89px",
                    marginTop: "14px",
                    marginBottom: "40px",
                    marginRight: "90px",
                    justifyContent: "space-between"
                }}>
                    <Box sx={{ flex: 1, display: "flex", justifyContent: "center" }}>
                        <Button variant="contained"
                            sx={{
                                minWidth: 0,
                                width: '90px',
                                height: '30px'
                            }}
                            onClick={checkCompeData}>
                            保存
                        </Button>
                    </Box>
                </Box>
                <HideHoleDialog
                    openHideHoleDialog={openHideHoleDialog}
                    closeHideHoleDialog={closeHideHoleDialog}
                    golfName={golfName}
                    hideHole={hideHole}
                    cancelHideHoleSetting={cancelHideHoleSetting}
                    hideHoleTitle={hideHoleTitle}
                    competitionName={competitionName}
                    randomSelecteHole={randomSelecteHole}
                    checkHideHoleSetting={checkHideHoleSetting}
                ></HideHoleDialog>
                <HideHolePageErrorDialog
                    openErrorDialog={openErrorDialog}
                    closeErrorDialog={closeErrorDialog}
                ></HideHolePageErrorDialog>
                <ApiDialog
                    openApiDialog={openApiDialog}
                    handleCloseErrorDialog={handleCloseErrorDialog}
                    succeed={createCompeSucceed}
                    apiType={"createCompe"}></ApiDialog>
            </Box>
        </LocalizationProvider>
    );
};

export default Home;