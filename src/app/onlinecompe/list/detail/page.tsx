"use client";
import React, { useState, useEffect, Suspense } from "react";
import {
  Box,
  ToggleButtonGroup,
  ToggleButton,
  Typography,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useSearchParams, useRouter } from "next/navigation";
import Pairing from "@/components/onlinecompe/pairing";
import Leaderboard from "@/components/onlinecompe/leaderboard";
import Holebyhole from "@/components/onlinecompe/holebyhole";

// Loading component to display while suspense is resolving
const DetailLoading = () => {
  return <div>Loading...</div>;
};

// Main component wrapped with Suspense
const DetailContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  // ステータス
  const [radio, setRadio] = useState<string | null>(
    typeof localStorage !== "undefined"
      ? localStorage.getItem("detailType")
      : null
  );
  // コンペ名
  const [compeName, setCompeName] = useState<string | null>(null);
  // コンペNo
  const [compeNo, setCompeNo] = useState<number | null>(null);

  useEffect(() => {
    // コンペ名
    const compeName = searchParams.get("compeName");
    setCompeName(compeName ? compeName : null);
    // コンペNo
    const compeNo = searchParams.get("compeNo");
    setCompeNo(compeNo ? Number(compeNo) : null);
  }, [searchParams]);

  // ボタン切り替え
  const handleClick = (
    _: React.MouseEvent<HTMLElement, MouseEvent>,
    newRadio: string | null
  ) => {
    if (newRadio !== null) {
      setRadio(newRadio);
      if (typeof localStorage !== "undefined") {
        localStorage.setItem("detailType", newRadio);
      }
    }
  };

  const renderContent = () => {
    return (
      <Typography component="div" className="my-2">
        {radio === "pairing" && typeof localStorage !== "undefined"
          ? `${compeName}(${localStorage.getItem("playerDateFrom")} ～ 
              ${localStorage.getItem("playerDateTo")})`
          : compeName}
      </Typography>
    );
  };

  return (
    <>
      {compeNo && compeName ? (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flex: 1,
              backgroundColor: "#f9f9f9",
              margin: "-24px",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                height: "65px",
                backgroundColor: "#f3f3f3",
                flexDirection: "row",
                borderRadius: "5px",
                marginLeft: "18px",
                marginRight: "18px",
                marginTop: "25px",
                paddingLeft: "32px",
              }}
            >
              <img
                src="/webapp/images/compe_ic_menu.png"
                style={{ width: "25px", height: "17px" }}
                alt="Competition menu icon"
              />
              <Typography
                variant="h2"
                sx={{ marginLeft: "14px" }}
                onClick={() => router.back()}
              >
                <a className="text-blue-500 hover:cursor-pointer">コンペ一覧</a>
              </Typography>
              <Typography variant="h2" className="!mx-1">
                /
              </Typography>
              <Typography variant="h2">{compeName}</Typography>
            </Box>
            <Box
              sx={{
                px: 4,
                py: 2,
                flexGrow: 1,
                borderRadius: 2,
              }}
            >
              {renderContent()}
              <ToggleButtonGroup
                color="info"
                value={radio}
                exclusive
                onChange={handleClick}
                className="gap-4 my-2"
                size="small"
                sx={{
                  "& .MuiToggleButton-root": {
                    color: "#304A89 !important",
                    border: "1px solid #304A89 !important",
                    borderRadius: "4px !important",
                  },
                  "& .Mui-selected": {
                    color: "#fff !important",
                    backgroundColor: "#304A89 !important",
                  },
                  "& .MuiToggleButton-root:hover": {
                    color: "#fff !important",
                    backgroundColor: "#304A89 !important",
                  },
                }}
              >
                <ToggleButton value="pairing">参加者情報</ToggleButton>
                <ToggleButton value="leaderboard">リーダボード</ToggleButton>
                <ToggleButton value="holebyhole">ホールバイホール</ToggleButton>
              </ToggleButtonGroup>
              {radio === "pairing" && (
                <Pairing compeNo={compeNo} compeName={compeName} />
              )}
              {radio === "leaderboard" && (
                <Leaderboard compeNo={compeNo} compeName={compeName} />
              )}
              {radio === "holebyhole" && (
                <Holebyhole compeNo={compeNo} compeName={compeName} />
              )}
            </Box>
          </Box>
        </LocalizationProvider>
      ) : (
        <div>Loading...</div>
      )}
    </>
  );
};

// Wrapper component with Suspense
const Detail = () => {
  return (
    <Suspense fallback={<DetailLoading />}>
      <DetailContent />
    </Suspense>
  );
};

export default Detail;
