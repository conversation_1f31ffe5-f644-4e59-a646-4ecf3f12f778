"use client";
import React, { useEffect, useState, Suspense } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import { JoinedPlayer } from "@/models/compe/resp/online-compe-player";
import { ScoreCourse } from "@/models/compe/resp/ranking-resp";
import ScoreFormat from "@/components/onlinecompe/score-format";

// Loading component to display while suspense is resolving
const ContentLoading = () => {
  return <div>Loading...</div>;
};

interface CourseData {
  course_name: string;
  hole: string;
  score_gross: number;
  score_net: number;
  hdcp_index: string;
  course_hdcp: string;
  start_hole: string;
  total_holes: number;
  hole_scores: {
    hole_number: string;
    course_index: string;
    hole_index: string;
    score: string;
    stroke: string;
    used_hdcp: string;
    start: string;
  }[];
}

interface CombinedPlayerData {
  player_no: string;
  player_name: string;
  pos: string;
  net: string;
  is_tied: number;
  score: string;
  today: string;
  courseData: {
    [key: string]: CourseData;
  };
}

interface CombinedPlayerDataLB {
  player_no: string;
  player_name: string;
  pos: string;
  net: string;
  is_tied: number;
  score: string;
  hole: string;
  today: string;
  hdcp: string;
}

const StyledTableCell = styled(TableCell)(() => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#EBEBEB",
    border: "1px solid #000",
    textAlign: "center",
    padding: 0,
  },
  [`&.${tableCellClasses.body}`]: {
    border: "1px solid #000",
    textAlign: "center",
    padding: 0,
  },
}));

const renderField = (field: string, stroke: string) => {
  if (field === "-") return null;
  else if (parseInt(field) <= -3) return <span style={{ color: "red" }}>★</span>;
  else if (field === "-2") return <span style={{ color: "red" }}>◎</span>;
  else if (field === "-1") return <span style={{ color: "red" }}>○</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>-</span>;
  else if (field === "1") return <span style={{ color: "blue" }}>△</span>;
  else if (field === "2") return <span style={{ color: "blue" }}>□</span>;
  else if (field === "3") return <span style={{ color: "blue" }}>■</span>;
  else if (parseInt(field) > 3)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

const renderField2 = (field: string, stroke: string) => {
  if (field === "-") return null;
  else if (parseInt(field) < 0)
    return <span style={{ color: "red" }}>{field}</span>;
  else if (field === "0" && stroke !== "0")
    return <span style={{ color: "black" }}>{field}</span>;
  else if (parseInt(field) > 0)
    return <span style={{ color: "blue" }}>{field}</span>;
  else return null;
};

const PrintContent = () => {
  const style = {
    backgroundColor: "#fff",
    width: "277mm",
    // height: "210mm",
    margin: "0mm auto",
    padding: "2px",
    overFlow: 'hidden',
  };
  // オフィス名
  const [officeName, setOfficeName] = useState<string | null>(null);
  // コンペ名
  const [compeName, setCompeName] = useState<string | null>(null);
  // 開催日
  const [playerDateFrom, setPlayerDateFrom] = useState<string | null>(null);
  // タイプ
  const [printType, setPrintType] = useState<string | null>(null);
  // 参加者
  const [pairingList, setPairingList] = useState<JoinedPlayer[]>([]);
  // リーダボード
  const [playerMapLB, setPlayerMapLB] = useState<CombinedPlayerDataLB[]>([]);
  // HBH
  const [playerMap, setPlayerMap] = useState<CombinedPlayerData[]>();
  const [courses, setCourses] = useState<ScoreCourse[]>([]);
  const [maxHoles, setMaxHoles] = useState<number>(0);
  const [scoreValue, setScoreValue] = useState<number | null>(null);
  const [rankinType, setRankinType] = useState<string | null>(null);

  useEffect(() => {
    const officeName =
      typeof localStorage !== "undefined"
        ? localStorage.getItem("office_name")
        : null;
    // オフィス名
    setOfficeName(officeName);

    const printData =
      typeof localStorage !== "undefined"
        ? localStorage.getItem("printData")
        : null;
    const data = printData ? JSON.parse(printData as string) : {};
    // コンペ名
    setCompeName(data?.compeName);
    // 開催日
    setPlayerDateFrom(data?.playerDateFrom);
    // タイプ
    setPrintType(data?.printType);
    // 参加者
    setPairingList(data?.pairingList);
    // リーダボード
    setPlayerMapLB(data?.playerMapLB);
    // HBH
    setPlayerMap(data?.playerMap);
    setCourses(data?.courses);
    setMaxHoles(data?.maxHoles);
    setScoreValue(data?.scoreRenderType);
    setRankinType(data?.rankinType);

    setTimeout(() => {
      window.print();
    }, 500);
  }, []);

  // ヘッダー セルのインターフェース
  interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: "center" | "left";
    format?: (value: number) => string;
  }

  const renderTableHead = () => {
    if (printType === "Holebyhole") {
      // ヘッダー 定義
      const columns: readonly Column[] = [
        { id: "pos", label: "POS", align: "center", minWidth: 40 },
        { id: "name", label: "NAME", align: "center", minWidth: 70 },
        { id: "score", label: rankinType === "gross" ? "GROSS" : "NET", align: "center", minWidth: 50 },
      ];
      return (
        <TableRow>
          {columns.map((column) => (
            <StyledTableCell
              key={column.id}
              align={column.align}
              sx={{ minWidth: column.minWidth }}
            >
              {column.label}
            </StyledTableCell>
          ))}
          {courses.map((course) => (
            <React.Fragment key={course.course_index}>
              {course.holes.map((hole) => (
                <StyledTableCell
                  key={`hole-${hole.hole_index}`}
                  sx={{ minWidth: 25 }}
                >
                  <p style={{ borderBottom: "1px solid #000" }}>
                    {Number(hole.hole_index) + Number(course.start_hole)}
                  </p>
                  <p>{hole.used_par}</p>
                </StyledTableCell>
              ))}
              <StyledTableCell sx={{ minWidth: 50 }}>
                <p>{course.course_name}</p>
                <p>
                  {course.holes.reduce(
                    (sum, hole) => sum + Number(hole.used_par),
                    0
                  )}
                </p>
              </StyledTableCell>
            </React.Fragment>
          ))}
          <StyledTableCell sx={{ minWidth: 50 }}>
            <p>TODAY</p>
            <p>
              {courses
                .flatMap((course) => course.holes)
                .reduce((sum, hole) => sum + parseInt(hole.used_par), 0)}
            </p>
          </StyledTableCell>
        </TableRow>
      );
    } else if (printType === "Leaderboard") {
      // ヘッダー 定義
      const columns: readonly Column[] = [
        { id: "pos", label: "POS", minWidth: 50 },
        { id: "name", label: "NAME", minWidth: 150 },
        { id: "score", label: rankinType === "gross" ? "GROSS" : "NET", minWidth: 150 },
        { id: "hole", label: "HOLE", minWidth: 150 },
        { id: "today", label: "TODAY", minWidth: 150 },
        { id: "hdcp", label: "HDCP", minWidth: 150 },
      ];
      return (
        <TableRow>
          {columns.map((column) => (
            <StyledTableCell key={column.id} sx={{ minWidth: column.minWidth }}>
              {column.label}
            </StyledTableCell>
          ))}
        </TableRow>
      );
    } else if (printType === "Pairing") {
      // ヘッダー 定義
      const columns: readonly Column[] = [
        { id: "player_name", label: "Name", minWidth: 150 },
        { id: "gender", label: "性別", minWidth: 100 },
        { id: "player_no", label: "Glid\u00a0No", minWidth: 150 },
        { id: "hdcp_index", label: "HDCP\u00a0Index", minWidth: 150 },
        { id: "hdcp", label: "HDCP", minWidth: 150 },
        { id: "tee_id", label: "使用ティー", minWidth: 160 },
      ];
      return (
        <TableRow>
          {columns.map((column) => (
            <StyledTableCell key={column.id} sx={{ minWidth: column.minWidth }}>
              {column.label}
            </StyledTableCell>
          ))}
        </TableRow>
      );
    }
  };

  const renderTableBody = () => {
    if (printType === "Holebyhole") {
      return (
        <>
          {(playerMap || []).map((player, index) => (
            <TableRow key={index} sx={{ height: "35px" }}>
              <StyledTableCell>
                {player.pos
                  ? `${player.pos}`
                  : "-"}
              </StyledTableCell>
              <StyledTableCell
                sx={{
                  textAlign: "left !important",
                  padding: "0 6px !important",
                }}
              >
                {player.player_name}
              </StyledTableCell>
              <StyledTableCell>{player.score}</StyledTableCell>
              {courses.map((course) => {
                const courseData = player.courseData[course.course_name];
                return (
                  <React.Fragment key={course.course_index}>
                    {Array.from({ length: maxHoles }, (_, i) => {
                      const holeScore = courseData?.hole_scores?.find(
                        (hs) => parseInt(hs.hole_index) === i
                      );
                      return (
                        <StyledTableCell key={i} sx={{ position: "relative" }}>
                          <p
                            style={{
                              fontSize: 9,
                              position: "absolute",
                              bottom: 0,
                              left: 0,
                              height: "10px",
                              overflow: "hidden",
                            }}
                          >
                            {holeScore?.start}
                          </p>
                          <p>
                            {holeScore
                              ? scoreValue === 1
                                ? renderField(holeScore.score, holeScore.stroke)
                                : renderField2(
                                  holeScore.score,
                                  holeScore.stroke
                                )
                              : ""}
                          </p>
                        </StyledTableCell>
                      );
                    })}
                    <StyledTableCell>
                      {courseData?.hole_scores.reduce(
                        (sum, hole) => sum + Number(hole.score),
                        0
                      )}
                    </StyledTableCell>
                  </React.Fragment>
                );
              })}
              <StyledTableCell>{player.today}</StyledTableCell>
            </TableRow>
          ))}
        </>
      );
    } else if (printType === "Leaderboard") {
      return (
        <>
          {(playerMapLB || []).map((player, index) => (
            <TableRow key={index}>
              <StyledTableCell>
                {player.pos
                  ? `${player.pos}`
                  : "-"}
              </StyledTableCell>
              <StyledTableCell>{player.player_name}</StyledTableCell>
              <StyledTableCell>{rankinType === "gross" ? player.score : player.net}</StyledTableCell>
              <StyledTableCell>{player.hole}</StyledTableCell>
              <StyledTableCell>{player.today}</StyledTableCell>
              <StyledTableCell>{player.hdcp ? player.hdcp : "-"}</StyledTableCell>
            </TableRow>
          ))}
        </>
      );
    } else if (printType === "Pairing") {
      return (
        <>
          {(pairingList || []).map((row, index) => (
            <TableRow key={index}>
              <StyledTableCell>{row.player_name}</StyledTableCell>
              <StyledTableCell>
                {row.gender === 1 ? "男性" : row.gender === 2 ? "女性" : ""}
              </StyledTableCell>
              <StyledTableCell>{row.glid_no}</StyledTableCell>
              <StyledTableCell>{row.hdcp_index}</StyledTableCell>
              <StyledTableCell>{row.hdcp}</StyledTableCell>
              <StyledTableCell>{row.tee_id}</StyledTableCell>
            </TableRow>
          ))}
        </>
      );
    }
  };

  return (
    <Box sx={{ background: "#fff", height: "100vh" }} className="no-print-box">
      <Box sx={style} className="print-box">
        <div className="flex justify-end items-center pt-4 no-print">
          {compeName && (
            <Button
              variant="contained"
              sx={{
                backgroundColor: "#EEE",
                color: "#000",
                fontWeight: "bold",
                "&:hover": {
                  backgroundColor: "#EEE",
                },
              }}
              onClick={() => {
                window.close();
              }}
            >
              画面を閉じる
            </Button>
          )}
        </div>
        <div className="flex justify-between items-center my-4">
          <Typography variant="h1" sx={{ fontWeight: "bold" }}>
            {compeName}
          </Typography>
        </div>
        <div className="flex justify-between items-center my-4">
          <Typography variant="h2" sx={{ fontWeight: "bold" }}>
            {playerDateFrom ? `開催日：${playerDateFrom}` : ""}
          </Typography>
          <Typography variant="h2" sx={{ fontWeight: "bold", paddingRight: printType === "Holebyhole" ? '7mm' : '20mm' }}>
            {officeName}
          </Typography>
        </div>
        <div style={{ width: printType === "Holebyhole" ? '98%' : '94%' }}>
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>{renderTableHead()}</TableHead>
              <TableBody>{renderTableBody()}</TableBody>
            </Table>
          </TableContainer>
          {scoreValue === 1 && <ScoreFormat />}
        </div>
      </Box>
    </Box>
  );
};

// Wrapper component with Suspense
const print = () => {
  return (
    <Suspense fallback={<ContentLoading />}>
      <PrintContent />
    </Suspense>
  );
};

export default print;
