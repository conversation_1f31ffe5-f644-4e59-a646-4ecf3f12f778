"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TableSortLabel,
  Modal,
  IconButton,
  Divider,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import EditNoteIcon from "@mui/icons-material/EditNote";
import CheckIcon from "@mui/icons-material/Check";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import "dayjs/locale/ja";
import { useRouter } from "next/navigation";
import CompeFilterForm from "@/components/onlinecompe/online-compe-office-form";
import PaginationComponent from "@/components/onlinecompe/pagination-component";
import { listOfficeCompe, createShared<PERSON><PERSON> } from "@/api/online-compe-api";
import { MergedCompe } from "@/models/compe/resp/online-compe-office";
dayjs.locale("ja");

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#eee",
    border: "1px solid #ddd",
    textAlign: "center",
    padding: "6px 10px",
  },
  [`&.${tableCellClasses.body}`]: {
    border: "1px solid #ddd",
    textAlign: "center",
    padding: "6px 10px",
  },
}));

const Home = () => {
  type Order = "asc" | "desc";
  const router = useRouter();
  // 排序类型
  const [order, setOrder] = useState<Order>("asc");
  // 排序字段
  const [orderBy, setOrderBy] = useState<keyof MergedCompe>("compe_no");
  // ページリスト
  const counts = [10, 50, 100];
  // 該当ページ数
  const [page, setPage] = useState(1);
  // 一つのページのデータ数
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // コンペ一覧リスト
  const [officeCompeList, setOfficeCompeList] = useState<MergedCompe[]>([]);
  // 操作行
  const [selectRow, setSelectRow] = useState<MergedCompe>();
  // 検索パラメータ
  const [filters, setFilters] = useState({
    play_date: dayjs().format("YYYYMMDD"),
    compe_type: 2,
    compe_kind: "all",
    free_word: "",
    offset: 0,
    limit: 100,
  });

  // コンペ一覧情報
  const getOfficeCompeList = async () => {
    try {
      const res = await listOfficeCompe({
        ...filters,
        play_date:
          filters.play_date === ""
            ? null
            : dayjs(filters.play_date).format("YYYYMMDD"),
        compe_type: filters.compe_type === 2 ? null : filters.compe_type,
        compe_kind: filters.compe_kind === "all" ? null : filters.compe_kind,
        free_word: filters.free_word === "" ? null : filters.free_word,
      });

      (res.data.compes || []).forEach((i) => {
        if (i?.duration?.from) {
          i.play_date = dayjs(i.duration.from).format("YYYY-MM-DD");
        }
      });

      setOfficeCompeList(res.data.compes || []);
    } catch (error) { }
  };

  useEffect(() => {
    localStorage.removeItem("detailType");
    localStorage.removeItem("playerDateFrom");
    localStorage.removeItem("playerDateTo");
    getOfficeCompeList();
  }, []);

  // ヘッダー ソート情報
  const handleRequestSort = (_: unknown, property: keyof MergedCompe) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  // ヘッダー セルのインターフェース
  interface HeadCell {
    id: keyof MergedCompe;
    label: string;
    sortable: boolean;
    disablePadding: boolean;
  }
  // ヘッダー 定義
  const headCells: readonly HeadCell[] = [
    {
      id: "play_date",
      disablePadding: true,
      label: "開催日",
      sortable: true,
    },
    {
      id: "compe_no",
      disablePadding: false,
      label: "No",
      sortable: true,
    },
    {
      id: "compe_type",
      disablePadding: false,
      label: "コンペタイプ",
      sortable: true,
    },
    {
      id: "compe_name",
      disablePadding: false,
      label: "コンペ名",
      sortable: true,
    },
    {
      id: "joined_players_count",
      disablePadding: false,
      label: "参加人数",
      sortable: false,
    },
    {
      id: "aggregation_types",
      disablePadding: false,
      label: "競技方法",
      sortable: false,
    },
    {
      id: "hidden_hole_setted",
      disablePadding: false,
      label: "隠しホール",
      sortable: false,
    },
  ];
  // ヘッダー componentのインターフェース
  interface EnhancedTableProps {
    onRequestSort: (
      event: React.MouseEvent<unknown>,
      property: keyof MergedCompe
    ) => void;
    order: Order;
    orderBy: string;
  }
  // ヘッダー component
  function EnhancedTableHead(props: EnhancedTableProps) {
    const { order, orderBy, onRequestSort } = props;

    const createSortHandler = (property: keyof MergedCompe) => (
      event: React.MouseEvent<unknown>
    ) => {
      onRequestSort(event, property);
    };

    const MyIconComponent1 = () => (
      <span style={{ width: 24, height: 18, paddingLeft: 4 }}>
        <img src="/webapp/images/sort.png" />
      </span>
    );
    const MyIconComponent2 = () => <></>;

    return (
      <TableHead>
        <TableRow>
          <StyledTableCell key="edit" sx={{ width: 50 }}></StyledTableCell>
          {headCells.map((headCell) => (
            <React.Fragment key={headCell.id}>
              {headCell.sortable ? (
                <StyledTableCell
                  key={headCell.id}
                  // padding={headCell.disablePadding ? "none" : "normal"}
                  sortDirection={orderBy === headCell.id ? order : false}
                >
                  <TableSortLabel
                    active={orderBy === headCell.id}
                    direction={orderBy === headCell.id ? order : "asc"}
                    onClick={createSortHandler(headCell.id)}
                    IconComponent={
                      orderBy === headCell.id
                        ? MyIconComponent2
                        : MyIconComponent1
                    }
                  >
                    {headCell.label}
                    {orderBy === headCell.id ? (
                      order === "desc" ? (
                        <span style={{ width: 24, paddingLeft: 4 }}>
                          <img src="/webapp/images/down.png" />
                        </span>
                      ) : (
                        <span style={{ width: 24, paddingLeft: 4 }}>
                          <img src="/webapp/images/up.png" />
                        </span>
                      )
                    ) : null}
                  </TableSortLabel>
                </StyledTableCell>
              ) : (
                <StyledTableCell key={headCell.id}>
                  {headCell.label}
                </StyledTableCell>
              )}
            </React.Fragment>
          ))}
          <StyledTableCell key="subao">速報</StyledTableCell>
          <StyledTableCell key="setkey">共有キー</StyledTableCell>
          <StyledTableCell key="seturl"></StyledTableCell>
        </TableRow>
      </TableHead>
    );
  }

  // 絞り込みボタン
  const handleApplyFilters = async () => {
    getOfficeCompeList();
    // ページ
    setPage(1);
  };

  // クリアボタン
  const handleResetFilters = async () => {
    setFilters({
      ...filters,
      play_date: "",
      compe_type: 2,
      compe_kind: "all",
      free_word: "",
    });
  };

  // 新規コンペ登録ボタン
  const handleCreateOnlineCompe = () => {
    router.push("/onlinecompe/registration");
  };

  // 編集ボタン
  const handleUpdateOnlineCompe = (no: number, isFront: boolean, playDate: string) => {
    if (isFront) {
      const url = new URL(process.env.BASE_URL || "");
      const prefix = `${url.protocol}//${url.hostname}`;
      // https://dev.marshal-i.com/ops/competition/20250521/18
      router.push(`${prefix}/ops/competition/${playDate.replace(/-/g, "")}/${no}`);
    } else {
      router.push(`/onlinecompe/edit?compe_no=${no}`);
    }
  };

  // コンペ名&リーダーボードボタン
  const handleNameClick = (type: string, row: MergedCompe) => {
    localStorage.setItem("detailType", type);
    if (row?.duration?.from) {
      localStorage.setItem(
        "playerDateFrom",
        dayjs(row.duration.from).format("YYYY-MM-DD")
      );
    }
    if (row?.duration?.to) {
      localStorage.setItem(
        "playerDateTo",
        dayjs(row.duration.to).format("YYYY-MM-DD")
      );
    }
    if (row.is_front_system) {
      const url = new URL(process.env.BASE_URL || "");
      const prefix = `${url.protocol}//${url.hostname}`;
      //https://dev.marshal-i.com/ops/competitionhistory/detail/1000219/18/20250521?type=leaderboard
      const officeId = localStorage.getItem('office_id') ?? '';
      router.push(`${prefix}/ops/competitionhistory/detail/${officeId}/${row.compe_no}/${row.play_date?.replace(/-/g, "")}?type=leaderboard`);
    } else {
      router.push(
        `/onlinecompe/list/detail?compeNo=${row.compe_no}&compeName=${row.compe_name}`
      );
    }
  };

  // 共有キーボタン
  const handleKeyCreate = async (row: MergedCompe) => {
    try {
      const value = await createSharedKey(row.compe_no);
      const updatedItems = officeCompeList.map((item) =>
        item.compe_no === row.compe_no ? { ...item, shared_key: value } : item
      );
      setOfficeCompeList(updatedItems);
    } catch (error) { }
  };

  // 共有URL発行ボタン
  const handleURL = (row: MergedCompe) => {
    window.open(
      `/webapp/project?compeNo=${row.compe_no}&compeName=${row.compe_name}`,
      "_blank"
    );
  };

  // ページを変更する
  const handleSelectedPage = (_: unknown, value: number) => {
    setPage(value);
  };

  // ページサイズを変更する
  const handleSelectChange = async (event: any) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  // 比較する関数
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  }
  function stableSort<T>(
    array: readonly T[],
    comparator: (a: T, b: T) => number
  ) {
    const stabilizedThis = (array || []).map(
      (el, index) => [el, index] as [T, number]
    );
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }
  function getComparator<Key extends keyof MergedCompe>(
    order: Order,
    orderBy: Key
  ): (a: { [key in Key]: unknown }, b: { [key in Key]: unknown }) => number {
    return order === "desc"
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }
  // 可見行を取得する
  const visibleRows = React.useMemo(
    () =>
      stableSort(officeCompeList, getComparator(order, orderBy)).slice(
        (page - 1) * rowsPerPage,
        (page - 1) * rowsPerPage + rowsPerPage
      ),
    [order, orderBy, page, rowsPerPage, officeCompeList]
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          flex: 1,
          backgroundColor: "#f9f9f9",
          margin: "-24px",
        }}
      >
        <Box
          sx={{
            display: "flex",
            height: "65px",
            alignItems: "center",
            backgroundColor: "#f3f3f3",
            flexDirection: "row",
            borderRadius: "5px",
            marginLeft: "18px",
            marginRight: "18px",
            marginTop: "25px",
            paddingLeft: "32px",
          }}
        >
          <img
            src="/webapp/images/compe_ic_menu.png"
            style={{ width: "25px", height: "17px" }}
          />
          <Typography
            variant="h2"
            sx={{ fontWeight: "normal", marginLeft: "14px" }}
          >
            コンペ一覧
          </Typography>
        </Box>
        <Box borderRadius={2} paddingX={4} paddingY={2}>
          <div className="w-full">
            <CompeFilterForm filters={filters} setFilters={setFilters} />
          </div>
          <div className="w-full flex justify-end gap-4 mt-4">
            <Button variant="outlined" onClick={handleResetFilters}>
              クリア
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleApplyFilters}
            >
              絞り込み
            </Button>
          </div>
        </Box>
        <Divider />
        <Box
          borderRadius={2}
          display="flex"
          flexDirection="column"
          flexGrow={1}
          paddingX={4}
          paddingY={2}
        >
          <div className="flex items-center">
            <Typography className="font-bold title-la">競技一覧</Typography>
            <small>(100件)</small>
          </div>
          <div className="flex justify-between items-end mb-2">
            <div className="flex justify-between items-center">
              <span className="flex justify-between items-center mr-10 text-sm">
                <img
                  src="/webapp/images/compe_ic_f.png"
                  alt="F"
                  style={{ width: "24px", height: "24px" }}
                />
                ···フロントシステムで登録したコンペ
              </span>
              <span style={{ color: "#FE3535", fontSize: 14 }}>
                ※開催日の指定がない場合、直近のデータのみ表示いたします。
              </span>
            </div>
            <Button
              variant="contained"
              color="primary"
              onClick={handleCreateOnlineCompe}
            >
              新規コンペ登録
            </Button>
          </div>
          <Box flex={1} overflow="auto">
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 750 }} size="small">
                <EnhancedTableHead
                  order={order}
                  orderBy={orderBy}
                  onRequestSort={handleRequestSort}
                />
                <TableBody>
                  {visibleRows.map((row, index) => {
                    return (
                      <TableRow hover role="checkbox" tabIndex={-1} key={index}>
                        <StyledTableCell>
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleUpdateOnlineCompe(row.compe_no as number, row.is_front_system, row.play_date as string)
                            }
                          >
                            <EditNoteIcon />
                          </IconButton>
                        </StyledTableCell>
                        <StyledTableCell>{row.play_date}</StyledTableCell>
                        <StyledTableCell>{row.compe_no}</StyledTableCell>
                        <StyledTableCell>
                          {row.compe_type === 0 ? "チーム戦" : "個人戦"}
                        </StyledTableCell>
                        <StyledTableCell>
                          <div className=" justify-between items-center flex">
                            <a
                              className="text-blue-500 hover:text-blue-400 hover:cursor-pointer"
                              onClick={() => handleNameClick("pairing", row)}
                            >
                              {row.compe_name}
                            </a>
                            {row.is_front_system && (
                              <img
                                src="/webapp/images/compe_ic_f.png"
                                alt="F"
                                style={{ width: "24px", height: "24px" }}
                              />
                            )}
                          </div>
                        </StyledTableCell>
                        <StyledTableCell>
                          {row.joined_players_count + "人"}
                        </StyledTableCell>
                        <StyledTableCell>
                          {row.aggregation_types[0]}
                          <br />
                          {row.aggregation_types.length > 1
                            ? row.aggregation_types[1]
                            : null}
                        </StyledTableCell>
                        <StyledTableCell>
                          {row.hidden_hole_setted ? (
                            <CheckIcon
                              sx={{ color: "#00B10C", fontSize: 26 }}
                            />
                          ) : (
                            "-"
                          )}
                        </StyledTableCell>
                        <StyledTableCell>
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={() => handleNameClick("leaderboard", row)}
                          >
                            リーダーボード
                          </Button>
                        </StyledTableCell>
                        <StyledTableCell>
                          {row.shared_key ? (
                            row.shared_key
                          ) : (
                            <Button
                              variant="contained"
                              color="primary"
                              size="small"
                              onClick={() => {
                                handleKeyCreate(row);
                              }}
                            >
                              キー生成
                            </Button>
                          )}
                        </StyledTableCell>
                        <StyledTableCell>
                          <Button
                            variant="contained"
                            color="info"
                            size="small"
                            onClick={() => handleURL(row)}
                          >
                            共有URL発行
                          </Button>
                        </StyledTableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
          <PaginationComponent
            count={
              officeCompeList.length > 0 && rowsPerPage > 0
                ? Math.ceil(officeCompeList.length / rowsPerPage)
                : 1
            }
            page={page}
            onChange={handleSelectedPage}
            selectedCount={rowsPerPage}
            onCountChange={handleSelectChange}
            counts={counts}
          />
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default Home;
