"use client";
import React, { useEffect, useRef, useState } from "react";
import {
    Box,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Hidden
} from "@mui/material";
import Breadcrumb from "@/components/breadcrumb";
import { teeSheet, getOnlineCompe, listOfficeCompe, updateJoinedPlayerHdcp, joinOnlineCompe } from "@/api/online-compe-api";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { JoinedCompe, TeeCourseData, TeePlayerData, TeeCartData } from "@/models/tee/resp/tee-sheet";
import PlayerCompeDialog, { AvailableCompesData } from '@/components/onlinecompe/player-compe-dialog';
import { OnlineCompe } from "@/models/compe/req/online-compe-creation";
import { MergedCompe } from "@/models/compe/resp/online-compe-office";
import PlayerSearchDialog from "@/components/onlinecompe/player-search-dialog";
import { OnlineCompeJoinedPlayerHdcpReq, OnlineCompePlayerJoinReq } from "@/models/compe/req/online-compe-player";
import { PlayerInfo } from '@/models/compe/resp/player-resp';
import dayjs from "dayjs";
import PlayerInputHdcpDialog from '@/components/onlinecompe/player-input-hdcp-dialog';

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

interface GroupedTeeCourseData {
    [key: string]: TeeCourseData[];
}

interface JoinedCompePlayerData {
    isCart: boolean;
    player: TeePlayerData;
    cart: TeeCartData;
    course: TeeCourseData;
}

function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`course-tabpanel-${index}`}
            aria-labelledby={`course-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ marginTop: '6px', marginRight: '180px' }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

const Home = () => {
    const breadcrumbItems = [
        { label: "ティーシート", href: "/onlinecompe/teesheet" },
        { label: "ティーシート" },
    ];

    const [refreshCount, setRefreshCount] = useState(0);
    const [teeSheetData, setTeeCourseData] = useState<TeeCourseData[]>([]);
    const [groupedData, setGroupedData] = useState<GroupedTeeCourseData>({});
    const [selectedTab, setSelectedTab] = useState(0);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedPlayer, setSelectedPlayer] = useState<{
        name: string;
        joinedCompes: OnlineCompe[];
    } | null>(null);

    const [availableCompes, setAvailableCompes] = useState<AvailableCompesData[]>([]);
    const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false);
    const [isInputDialogOpen, setIsInputDialogOpen] = useState(false);
    const [oldCompeNoList, setOldCompeNoList] = useState<number[]>([]);
    useEffect(() => {
        getCompe();
    }, []);
    useEffect(() => {
        fetchData();
    }, [refreshCount]);

    useEffect(() => {
        // Group data by course_name
        const grouped = teeSheetData.reduce((acc: GroupedTeeCourseData, course) => {
            if (!acc[course.course_name]) {
                acc[course.course_name] = [];
            }
            acc[course.course_name].push(course);
            return acc;
        }, {});
        const sortedGroupedData = getSortedTeeSheetData(grouped);
        setGroupedData(sortedGroupedData);
    }, [teeSheetData]);

    //ティーシートデータ 配列ソート
    const getSortedTeeSheetData = (group: GroupedTeeCourseData): GroupedTeeCourseData => {
        const sortedGroupedData: GroupedTeeCourseData = {};
        Object.keys(group).forEach(groupKey => {
            sortedGroupedData[groupKey] = [...group[groupKey]].sort((cart1, cart2) => {
                const cart1Scheduled = cart1.cart_data[0].scheduled_start_time;
                const cart2Scheduled = cart2.cart_data[0].scheduled_start_time;
                const cart1Start = cart1.cart_data[0].start_time;
                const cart2Start = cart2.cart_data[0].start_time;
                const cart1CartNo = cart1.cart_data[0].cart_no;
                const cart2CartNo = cart2.cart_data[0].cart_no;
                if (cart1Scheduled || cart2Scheduled) {
                    if (cart1Scheduled && cart2Scheduled) {
                        return cart1Scheduled.localeCompare(cart2Scheduled);
                    }
                    return cart1Scheduled ? -1 : 1;
                }
                if (cart1Start || cart2Start) {
                    if (cart1Start && cart2Start) {
                        return cart1Scheduled.localeCompare(cart2Scheduled);
                    }
                    return cart1Scheduled ? -1 : 1;
                }
                return cart1CartNo - cart2CartNo;
            });
        })
        return sortedGroupedData;
    }
    const fetchData = async () => {
        try {
            const resp = await teeSheet(null);
            const courseDataSort = resp.data.sort((course1, course2) => Number(course1.course_index) - Number(course2.course_index));
            setTeeCourseData(courseDataSort);
        } catch (error) {
            console.error(error);
        }
    }

    //全部コンペを取得,旧コンペNoを保存する
    const getCompe = async () => {
        try {
            const resp = await listOfficeCompe({
                office_key: null,
                play_date: null,
                compe_type: null,
                compe_kind: null,
                free_word: null,
                offset: 0,
                limit: 100
            });
            const oldlCompes = resp.data.compes.filter(compe => compe.is_front_system);
            if (oldlCompes.length > 0) {
                setOldCompeNoList(oldlCompes.map(compe => compe.compe_no));
            }
        } catch (error) {
            console.error(error);
        }
    }

    const postPlayerData = async (req: OnlineCompeJoinedPlayerHdcpReq) => {
        try {
            const resp = await updateJoinedPlayerHdcp(req);
            if (resp) {
                setRefreshCount(prevCount => prevCount + 1);
            }
        } catch (error) {
            console.error(error)
        }
    }

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setSelectedTab(newValue);
    };

    const courseNames = Object.keys(groupedData);

    const playerData = useRef<JoinedCompePlayerData>();
    //コンペ参加APIが呼び出す
    const playerJoinCompe = async (joinCompes: AvailableCompesData[], players: TeePlayerData[] | null) => {
        if (players === null || players.length === 0) return;
        const result = await Promise.all(
            players.flatMap(player =>
                joinCompes.map(async compe => {
                    const req = getJoinCompeDataWithPlayer(compe, player);
                    if (req === null) return true;
                    try {
                        return await joinOnlineCompe(req);
                    } catch (error) {
                        return false;
                    }
                })
            )
        );
        if (!result.includes(false)) {
            setRefreshCount(prevCount => prevCount + 1);
        }
    }
    const handleJoinedCompe = async (compes: AvailableCompesData[]) => {
        const joinCompes = compes.filter(compe => compe.isJoined);
        if (playerData.current?.isCart) {
            if (playerData.current?.cart.players) {
                playerJoinCompe(joinCompes, playerData.current?.cart.players);
            }
        } else {
            if (playerData.current) {
                playerJoinCompe(joinCompes, [playerData.current.player]);
            }
        }
    }
    //選択するplayerの情報一旦保存する
    const handlerPlayerData = (isCart: boolean, player: TeePlayerData, cart: TeeCartData, course: TeeCourseData) => {
        playerData.current = {
            isCart: isCart,
            player: player,
            cart: cart,
            course: course,
        }
    }

    //カート全部player参加するコンペを取得
    const joinedCompeByAllCartPlayer = (cartData: TeeCartData): JoinedCompe[] => {
        let joinedCompes: JoinedCompe[] = [];
        if (cartData.players.length === 0) return [];
        const compeCountMap = new Map<number, number>();
        cartData.players.forEach(player => {
            const uniqueCompes = new Set(player.joined_compes.map(c => c.compe_no));
            uniqueCompes.forEach(compeNo => {
                compeCountMap.set(compeNo, (compeCountMap.get(compeNo) || 0) + 1);
            });
        });
        const totalPlayers = cartData.players.length;
        for (const [compeNo, count] of compeCountMap) {
            if (count === totalPlayers) {
                const compe = { compe_no: compeNo };
                joinedCompes.push(compe);
            }
        }
        return joinedCompes;
    }
    const handlePlayerClick = async (player: TeePlayerData | null, cart: TeeCartData, course: TeeCourseData) => {
        const joinedCompes = player ? player.joined_compes : joinedCompeByAllCartPlayer(cart);
        const compes = await Promise.all(
            joinedCompes.map(compe => getOnlineCompe(compe.compe_no))
        );
        handlerPlayerData(player === null, player ? player : cart.players[0], cart, course);
        const resp = await listOfficeCompe({
            office_key: null,
            play_date: null,
            compe_type: null,
            compe_kind: null,
            free_word: null,
            offset: 0,
            limit: 100
        });

        setSelectedPlayer({
            name: player ? player.player_name : '',
            joinedCompes: compes
        });

        if (resp.code === 200) {
            const mergedCompes: MergedCompe[] = resp.data.compes;

            //mergedCompes filter is_front_system = false
            const filteredCompes = mergedCompes.filter(compe => !compe.is_front_system);
            const allOnlineCompes = filteredCompes.map(compe => ({
                name: compe.compe_name,
                fee: compe.participation_fee ?? 0,
                compeNo: compe.compe_no,
                // if compe.compe_no is in joinedCompes, isJoined is true
                isJoined: joinedCompes.some(joined => joined.compe_no === compe.compe_no)
            }));
            setAvailableCompes(allOnlineCompes.filter(compe => !compe.isJoined));
        }

        setDialogOpen(true);
    };

    const handleHdcpPlayerInfo = (hdcp: string) => {
        if (playerData.current) {
            const hdcpReq: OnlineCompeJoinedPlayerHdcpReq = {
                cart_no: playerData.current.cart.cart_no,
                glid_no: '',
                hdcp: hdcp,
                hdcp_index: playerData.current.player.hdcp_index,
                gender: playerData.current.player.gender,
                birthday: playerData.current.player.birthday,
                play_date: dayjs().format('YYYYMMDD'),
                player_no: playerData.current.player.player_no,
                scheduled_start_time: playerData.current.cart.scheduled_start_time,
            }
            postPlayerData(hdcpReq);
        }
        setIsSearchDialogOpen(false);
    }

    const handleApplyPlayerInfo = (playerInfo: PlayerInfo) => {
        // Handle the selected player info
        if (playerData.current) {
            const hdcpReq: OnlineCompeJoinedPlayerHdcpReq = {
                cart_no: playerData.current.cart.cart_no,
                glid_no: playerInfo.glid_no,
                hdcp: playerData.current.player.hdcp,
                gender: playerInfo.gender,
                birthday: playerInfo.birthday,
                hdcp_index: playerInfo.hdcp_index,
                play_date: dayjs().format('YYYYMMDD'),
                player_no: playerData.current.player.player_no,
                scheduled_start_time: playerData.current.cart.scheduled_start_time,
            }
            postPlayerData(hdcpReq);
        }
        setIsSearchDialogOpen(false);
    };

    const getJoinCompeDataWithPlayer = (compe: AvailableCompesData, player: TeePlayerData): OnlineCompePlayerJoinReq | null => {
        if (playerData.current) {
            const req: OnlineCompePlayerJoinReq = {
                player_no: player.player_no,
                player_name: player.player_name,
                birthday: player.birthday,
                gender: player.gender,
                glid_no: player.glid_no,
                tee_id: player.tee_id,
                hdcp: player.hdcp,
                hdcp_index: player.hdcp_index,
                compe_no: compe.compeNo,
                office_key: localStorage.getItem('office_key') ?? '',
                play_date: dayjs().format('YYYYMMDD'),
                cart_no: playerData.current.cart.cart_no,
                course_index: Number(playerData.current.course.course_index),
                is_paid: false
            }
            return req;
        }
        return null;
    }
    const getJoinCompeData = (compe: AvailableCompesData): OnlineCompePlayerJoinReq | null => {
        if (playerData.current) {
            const req: OnlineCompePlayerJoinReq = {
                player_no: playerData.current.player.player_no,
                player_name: playerData.current.player.player_name,
                glid_no: playerData.current.player.glid_no,
                birthday: playerData.current.player.birthday,
                gender: playerData.current.player.gender,
                tee_id: playerData.current.player.tee_id,
                hdcp: playerData.current.player.hdcp,
                hdcp_index: playerData.current.player.hdcp_index,
                compe_no: compe.compeNo,
                office_key: localStorage.getItem('office_key') ?? '',
                play_date: dayjs().format('YYYYMMDD'),
                cart_no: playerData.current.cart.cart_no,
                course_index: Number(playerData.current.course.course_index),
                is_paid: false
            }
            return req;
        }
        return null;
    }

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box
                sx={{ display: "flex", flexDirection: "column", backgroundColor: '#f9f9f9', margin: "-24px" }}>
                <Box sx={{ display: 'flex', height: '65px', alignItems: 'center', backgroundColor: '#f3f3f3', flexDirection: "row", borderRadius: "5px", marginLeft: "18px", marginRight: "18px", marginTop: "25px", paddingLeft: "32px" }}>
                    <img src="/webapp/images/compe_ic_menu.png" style={{ width: '25px', height: '17px' }} />
                    <Typography variant="h2" sx={{ fontWeight: 'normal', marginLeft: "18px" }}>
                        ティーシート
                    </Typography>
                </Box>
                <Box sx={{ marginTop: '17px', marginLeft: '59px' }}>
                    <Tabs
                        value={selectedTab}
                        onChange={handleTabChange}
                        aria-label="course tabs"
                        variant="scrollable"
                        scrollButtons="auto"
                        TabIndicatorProps={{
                            style: { display: 'none' },
                        }}
                        sx={{ minHeight: '40px', height: '40px' }}
                    >
                        {courseNames.map((courseName, index) => (
                            <Tab
                                key={index}
                                label={courseName}
                                id={`course-tab-${index}`}
                                aria-controls={`course-tabpanel-${index}`}
                                sx={{
                                    minHeight: '40px',
                                    height: '40px',
                                    width: '150px',
                                    color: '#324F85',
                                    fontSize: '16px',
                                    backgroundColor: '#F3F3F3',
                                    '&.Mui-selected': {
                                        color: 'white',
                                        backgroundColor: '#324F85'
                                    },
                                }}
                            />
                        ))}
                    </Tabs>
                </Box>
                <Box sx={{ marginLeft: '59px' }}>
                    {courseNames.map((courseName, index) => (
                        <TabPanel key={index} value={selectedTab} index={index}>
                            <TableContainer component={Paper}>
                                <Table aria-label="tee sheet table">
                                    <TableHead>
                                        <TableRow sx={{
                                            minHeight: '30px',
                                            height: '35px',
                                            padding: "0px",
                                            backgroundColor: '#F3F3F3',
                                            '& th': {
                                                border: '1px solid #D1D1D1',
                                                textAlign: 'center',
                                            }
                                        }}>
                                            <TableCell sx={{ minWidth: '150px', width: '150px', fontSize: '16px', fontWeight: 'bold', padding: "0px", alignItems: 'center' }}>予定時間</TableCell>
                                            <TableCell sx={{ minWidth: '150px', width: '150px', fontSize: '16px', fontWeight: 'bold', padding: "0px", alignItems: 'center' }}>スタート時間</TableCell>
                                            <TableCell sx={{ minWidth: '124px', width: '124px', fontSize: '16px', fontWeight: 'bold', padding: "0px", alignItems: 'center' }}>カート番号</TableCell>
                                            <TableCell sx={{ minWidth: '200px', width: '200px', fontSize: '16px', fontWeight: 'bold', padding: "0px", alignItems: 'center' }}>コンペ名</TableCell>
                                            <TableCell sx={{ width: '1000px', fontSize: '16px', fontWeight: 'bold', padding: "0px", alignItems: 'center' }}>プレイヤー</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody sx={{ '& tr td': { border: '1px solid #eee' } }}>
                                        {groupedData[courseName].map((course) => (
                                            course.cart_data.map((cart, cartIndex) => (
                                                <TableRow key={cartIndex} sx={{ padding: '0px', height: '80px' }}>
                                                    <TableCell sx={{ width: '150px', textAlign: 'center', fontSize: '18px', fontWeight: 'bold' }}>
                                                        {cart.scheduled_start_time}
                                                    </TableCell>
                                                    <TableCell sx={{ width: '150px', textAlign: 'center', fontSize: '18px', fontWeight: 'bold' }}>
                                                        {cart.start_time}
                                                    </TableCell>
                                                    <TableCell sx={{ width: '124px', textAlign: 'center', fontSize: '18px', fontWeight: 'bold' }}>
                                                        {cart.cart_no}
                                                    </TableCell>
                                                    <TableCell sx={{
                                                        width: '200px',
                                                        textAlign: 'center',
                                                        fontSize: '18px',
                                                        fontWeight: 'mormal',
                                                        color: '#324F85',
                                                        position: 'relative',
                                                        verticalAlign: 'middle',
                                                    }}>
                                                        <Box
                                                            sx={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                            }}
                                                        >
                                                            <Typography sx={{
                                                                height: '40px',
                                                                width: '200px',
                                                                WebkitLineClamp: 3,
                                                                WebkitBoxOrient: 'vertical',
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap',
                                                            }}>
                                                                {cart.delegate_compe?.compe_name || '-'}
                                                            </Typography>

                                                        </Box>
                                                        <Button
                                                            variant="contained"
                                                            size="small"
                                                            sx={{
                                                                minWidth: '20px',
                                                                height: '20px',
                                                                width: '20px',
                                                                position: 'absolute',
                                                                bottom: 4,
                                                                right: 4,
                                                                fontSize: '16px',
                                                                fontWeight: 'bold',
                                                                backgroundColor: 'white',
                                                                color: '#324F85',
                                                                border: "1px solid #324F85",
                                                                textAlign: 'center',
                                                                borderRadius: '0px'
                                                            }}
                                                            onClick={() => {
                                                                handlePlayerClick(null, cart, course);
                                                            }}
                                                        >
                                                            +
                                                        </Button>
                                                    </TableCell>
                                                    <TableCell sx={{ padding: '0px', width: '1000px' }}>
                                                        <Box sx={{
                                                            display: 'flex',
                                                            gap: 0,
                                                            flexWrap: 'nowrap',
                                                            overflowX: 'auto'
                                                        }}>
                                                            {cart.players.map((player, playerIndex) => (
                                                                <Box
                                                                    key={playerIndex}
                                                                    sx={{
                                                                        cursor: 'pointer',
                                                                        borderRight: '1px solid #ccc',
                                                                        // '&:hover': {
                                                                        //     backgroundColor: 'rgba(251, 170, 38, 0.73)'
                                                                        // },
                                                                        padding: 0,
                                                                        width: '250px',
                                                                        height: '80px',
                                                                        flexShrink: 0,
                                                                    }}
                                                                >
                                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0px' }}>
                                                                        <Box sx={{ display: 'flex', flexDirection: 'column', width: 'auto', marginTop: '14px', marginLeft: '18px' }}>
                                                                            <Typography variant="body1" sx={{ fontSize: '16px' }}
                                                                                onClick={() => handlePlayerClick(player, cart, course)}>
                                                                                {player.player_name}</Typography>
                                                                            <Typography variant="body2" sx={{ color: '#2DAADF', fontSize: '16px' }}>{player.locker_no || '-'}</Typography>
                                                                        </Box>
                                                                        <Box sx={{ display: 'flex', flexDirection: 'column', marginTop: '8px' }}>
                                                                            <Box display="flex" flexDirection='row'>
                                                                                <Typography variant="body2"
                                                                                    sx={{
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        fontSize: '10px',
                                                                                        color: 'white',
                                                                                        backgroundColor: player.hdcp_index ? '#0E97FF' : '#979797',
                                                                                        borderRadius: '3px',
                                                                                        textAlign: 'center',
                                                                                        display: 'flex',
                                                                                        justifyContent: 'center',
                                                                                        alignItems: 'center'
                                                                                    }}
                                                                                    onClick={() => {
                                                                                        setIsSearchDialogOpen(true);
                                                                                        handlerPlayerData(false, player, cart, course);
                                                                                    }}
                                                                                >HDCP Index</Typography>
                                                                                <Typography variant="body2"
                                                                                    sx={{
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        fontSize: '10px',
                                                                                        marginLeft: '4px',
                                                                                        marginRight: '8px',
                                                                                        color: 'white',
                                                                                        backgroundColor: player.joined_compes.filter(compe => !oldCompeNoList.includes(compe.compe_no)).length > 0 ? '#FF8D37' : '#979797',
                                                                                        borderRadius: '3px',
                                                                                        textAlign: 'center',
                                                                                        display: 'flex',
                                                                                        justifyContent: 'center',
                                                                                        alignItems: 'center'
                                                                                    }}
                                                                                    onClick={() => handlePlayerClick(player, cart, course)}
                                                                                >コンペ</Typography>
                                                                            </Box>
                                                                            <Box display="flex" flexDirection='row' sx={{ marginTop: '5px', justifyContent: 'flex-end' }}>
                                                                                <Typography variant="body2"
                                                                                    sx={{
                                                                                        width: '30px',
                                                                                        height: '30px',
                                                                                        fontSize: '10px',
                                                                                        color: 'white',
                                                                                        backgroundColor: player.hdcp && player.hdcp !== '0.0' ? '#2DAE5D' : '#979797',
                                                                                        borderRadius: '3px',
                                                                                        textAlign: 'center',
                                                                                        display: 'flex',
                                                                                        justifyContent: 'center',
                                                                                        alignItems: 'center',
                                                                                        marginRight: '8px'
                                                                                    }}
                                                                                    onClick={() => {
                                                                                        handlerPlayerData(false, player, cart, course);
                                                                                        setIsInputDialogOpen(true);
                                                                                    }}
                                                                                >HDCP 手入力</Typography>
                                                                            </Box>
                                                                        </Box>
                                                                    </Box>
                                                                </Box>
                                                            ))}
                                                        </Box>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </TabPanel>
                    ))}
                </Box>
            </Box>
            {selectedPlayer && (
                <PlayerCompeDialog
                    open={dialogOpen}
                    onClose={() => setDialogOpen(false)}
                    playerName={selectedPlayer.name}
                    joinedCompes={selectedPlayer.joinedCompes}
                    availableCompes={availableCompes}
                    joinCompes={handleJoinedCompe}
                />
            )}

            <PlayerSearchDialog
                open={isSearchDialogOpen}
                onClose={() => setIsSearchDialogOpen(false)}
                onApply={handleApplyPlayerInfo}
            />
            <PlayerInputHdcpDialog
                open={isInputDialogOpen}
                onClose={() => setIsInputDialogOpen(false)}
                onApply={handleHdcpPlayerInfo}
                playerName={playerData.current?.player.player_name ? playerData.current?.player.player_name : ''}
                playerHdcp={playerData.current?.player.hdcp ? playerData.current?.player.hdcp : ''}
            ></PlayerInputHdcpDialog>
        </LocalizationProvider>
    );
}

export default Home;
