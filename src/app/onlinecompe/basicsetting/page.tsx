"use client";
import React, { useEffect, useState } from "react";
import { Box, Button, FormControlLabel, Typography, RadioGroup, Radio, TextField, MenuItem } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LeadBoardChange, NaviEntry, HdcpTypes, MarkerSetting } from "@/models/compe/enum-types";
import { updateDefaultSetting, getDefaultSetting } from "@/api/online-compe-api"
import { OnlineCompeDefaultSetting } from "@/models/compe/req/online-compe-default-setting";
import ApiDialog from "@/components/onlinecompe/api-dialog"

const Home = () => {
    const lineStyle = {
        border: '1px solid #999',
        height: 'calc(100% - 40px)',
        marginTop: '20px',
        marginBottom: '20px',
        marginLeft: '32px'
    };
    const itemStyle = {
        textAlign: 'right',
        marginRight: '20px',
        width: '160px',
        fontWeight: 'bold',
        fontSize: '16px'
    };
    const [naviEntryPermission, setNaviEntryPermission] = useState<NaviEntry>(NaviEntry.Disabled)
    const handleNaviEntryPermissionChange = (event: any) => {
        setNaviEntryPermission(Number(event.target.value));
    };
    const [roundType, setRoundType] = useState('0.5');
    const handleRoundChange = (event: any) => {
        setRoundType(event.target.value);
    };
    const [ranking, setRanking] = useState('');
    const rankingOptions = [
        { value: '0', label: '全ホール表示' },
        { value: '1', label: '最終3ホールを隠す' },
        { value: '2', label: '最終6ホールを隠す' },
        { value: '3', label: '最終9ホールを隠す' },
    ];
    const handleRankingChange = (event: any) => {
        setRanking(event.target.value);
        setDefaultSettingDataError(prev => prev.filter(error => error !== 'ranking'))
    };
    const [leaderBoard, setLeaderBoard] = useState<LeadBoardChange>(LeadBoardChange.Abled);
    const handleLeaderBoardChange = (event: any) => {
        setLeaderBoard(Number(event.target.value));
    };
    const boardOptions = [
        { value: 'net', label: 'ネットをデフォルト表示' },
        { value: 'gross', label: 'グロスをデフォルト表示' },
    ];
    const [leaderBoardType, setLeaderBoardType] = useState("net");
    const handleLeaderBoardTypeChange = (event: any) => {
        setLeaderBoardType(event.target.value);
    };
    const [defaultBoardType, setDefaultBoardType] = useState("gross");
    const handleDefaultBoardTypeChange = (event: any) => {
        setDefaultBoardType(event.target.value);
    };
    const [hdcpType, setHdcpType] = useState<HdcpTypes>(HdcpTypes.WHS);
    const handleHdcpTypeChange = (event: any) => {
        setHdcpType(Number(event.target.value));
    };
    const [markerSetting, setMarkerSetting] = useState<MarkerSetting>(MarkerSetting.Disabled);
    const handleMarkerSettingChange = (event: any) => {
        setMarkerSetting(Number(event.target.value));
    };
    const [defaultSettingDataError, setDefaultSettingDataError] = useState<string[]>([]);
    const checkDefaultSettingData = () => {
        let errorList: string[] = [];
        if (ranking.trim() === '') {
            errorList.push('ranking');
        }
        if (errorList.length === 0) {
            postDefaultSetting(createDefaultSettingData());
        }
        setDefaultSettingDataError(errorList);
    }
    const createDefaultSettingData = (): OnlineCompeDefaultSetting => {
        const compeSetting = {
            entry_from_navi: naviEntryPermission,
            ranking_aggregation: Number(ranking),
            round: roundType
        }
        const otherSetting = {
            marker_setting: markerSetting,
            leadboard_change: {
                type: leaderBoard, //0 しない 1 suru
                default: leaderBoardType,
            }
        }
        const handicap = {
            type: hdcpType
        }
        return {
            compe_setting: compeSetting,
            other_setting: otherSetting,
            handicap: handicap
        }
    }
    const [apiSucceed, setApiSucceed] = useState<boolean>(false);
    const [openApiDialog, setOpenApiDialog] = useState<boolean>(false)
    const handleCloseErrorDialog = () => {
        setOpenApiDialog(false);
    };
    useEffect(() => {
        getCompeDefaultSetting();
    }, []);
    const getCompeDefaultSetting = async () => {
        try {
            const result = await getDefaultSetting();
            if (Object.keys(result).length > 0) {
                setCompeDefaultSeting(result)
            }
        } catch (error) {

        }
    }
    const setCompeDefaultSeting = (defaultSetting: OnlineCompeDefaultSetting) => {
        setNaviEntryPermission(defaultSetting.compe_setting.entry_from_navi);
        setRanking(defaultSetting.compe_setting.ranking_aggregation.toString());
        setRoundType(defaultSetting.compe_setting.round);
        setLeaderBoard(defaultSetting.other_setting.leadboard_change.type);
        if (defaultSetting.other_setting.leadboard_change.type === 1) {
            setLeaderBoardType(defaultSetting.other_setting.leadboard_change.default);
        } else {
            setDefaultBoardType(defaultSetting.other_setting.leadboard_change.default);
        }
        setHdcpType(defaultSetting.handicap.type);
        setMarkerSetting(defaultSetting.other_setting.marker_setting);
    }
    //コンペ基本設定をサーバに送る
    const postDefaultSetting = async (defaultSetting: OnlineCompeDefaultSetting) => {
        try {
            const res = await updateDefaultSetting(defaultSetting);
            setApiSucceed(res);
            setOpenApiDialog(true)
        } catch (error) {
            setApiSucceed(false);
            setOpenApiDialog(true)
        }
    }
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box sx={{ display: "flex", flexDirection: "column", backgroundColor: '#f9f9f9', margin: "-24px", minHeight: '100vh' }}>
                <Box sx={{ display: 'flex', height: '65px', alignItems: 'center', backgroundColor: '#f3f3f3', flexDirection: "row", borderRadius: "5px", marginLeft: "18px", marginRight: "18px", marginTop: "25px", paddingLeft: "32px" }}>
                    <img src="/webapp/images/compe_ic_menu.png" style={{ width: '25px', height: '17px' }} />
                    <Typography variant="h2" sx={{ fontWeight: 'normal', marginLeft: "14px" }}>
                        基本設定
                    </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px", alignItems: 'center' }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        コンペ設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ナビからエントリー
                            </Typography>
                            <RadioGroup
                                value={naviEntryPermission}
                                onChange={handleNaviEntryPermissionChange}
                                row
                            >
                                <FormControlLabel
                                    value={NaviEntry.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="許可しない"

                                />
                                <FormControlLabel
                                    value={NaviEntry.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="許可する"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ランキング集計
                            </Typography>
                            <TextField
                                select
                                error={defaultSettingDataError?.includes('ranking')}
                                value={ranking}
                                size="small"
                                name="ranking"
                                variant="outlined"
                                style={{ width: '190px', fontSize: '14px', height: "30px" }}
                                InputProps={{
                                    style: { height: "30px" },
                                }}
                                onChange={handleRankingChange}
                            >
                                {rankingOptions.map((option, index) => (
                                    <MenuItem key={index} value={option.value} style={{ fontSize: '14px' }}>
                                        {option.label}
                                    </MenuItem>
                                ))}
                            </TextField>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px', marginBottom: '18px' }}>
                            <Typography component="div" sx={itemStyle}>
                                ラウンド
                            </Typography>
                            <RadioGroup
                                value={roundType}
                                onChange={handleRoundChange}
                                row
                            >
                                <FormControlLabel
                                    value="0.5"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="0.5ラウンド"

                                />
                                <FormControlLabel
                                    value="1"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="1ラウンド"
                                    sx={{ marginLeft: '20px' }}
                                />
                                <FormControlLabel
                                    value="1.5"
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="1.5ラウンド"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>

                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "10px", marginBottom: "14px", marginRight: "90px", alignItems: "center" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        ハンデ設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                使用ハンディキャップ
                            </Typography>
                            <RadioGroup
                                value={hdcpType}
                                onChange={handleHdcpTypeChange}
                                row
                            >
                                <FormControlLabel
                                    value={HdcpTypes.WHS}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="HDCP Index(WHS)"

                                />
                                <FormControlLabel
                                    value={HdcpTypes.Front}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="プライベートハンディキャップ"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "10px", marginBottom: "14px", marginRight: "90px", alignItems: "center" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '80px' }}>
                        その他設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '18px', }}>
                            <Typography component="div" sx={itemStyle}>
                                マーカー設定
                            </Typography>
                            <RadioGroup
                                value={markerSetting}
                                onChange={handleMarkerSettingChange}
                                row
                            >
                                <FormControlLabel
                                    value={MarkerSetting.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="しない"

                                />
                                <FormControlLabel
                                    value={MarkerSetting.Free}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="自由設定"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px', marginBottom: '18px', minWidth: '800px' }}>
                            <Typography component="div" sx={itemStyle}>
                                リーダーボード切替
                            </Typography>
                            <RadioGroup
                                value={leaderBoard}
                                onChange={handleLeaderBoardChange}
                                row
                                sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                            >
                                <FormControlLabel
                                    value={LeadBoardChange.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="する"

                                />
                                <TextField
                                    select
                                    disabled={leaderBoard !== LeadBoardChange.Abled}
                                    value={leaderBoardType}
                                    size="small"
                                    name="totalling"
                                    variant="outlined"
                                    style={{
                                        height: "30px",
                                        width: '200px',
                                    }}
                                    InputProps={{
                                        style: {
                                            width: '200px',
                                            height: "30px",
                                            fontSize: '14px',
                                        },
                                    }}
                                    onChange={handleLeaderBoardTypeChange}
                                >
                                    {boardOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </TextField>
                                <FormControlLabel
                                    value={LeadBoardChange.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="しない"
                                    sx={{ marginLeft: '20px' }}
                                />
                                <TextField
                                    select
                                    disabled={leaderBoard !== LeadBoardChange.Disabled}
                                    value={defaultBoardType}
                                    size="small"
                                    name="totalling"
                                    variant="outlined"
                                    style={{ width: '200px', height: "30px" }}
                                    InputProps={{
                                        style: {
                                            height: "30px",
                                            width: '200px',
                                            fontSize: '14px',
                                        },
                                    }}
                                    onChange={handleDefaultBoardTypeChange}
                                >
                                    {boardOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value} style={{ fontSize: '14px' }}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </TextField>
                            </RadioGroup>
                        </Box>
                    </Box>

                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        marginTop: '54px'
                    }}
                >
                    <Button variant="contained"
                        onClick={checkDefaultSettingData}>
                        保存
                    </Button>
                </Box>
                <ApiDialog
                    openApiDialog={openApiDialog}
                    handleCloseErrorDialog={handleCloseErrorDialog}
                    succeed={apiSucceed}
                    apiType={"defaultSetting"}>
                </ApiDialog>
            </Box>
        </LocalizationProvider>
    );
}

export default Home;
