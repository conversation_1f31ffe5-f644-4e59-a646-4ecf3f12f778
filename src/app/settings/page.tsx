"use client";
import React, { useState, useEffect, useCallback, useRef, useReducer } from "react";
import {
    Box, Button, Typography, Table,
    TableHead, TableRow, TableCell,
    TableBody, TextField,
    Modal, Fade, Backdrop, RadioGroup, Radio, FormControlLabel
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { CaddyNameTypes } from "@/models/compe/enum-types";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import SettingsItem from "../../components/settings-item";
import NotMoveSettingItem from "../../components/not-move-settings-item";
import { useTheme } from "@mui/material/styles";
import Breadcrumb from "../../components/breadcrumb";
import SurveyForm from "../../components/survey-form";
import {
    getQuestion, deleteQuestion, updateQuestion, postQuestion, updateQuestionIndex,
    updateEvaluations, getEvaluations, updateQuesnaireSettings, getQuesnaireSettings
} from "@/api/caddy-api";
import { CommonUtils } from '@/utils/common-utils';
import ErrorHint from "../../components/error-hint";
import { handleAuthToken } from "../../auth/Auth"

export interface QuestionDataBean {
    id: number,
    sqlIndex: number,
    question: string,
    isRequired: boolean,
}

const Home = () => {
    const previewStyle = {
        position: 'absolute' as 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '90%',
        maxWidth: 900,
        maxHeight: '90vh',
        bgcolor: 'background.paper',
        border: '1px solid #999',
        boxShadow: 24,
        p: 4,
        overflowY: 'auto',
        '@media (min-width: 600px)': {
            width: '80%',
        },
        '@media (min-width: 900px)': {
            width: 900,
        }
    };
    const [widthSize, setWidthSize] = useState(true);
    useEffect(() => {
        handleAuthToken().then(
            (value) => {
                if (value) {
                    const handleResize = () => {
                        if (window.innerWidth < 910) {
                            setWidthSize(false);
                        } else {
                            setWidthSize(true);
                        }
                    };

                    handleResize(); // Initialize the state based on the current window width
                    window.addEventListener("resize", handleResize);
                    return () => window.removeEventListener("resize", handleResize);
                } else {
                    // const login = process.env.BASE_URL?.replace("api/v2/web", "") + "ops/login";
                    // window.location.href = login;
                }
            },
        );

    }, []);
    //error
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [questingsApi, setQuestingsApi] = useState(false);
    const [questingsApiError, setQuestingsApiError] = useState(false);
    const [evaluationApi, setEvaluationApi] = useState(false);
    const [evaluationApiError, setEvaluationApiError] = useState(false);
    
    const handleErrorButtonClick = async (type: string) => {
        const tasks = [];
        if (type === 'top' || type === 'all') {
            tasks.push(fetchAndSetQuestions());
        }
        if (type === 'bottom' || type === 'all') {
            tasks.push(fetchEvaluations());
        }
        try {
            await Promise.all(tasks);
        } catch (error) {
            console.error('Error handling button click:', error);
        }
    };
    //dataBean
    const ProblemDataBean = (id: number, sqlIndex: number, question: string, isRequired: boolean) => ({
        id,
        sqlIndex,
        question,
        isRequired,
    });
    const EvaluationDataBean = (
        stage: number,
        scoreHint: string,
        contentHint: string,
        score: string,
        content: string,
    ) => {
        return { stage, content, score, contentHint, scoreHint };
    }
    //questions-var
    const [firstSetQuestions, setFirstSetQuestions] = useState<QuestionDataBean[]>([]);
    const [secondSetQuestions, setSecondSetQuestions] = useState<QuestionDataBean[]>([]);
    const [tempFirstSetQuestions, setTempFirstSetQuestions] = useState<{
        id: any,
        question: any,
        isRequired: any,
    }[]>([]);
    const [tempSecondSetQuestions, setTempSecondSetQuestions] = useState<{
        id: any,
        question: any,
        isRequired: any,
    }[]>([]);

    // 編集中の項目Id
    const [editingId, setEditingId] = useState<number | null>(null);

    //evaluations-var
    const fixedEvaluations = [
        EvaluationDataBean(1, '1', '非常に不満', "", ""),
        EvaluationDataBean(2, '2', 'やや不満', "", ""),
        EvaluationDataBean(3, '3', '普通', "", ""),
        EvaluationDataBean(4, '4', 'やや普通', "", ""),
        EvaluationDataBean(5, '5', '非常に満足', "", "")
    ];
    const [evaluations, setEvaluations] = useState([
        EvaluationDataBean(1, '1', '非常に不満', "", ""),
        EvaluationDataBean(2, '2', 'やや不満', "", ""),
        EvaluationDataBean(3, '3', '普通', "", ""),
        EvaluationDataBean(4, '4', 'やや普通', "", ""),
        EvaluationDataBean(5, '5', '非常に満足', "", ""),
    ]);
    const [caddyNameType, setCaddyNameType] = useState<CaddyNameTypes>(CaddyNameTypes.Caddy)
    const handleCaddyNameTypeChange = (event: any) => {
        setCaddyNameType(Number(event.target.value));
    };
    //logical-var
    const theme = useTheme();
    const breadcrumbItems = [
        { label: "アンケート", href: "/" },
        { label: "各種设定" },
    ];
    const [open, setOpen] = React.useState(false);
    const openPreview = () => setOpen(true);
    const closePreview = () => setOpen(false);
    //api-questions
    const fetchAndSetQuestions = async () => {
        setQuestingsApi(true)
        try {
            const responseQuestions = await getQuestion();
            setQuestingsApiError(false);
            if (responseQuestions) {
                const firstSetQuestions = responseQuestions
                    .filter((q) => q.type === 1)
                    .map((q) => ProblemDataBean(q.id, q.index, q.content, q.require === 1));

                const secondSetQuestions = responseQuestions
                    .filter((q) => q.type === 2)
                    .map((q) => ProblemDataBean(q.id, q.index, q.content, q.require === 1));

                setFirstSetQuestions(firstSetQuestions);
                setSecondSetQuestions(secondSetQuestions);
                setEditingId(null);
            } else {
                setFirstSetQuestions([]);
                setSecondSetQuestions([]);
                setEditingId(null);
            }
        } catch (error) {
            setQuestingsApiError(true)
            console.error("Error fetching questions:", error);
        }
    };
    const addQuestion = useCallback(async (id: any, type: any, content: any, require: any) => {
        if (type === 1) {
            setTempFirstSetQuestions((prevQuestions) =>
                prevQuestions.filter((question) => question.id !== id)
            );
        } else {
            setTempSecondSetQuestions((prevQuestions) =>
                prevQuestions.filter((question) => question.id !== id)
            );
        }
        await fetchAndSetQuestions();
    }, []);
    const delQuestion = useCallback(async (id: any) => {
        await fetchAndSetQuestions();
    }, []);
    const upQuestion = useCallback(async (id: any, content: any, require: any) => {
        await fetchAndSetQuestions();
    }, []);
    //api-evaluations
    const fetchEvaluations = async () => {
        setEvaluationApi(true)
        try {
            const response = await getEvaluations();
            setEvaluationApiError(false);
            if (response) {
                const updatedEvaluations = fixedEvaluations.map((item, index) => {
                    const apiItem = response[index] || { content: '', score: '' };
                    ;
                    return {
                        ...item,
                        content: apiItem.content || '',
                        score: apiItem.score ? apiItem.score.toString() : ''
                    };
                });
                console.log(updatedEvaluations)
                setEvaluations(updatedEvaluations);
            }
        } catch (error) {
            setEvaluationApiError(true)
            console.error("Error fetching questions:", error);
        }
    };

    useEffect(() => {
        fetchAndSetQuestions();
        fetchEvaluations();
        if (localStorage.getItem("caddy_name_type") === "2") {
            setCaddyNameType(CaddyNameTypes.CourseAtt);
        } else {
            setCaddyNameType(CaddyNameTypes.Caddy);
        }
    }, []);
    //question fun
    //addNewQuestion not api
    const addNewQuestion = (type: any) => {
        const newQuestion = {
            id: Date.now(),
            question: "",
            isRequired: false,
        };
        setEditingId(newQuestion.id);
        if (type === 1) {
            setTempFirstSetQuestions([...tempFirstSetQuestions, newQuestion]);
        } else if (type === 2) {
            setTempSecondSetQuestions([...tempSecondSetQuestions, newQuestion]);
        }
    };
    // //move fun
    const firstMoveCard = useCallback((dragIndex: any, hoverIndex: any, type: any) => {
        if (dragIndex > firstSetQuestions.length - 1 || hoverIndex > firstSetQuestions.length - 1) {
            return
        }
        if (type === 1) {
            setErrors(prevErrors => {
                const newErrors = { ...prevErrors };
                Object.keys(newErrors).forEach(key => {
                    newErrors[key] = "";
                });
                return newErrors;
            });
            // console.log("Move Card:", dragIndex, hoverIndex);
            const newQuestions = [...firstSetQuestions];
            const [draggedQuestion] = newQuestions.splice(dragIndex, 1);
            newQuestions.splice(hoverIndex, 0, draggedQuestion);
            setFirstSetQuestions(newQuestions);
        }
    }, [firstSetQuestions]);

    const secondMoveCard = useCallback((dragIndex: any, hoverIndex: any, type: any) => {
        if (dragIndex > secondSetQuestions.length - 1 || hoverIndex > secondSetQuestions.length - 1) {
            return
        }
        if (type === 2) {
            setErrors(prevErrors => {
                const newErrors = { ...prevErrors };
                Object.keys(newErrors).forEach(key => {
                    newErrors[key] = "";
                });
                return newErrors;
            });
            const newQuestions = [...secondSetQuestions];
            const [draggedQuestion] = newQuestions.splice(dragIndex, 1);
            newQuestions.splice(hoverIndex, 0, draggedQuestion);
            setSecondSetQuestions(newQuestions);
        }
    }, [secondSetQuestions]);

    const onDropComplete = async (type: any, id: any) => {
        // console.log("secondSetQuestions")
        let num
        if (type === 1) {
            const question = firstSetQuestions.find(q => q.id === id);
            num = question ? firstSetQuestions.indexOf(question) + 1 : 0;
        } else {
            const question = secondSetQuestions.find(q => q.id === id);
            num = question ? secondSetQuestions.indexOf(question) + 1 : 0;
        }
        // console.log(id + "--" + num)
        if (num > 0) {
            try {
                setErrors(prevErrors => {
                    const newErrors = { ...prevErrors };
                    Object.keys(newErrors).forEach(key => {
                        newErrors[key] = "";
                    });
                    return newErrors;
                });
                const success = await updateQuestionIndex(id, num);
                if (success) {
                    await fetchAndSetQuestions();
                    setErrors(prevErrors => ({ ...prevErrors, [id]: "" }));
                } else {
                    setErrors(prevErrors => ({ ...prevErrors, [id]: "設問の順番変更に失敗しました。" }));
                }
            } catch (error) {
                await fetchAndSetQuestions();
                setErrors(prevErrors => ({ ...prevErrors, [id]: "設問の順番変更に失敗しました。" }));
            }
        }
    };
    const addPrefixToQuestions = (questions: QuestionDataBean[], prefix: String) => {
        return questions.map(question => ({
            ...question,
            question: `${prefix} ${question.question}`
        }));
    };
    const prefixedFirstSet = addPrefixToQuestions(firstSetQuestions, "");
    const prefixedSecondSet = addPrefixToQuestions(secondSetQuestions, "");
    //question component
    const SettingsSection = ({
        questions,
        tempQuestions,
        moveCard,
        delQuestion,
        upQuestion,
        addQuestion,
        addNewQuestion,
        onDropComplete,
        type
    }: {
        questions: any,
        tempQuestions: any,
        moveCard: any,
        delQuestion: any,
        upQuestion: any,
        addQuestion: any,
        addNewQuestion: any,
        onDropComplete: any,
        type: any,
    }) => {
        return (
            <Box className="py-4">
                {questions.map((question: any, index: any) => (
                    <SettingsItem
                        key={question.id}
                        index={index}
                        question={question}
                        moveCard={moveCard}
                        onDelete={delQuestion}
                        onSave={upQuestion}
                        onDropComplete={onDropComplete}
                        componentType={type}
                        errorHintMsg={errors[question.id] || ""}
                        editingId={editingId}
                        onEdit={() => setEditingId(question.id)}
                    />
                ))}
                {tempQuestions.map((question: any, index: any) => (
                    <NotMoveSettingItem
                        key={question.id}
                        index={index + questions.length}
                        question={question}
                        onSave={addQuestion}
                        type={type}
                        editingId={editingId}
                    />
                ))}
                <Button variant="outlined" disabled={editingId !== null} onClick={() => addNewQuestion(type)} sx={{ fontWeight: 'bold' }}>
                    +設問追加
                </Button>
            </Box>
        );
    };
    //top
    const TopComponent = () => {
        return (
            <div>
                {!questingsApiError &&

                    <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white" }}>

                        <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                            <div
                                style={{
                                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                                    height: "16px",
                                    flexShrink: 0,
                                }}
                            ></div>
                            <Typography
                                noWrap
                                component="span"
                                sx={{ fontWeight: "bold", marginLeft: '16px' }}
                                color="primary"
                            >
                                アンケート設問設定
                            </Typography>
                        </div>
                        <Box className="py-4">
                            <div>
                                ＜{ CommonUtils.getCaddyName(caddyNameType + '') }評価設問設定＞※ここで設定した設問の回答は、{ CommonUtils.getCaddyName(caddyNameType + '') }の評価として集計されます。
                            </div>
                            <SettingsSection
                                questions={firstSetQuestions}
                                moveCard={firstMoveCard}
                                delQuestion={delQuestion}
                                upQuestion={upQuestion}
                                addQuestion={addQuestion}
                                tempQuestions={tempFirstSetQuestions}
                                addNewQuestion={addNewQuestion}
                                onDropComplete={onDropComplete}
                                type={1}
                            />
                        </Box>

                        <Box className="py-4">
                            <div>
                                ＜ゴルフ場評価設問設定＞※ここで設定した設問の回答は、ゴルフ場の評価として集計されます。
                            </div>
                            <SettingsSection
                                questions={secondSetQuestions}
                                moveCard={secondMoveCard}
                                delQuestion={delQuestion}
                                upQuestion={upQuestion}
                                addQuestion={addQuestion}
                                tempQuestions={tempSecondSetQuestions}
                                addNewQuestion={addNewQuestion}
                                onDropComplete={onDropComplete}
                                type={2}
                            />
                        </Box>
                    </Box>

                }
                {questingsApiError &&
                    <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white" }}>

                        <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                            <div
                                style={{
                                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                                    height: "16px",
                                    flexShrink: 0,
                                }}
                            ></div>
                            <Typography
                                noWrap
                                component="span"
                                sx={{ fontWeight: "bold", marginLeft: '16px' }}
                                color="primary"
                            >
                                アンケート設問設定
                            </Typography>
                        </div>
                        <ErrorHint onButtonClick={() => handleErrorButtonClick("top")} screenWidth={widthSize} />
                    </Box>
                }
            </div>
        )
    }
    //evaluations fun
    //evaluations component
    const BottomComponent = () => {
        type State = {
            error: boolean;
            netError: boolean;
        };

        type Action =
            | { type: 'SET_ERROR'; payload: boolean }
            | { type: 'SET_NET_ERROR'; payload: boolean };

        const reducer = (state: State, action: Action) => {
            switch (action.type) {
                case 'SET_ERROR':
                    return { ...state, error: action.payload };
                case 'SET_NET_ERROR':
                    return { ...state, netError: action.payload };
                default:
                    return state;
            }
        };

        const initialState: State = {
            error: false,
            netError: false,
        };

        const [state, dispatch] = useReducer(reducer, initialState);

        const evaluationRefs = useRef<{ textFieldRef: HTMLInputElement | null, scoreFieldRef: HTMLInputElement | null }[]>([]);
        const [errorIndices, setErrorIndices] = useState<{ [key: number]: { content: boolean; score: boolean } }>({});

        const saveSettings = async () => {
            let hasInvalidInput = false;
            let errorIndicesTemp: { [key: number]: { content: boolean; score: boolean } } = {};

            const maxContentLength = 10;
            const maxScoreLength = 5;

            const evaluationsData = evaluations.map((_, index) => {
                const textFieldRef = evaluationRefs.current[index]?.textFieldRef;
                const scoreFieldRef = evaluationRefs.current[index]?.scoreFieldRef;
                if (textFieldRef && scoreFieldRef) {
                    const content = textFieldRef.value.trim();
                    const score = parseInt(scoreFieldRef.value, 10);

                    let contentError = false;
                    let scoreError = false;

                    if (content === '' || isNaN(score)) {
                        hasInvalidInput = true;
                        if (content === '') contentError = true;
                        if (isNaN(score)) scoreError = true;
                    } else if (content.length > maxContentLength) {
                        contentError = true;
                    } else if (score > Math.pow(10, maxScoreLength) - 1) {
                        scoreError = true;
                    }

                    if (contentError || scoreError) {
                        errorIndicesTemp[index] = { content: contentError, score: scoreError };
                    }

                    return {
                        id: index + 1,
                        stage: index + 1,
                        content: textFieldRef.value,
                        score: parseInt(scoreFieldRef.value, 10)
                    };
                }
                return { id: 0, stage: index + 1, content: '', score: 0 };
            });

            setErrorIndices(errorIndicesTemp);

            if (hasInvalidInput) {
                dispatch({ type: 'SET_ERROR', payload: true });
                return;
            }
            dispatch({ type: 'SET_ERROR', payload: false });

            try {
                const success = await updateEvaluations(evaluationsData);
                if (success) {
                    dispatch({ type: 'SET_NET_ERROR', payload: false });
                    await fetchEvaluations();
                } else {
                    dispatch({ type: 'SET_NET_ERROR', payload: true });
                }
            } catch (e) {
                dispatch({ type: 'SET_NET_ERROR', payload: true });
            }
        };

        return (
            <div>
                {!evaluationApiError &&
                    <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                        <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                            <div
                                style={{
                                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                                    height: "16px",
                                    flexShrink: 0,
                                }}
                            ></div>
                            <Typography
                                noWrap
                                component="span"
                                sx={{ fontWeight: "bold", marginLeft: '16px' }}
                                color="primary"
                            >
                                評価文言/配点設定
                            </Typography>
                        </div>
                        <Box>
                            {(state.error) ? (
                                <div style={{ color: 'red' }}>
                                    {"必須項目の入力をお願いします。"}
                                </div>
                            ) : (state.netError) ? (
                                <div style={{ color: 'red' }}>
                                    {"評価文言/配点設定の保存に失敗しました。"}
                                </div>
                            ) : (
                                <div></div>
                            )
                            }
                            <div>
                                設問への評価の文言と、各評価の配点を設定できます。こちらで設定した配点を、集計の際点数として利用します。
                            </div>
                            <div className="flex-row">
                                <Table sx={{ minWidth: 250, maxWidth: 450, marginTop: '20px' }} aria-label="simple table">
                                    <TableHead>
                                        <TableRow sx={{ minHeight: '30px' }}>
                                            <TableCell sx={{
                                                fontSize: '15px',
                                                border: '1px solid #304A89',
                                                color: '#304A89',
                                                padding: '10px',
                                                whiteSpace: 'nowrap'
                                            }}>
                                                評価段階
                                            </TableCell>
                                            <TableCell sx={{
                                                fontSize: '15px',
                                                border: '1px solid #304A89',
                                                color: '#304A89',
                                                padding: '10px'
                                            }}>
                                                評価文言
                                            </TableCell>
                                            <TableCell sx={{
                                                fontSize: '15px',
                                                border: '1px solid #304A89',
                                                color: '#304A89',
                                                padding: '10px'
                                            }}>
                                                評価配点
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {evaluations.map((evaluation, index) => (
                                            <TableRow key={evaluation.stage}>
                                                <TableCell sx={{
                                                    fontSize: '19px',
                                                    color: '#304A89',
                                                    border: '1px solid #304A89',
                                                    paddingLeft: '10px',
                                                    paddingRight: '10px',
                                                    paddingTop: '5px',
                                                    paddingBottom: '5px'
                                                }}>{evaluation.stage}</TableCell>
                                                <TableCell sx={{
                                                    border: '1px solid #304A89',
                                                    paddingLeft: '10px',
                                                    paddingRight: '0px',
                                                    paddingTop: '5px',
                                                    paddingBottom: '5px'
                                                }}>
                                                    <TextField
                                                        size="small"
                                                        placeholder={evaluation.contentHint}
                                                        defaultValue={evaluation.content}
                                                        inputRef={ref => {
                                                            if (!evaluationRefs.current[index]) {
                                                                evaluationRefs.current[index] = {
                                                                    textFieldRef: null,
                                                                    scoreFieldRef: null
                                                                };
                                                                ;
                                                            }
                                                            evaluationRefs.current[index].textFieldRef = ref;
                                                        }}
                                                        inputProps={{
                                                            maxLength: 10
                                                        }}
                                                        sx={{
                                                            border: errorIndices[index]?.content ? '2px solid red' : '2px solid #00000000',
                                                            borderRadius: "8px",
                                                            position: 'relative'
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell className="flex" sx={{
                                                    borderBottom: '1px solid #304A89',
                                                    borderRight: '1px solid #304A89',
                                                    paddingLeft: '10px',
                                                    paddingRight: '10px',
                                                    paddingTop: '5px',
                                                    paddingBottom: '5px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                }}>
                                                    <TextField
                                                        size="small"
                                                        placeholder={evaluation.scoreHint}
                                                        defaultValue={evaluation.score}
                                                        inputRef={ref => {
                                                            if (!evaluationRefs.current[index]) {
                                                                evaluationRefs.current[index] = {
                                                                    textFieldRef: null,
                                                                    scoreFieldRef: null
                                                                };
                                                            }
                                                            evaluationRefs.current[index].scoreFieldRef = ref;
                                                        }}
                                                        inputProps={{
                                                            maxLength: 5
                                                        }}
                                                        onChange={(event) => {
                                                            const value = event.target.value;
                                                            const newValue = value.replace(/[^0-9]/g, '');
                                                            if (newValue !== value) {
                                                                event.target.value = newValue;
                                                            }
                                                        }}
                                                        sx={{
                                                            border: errorIndices[index]?.score ? '2px solid red' : '2px solid #00000000',
                                                            borderRadius: "8px",
                                                            width: {
                                                                xs: '45px',
                                                                sm: '90px'
                                                            },
                                                            paddingTop: '0px',
                                                            position: 'relative',
                                                        }}
                                                    />
                                                    <span className="flex items-center" style={{
                                                        color: '#304A89',
                                                        marginLeft: '5px', fontSize: "15px"
                                                    }}>点</span>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                            <div className="py-4 space-x-4 ">
                                <Button variant="outlined" onClick={saveSettings} sx={{ fontWeight: 'bold' }}
                                    style={{ fontWeight: 'bold !important' }}>設定を保存</Button>
                                <Button
                                    variant="outlined"
                                    onClick={openPreview}
                                    startIcon={<img src="/webapp/images/ic_preview.png" alt="" style={{ width: 16, height: 12 }} />}>
                                    プレビュー
                                </Button>
                            </div>
                        </Box>
                    </Box>
                }
                {evaluationApiError &&
                    <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                        <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                            <div
                                style={{
                                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                                    height: "16px",
                                    flexShrink: 0,
                                }}
                            ></div>
                            <Typography
                                noWrap
                                component="span"
                                sx={{ fontWeight: "bold", marginLeft: '16px' }}
                                color="primary"
                            >
                                評価文言/配点設定
                            </Typography>
                        </div>
                        <ErrorHint onButtonClick={() => handleErrorButtonClick("bottom")} screenWidth={widthSize} />
                    </Box>
                }
            </div>
        )
    }


    // 表示設定Component
    const CaddyNameSettingComponent = () => {
        type State = {
            error: boolean;
            netError: boolean;
        };

        type Action =
            | { type: 'SET_ERROR'; payload: boolean }
            | { type: 'SET_NET_ERROR'; payload: boolean };

        const reducer = (state: State, action: Action) => {
            switch (action.type) {
                case 'SET_ERROR':
                    return { ...state, error: action.payload };
                case 'SET_NET_ERROR':
                    return { ...state, netError: action.payload };
                default:
                    return state;
            }
        };

        const initialState: State = {
            error: false,
            netError: false,
        };

        const [state, dispatch] = useReducer(reducer, initialState);

        const saveQuesnaireSettings = async () => {
            dispatch({ type: 'SET_ERROR', payload: false });

            try {
                const success = await updateQuesnaireSettings({ caddy_name_type: caddyNameType });
                if (success) {
                    dispatch({ type: 'SET_NET_ERROR', payload: false });
                    localStorage.setItem("caddy_name_type", caddyNameType + '');
                    console.log(localStorage.getItem("caddy_name_type"));
                    window.location.reload();
                } else {
                    dispatch({ type: 'SET_NET_ERROR', payload: true });
                }
            } catch (e) {
                dispatch({ type: 'SET_NET_ERROR', payload: true });
            }
        };

        return (
            <div>
                <Box borderRadius={2} sx={{ p: 2, backgroundColor: "white", marginTop: '20px' }}>
                    <div className="flex items-center" style={{ flexWrap: 'wrap' }}>
                        <div
                            style={{
                                borderLeft: `4px solid ${theme.palette.primary.main}`,
                                height: "16px",
                                flexShrink: 0,
                            }}
                        ></div>
                        <Typography
                            noWrap
                            component="span"
                            sx={{ fontWeight: "bold", marginLeft: '16px' }}
                            color="primary"
                        >
                            表示設定
                        </Typography>
                    </div>
                    <Box className="py-4">
                        {(state.error) ? (
                            <div style={{ color: 'red' }}>
                                {"必須項目の入力をお願いします。"}
                            </div>
                        ) : (state.netError) ? (
                            <div style={{ color: 'red' }}>
                                {"表示設定の保存に失敗しました。"}
                            </div>
                        ) : (
                            <div></div>
                        )
                        }
                        <div>
                            キャディの表示名を設定できます。
                        </div>
                        <div className="flex-row">
                            <RadioGroup
                                value={caddyNameType}
                                onChange={handleCaddyNameTypeChange}
                                row
                            >
                                <FormControlLabel
                                    value={CaddyNameTypes.Caddy}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="キャディ"

                                />
                                <FormControlLabel
                                    value={CaddyNameTypes.CourseAtt}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="コースアテンダント"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </div>
                        <div className="py-4 space-x-4 ">
                            <Button variant="outlined" onClick={saveQuesnaireSettings} sx={{ fontWeight: 'bold' }}
                                style={{ fontWeight: 'bold !important' }}>設定を保存</Button>
                        </div>
                    </Box>
                </Box>
            </div>
        )
    }


    //dialog component
    const SettingDialog = () => {
        return (
            <Modal
                aria-labelledby="transition-modal-title"
                aria-describedby="transition-modal-description"
                open={open}
                onClose={closePreview}
                closeAfterTransition
                slots={{ backdrop: Backdrop }}
                slotProps={{
                    backdrop: {
                        timeout: 500,
                    },
                }}
            >
                <Fade in={open}>
                    <Box sx={previewStyle}>
                        <Typography id="transition-modal-title" color="primary" variant="h1" component="h1"
                            className="flex justify-center py-3">
                            プレビュー
                        </Typography>
                        <Typography id="transition-modal-description" color="primary" variant="h6"
                            className="px-9 py-2">
                            <p>クラブ確認画面の下半分にあるアンケ一トの設定がこちらから行えます。</p>
                            <p>下画像の赤枠内、各設問の解答欄の文言を「評価文言」と定義します。</p>
                            <p>評価文言ごとに配点を設定することができ、集計の際には設定した配点を点数として利用します。</p>
                        </Typography>

                        <div className="py-4">
                            <SurveyForm questions={[...prefixedFirstSet, ...prefixedSecondSet]}
                                options={evaluations}></SurveyForm>
                        </div>

                        <div className="flex justify-center items-center">
                            <Button variant="outlined" onClick={closePreview}>閉じる</Button>
                        </div>
                    </Box>
                </Fade>
            </Modal>)
    }
    // end**********************
    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DndProvider backend={HTML5Backend}>
                {(!questingsApiError || !evaluationApiError) ? (
                    <Box
                        flex={1}
                        overflow="auto"
                        sx={{ display: "flex", flexDirection: "column" }}
                    >
                        <Breadcrumb items={breadcrumbItems} />

                        <TopComponent />

                        <BottomComponent />

                        <CaddyNameSettingComponent />
                    </Box>
                )
                    : ((questingsApiError && evaluationApiError)) ? (
                        <Box
                            flex={1}
                            overflow="auto"
                            sx={{ display: "flex", flexDirection: "column" }}
                        >
                            <Breadcrumb items={breadcrumbItems} />
                            <ErrorHint onButtonClick={() => handleErrorButtonClick("all")} screenWidth={widthSize}
                                isAll={(questingsApiError && evaluationApiError)} />
                        </Box>
                    )
                        : (
                            <div></div>
                        )
                }
                <SettingDialog />
            </DndProvider>
        </LocalizationProvider>
    );
};

export default Home;
