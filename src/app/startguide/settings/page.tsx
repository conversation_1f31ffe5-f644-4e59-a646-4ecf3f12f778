"use client";
import React, { useEffect, useState } from "react";
import { Box, Button, FormControlLabel, Typography, RadioGroup, Radio, TextField } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { EnableStartTime, EnableAutoStart, AutoStartType } from "@/models/compe/enum-types";
import ApiDialog from "@/components/onlinecompe/api-dialog"
import { getStartGuideSettings, updateStartGuideSettings } from "@/api/start-guide-api";
import { StartGuideSettings } from "@/models/startguide/resp/start-guide-setting";
import InfoIcon from '@mui/icons-material/Info';


const Home = () => {
    const lineStyle = {
        border: '1px solid #999',
        height: 'calc(100% - 40px)',
        marginTop: '20px',
        marginBottom: '20px',
        marginLeft: '32px'
    };
    const itemStyle = {
        textAlign: 'right',
        marginRight: '20px',
        width: '160px',
        fontWeight: 'bold',
        fontSize: '16px'
    };
    
    const [enableStartTime, setEnableStartTime] = useState<number>(0);
    const [mainTextAlways, setMainTextAlways] = useState<string>('');
    const [subTextAlways, setSubTextAlways] = useState<string>('');
    const [subTextAuto, setSubTextAuto] = useState<string>('');
    const [enableAutoStart, setEnableAutoStart] = useState<number>(0);
    const [autoStartType, setAutoStartType] = useState<number>(0);
    const [startTimeSchedule, setStartTimeSchedule] = useState<number>(1);
    const [startNumber, setStartNumber] = useState<number>(1);

    const [previewMainTextAlways, setPreviewMainTextAlways] = useState<string>('');
    const [previewSubTextAlways, setPreviewSubTextAlways] = useState<string>('');
    const [previewMainTextAuto, setPreviewMainTextAuto] = useState<string>('');
    const [previewSubTextAuto, setPreviewSubTextAuto] = useState<string>('');
        
    const [dataError, setDataError] = useState<string[]>([]);

    const handleMainTextAlwaysChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setMainTextAlways(event.target.value);
    };
    const handleSubTextAlwaysChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setSubTextAlways(event.target.value);
    };

    const handleEnableStartTimeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEnableStartTime(Number(event.target.value));
    };

    const handleEnableAutoStartChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEnableAutoStart(Number(event.target.value));
    };

    const handleAutoStartTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setAutoStartType(Number(event.target.value));
    };

    const handleStartTimeScheduleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const ts = Number(event.target.value);
        if (ts > 59) {
            setStartTimeSchedule(59);
        } else if (ts < 1) {
            setStartTimeSchedule(1);
        } else {
            setStartTimeSchedule(ts);
        }
    };

    const handleStartNumberChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setStartNumber(Number(event.target.value));
        const sn = Number(event.target.value);
        if (sn > 9) {
            setStartNumber(9);
        } else if (sn < 1) {
            setStartNumber(1);
        } else {
            setStartNumber(sn);
        }
    };
    
    const handleSubTextAutoChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setSubTextAuto(event.target.value);
    };
    
    const checkStartGuideSettingsData = () => {
        let errorList: string[] = [];

        if (Number(enableStartTime) === 1) {
            if (mainTextAlways.trim() === '') {
                errorList.push('mainTextAlways');
            }
            if (subTextAlways.trim() === '') {
                errorList.push('subTextAlways');
            }
            if (Number(enableAutoStart) === 1) {
                if (subTextAuto.trim() === '') {
                    errorList.push('subTextAuto');
                }
                if (Number(autoStartType) === 1) {
                    if (Number(startNumber) <= 0 || Number(startNumber) > 9) {
                        errorList.push('startNumber');
                    }
                } else {
                    if (Number(startTimeSchedule) <= 0 || Number(startTimeSchedule) > 59) {
                        errorList.push('startTimeSchedule');
                    }
                }
            }
        }
        
        if (errorList.length === 0) {
            postStartGuideSettings(createStartGuideSettingsData());
        }
        setDataError(errorList);
    }
    const createStartGuideSettingsData = (): StartGuideSettings => {
        return {
            enable_start_time: Number(enableStartTime),
            main_text_always: mainTextAlways,
            sub_text_always: subTextAlways,
            enable_autostart: Number(enableAutoStart),
            autostart_type: Number(autoStartType),
            start_time_schedule: Number(startTimeSchedule),
            start_number: Number(startNumber),
            sub_text_auto: subTextAuto, 
        }
    }
    const [apiSucceed, setApiSucceed] = useState<boolean>(false);
    const [openApiDialog, setOpenApiDialog] = useState<boolean>(false)
    const handleCloseErrorDialog = () => {
        setOpenApiDialog(false);
    };
    useEffect(() => {
        fetchStartGuideSettings();
    }, []);
    const fetchStartGuideSettings = async () => {
        try {
            const result = await getStartGuideSettings();
            if (Object.keys(result).length > 0) {
                setEnableStartTime(result.enable_start_time);
                setMainTextAlways(result.main_text_always);
                setSubTextAlways(result.sub_text_always);
                setEnableAutoStart(result.enable_autostart);
                setAutoStartType(result.autostart_type);
                setStartTimeSchedule(result.start_time_schedule);
                setStartNumber(result.start_number);
                setSubTextAuto(result.sub_text_auto);

                setPreviewMainTextAlways(result.main_text_always);
                setPreviewSubTextAlways(result.sub_text_always);
                updatePreviewMainTextAuto(result.autostart_type, result.start_time_schedule, result.start_number);
                setPreviewSubTextAuto(result.sub_text_auto);
            }
        } catch (error) {

        }
    }

    //スタート案内設定をサーバに送る
    const postStartGuideSettings = async (startGuideSettings: StartGuideSettings) => {
        try {
            const res = await updateStartGuideSettings(startGuideSettings);
            setApiSucceed(res);
            setOpenApiDialog(true)
        } catch (error) {
            setApiSucceed(false);
            setOpenApiDialog(true)
        }
    }

    const clickPreviewAlwaysText = () => {
        setPreviewMainTextAlways(mainTextAlways)
        setPreviewSubTextAlways(subTextAlways)
    }

    const clickPreviewAutoText = () => {
        updatePreviewMainTextAuto(autoStartType, startTimeSchedule, startNumber)
        setPreviewSubTextAuto(subTextAuto)
    }

    const updatePreviewMainTextAuto = (autoStartType : number, startTimeSchedule: number, startNumber: number) => {
        setPreviewMainTextAuto("カート番号***、***、***の皆様は、スタートホールへご移動ください")
    }


    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box sx={{ display: "flex", flexDirection: "column", backgroundColor: '#f9f9f9', margin: "-24px" }}>
                <Box sx={{ display: 'flex', height: '65px', alignItems: 'center', backgroundColor: '#f3f3f3', flexDirection: "row", borderRadius: "5px", marginLeft: "18px", marginRight: "18px", marginTop: "25px", paddingLeft: "32px" }}>
                    <img src="/webapp/images/compe_ic_menu.png" style={{ width: '25px', height: '17px' }} />
                    <Typography variant="h2" sx={{ fontWeight: 'normal', marginLeft: "14px" }}>
                        スタート案内設定
                    </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "14px", marginBottom: "14px", marginRight: "90px", alignItems: 'center' }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '100px' }}>
                        項目表示設定
                    </div>
                    <div style={lineStyle}></div>
                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                スタート予定時間
                            </Typography>
                            <RadioGroup
                                value={enableStartTime}
                                onChange={handleEnableStartTimeChange}
                                row
                            >
                                <FormControlLabel
                                    value={EnableStartTime.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="表示"

                                />
                                <FormControlLabel
                                    value={EnableStartTime.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="非表示"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'white', marginLeft: "89px", marginTop: "10px", marginBottom: "14px", marginRight: "90px" }}>
                    <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold', width: '100px' }}>
                        インフォメーション表示設定
                    </div>
                    <div style={lineStyle}></div>


                    <Box flex={1} sx={{ display: 'flex', flexDirection: 'column' }}>
                        <div style={{ display: 'flex', height: '50px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold' }}>
                            常時表示インフォメーション
                        </div>

                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                メイン文言設定<br/>(30文字まで)
                            </Typography>
                            <TextField
                                value={mainTextAlways}
                                error={dataError?.includes('mainTextAlways')}
                                onChange={handleMainTextAlwaysChange}
                                style={{ width: '644px', fontSize: '14px', height: "30px", marginRight: '20px' }}
                                inputProps={{ maxLength: 30 }}
                                InputProps={{
                                    inputMode: "numeric",
                                    style: { height: "30px" },
                                }}
                                size="small"
                            ></TextField>
                            <Button variant="contained"
                                onClick={clickPreviewAlwaysText}>
                                プレビュー
                            </Button>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                サブ文言設定<br/>(45文字まで)
                            </Typography>
                            <TextField
                                value={subTextAlways}
                                error={dataError?.includes('subTextAlways')}
                                onChange={handleSubTextAlwaysChange}
                                style={{ width: '644px', fontSize: '14px', height: "30px", marginRight: '20px' }}
                                inputProps={{ maxLength: 45 }}
                                InputProps={{
                                    inputMode: "numeric",
                                    style: { height: "30px" },
                                }}
                                size="small"
                            ></TextField>
                            <Button variant="contained"
                                onClick={clickPreviewAlwaysText}>
                                プレビュー
                            </Button>
                        </Box>

                        <div style={{ display: 'flex', height: '30px', marginTop: '30px', marginLeft: '60px', fontSize: '16px', fontWeight: 'bold' }}>
                            常時表示インフォメーションプレビュー（モニター下部）
                        </div>
                        <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'black', marginLeft: "60px", marginTop: "0px", marginBottom: "14px", marginRight: "120px", alignItems: "center" }}>
                            <InfoIcon style={{ color: 'yellow', marginLeft: '30px', width: '60px', height: '60px' }} />
                            <Box sx={{ display: 'flex', flexDirection: 'column', backgroundColor: 'black', marginLeft: "10px", marginTop: "0px", marginBottom: "0px", marginRight: "30px" }}>
                                <div style={{ display: 'flex', height: '30px', marginTop: '15px', marginLeft: '0px', fontSize: '16px', fontWeight: 'bold', color: 'yellow', alignItems: 'center' }}>
                                    {previewMainTextAlways}
                                </div>
                                <div style={{ display: 'flex', height: '20px', marginBottom: '15px', marginLeft: '0px', fontSize: '12px', fontWeight: 'bold', color: 'white', alignItems: 'center' }}>
                                    {previewSubTextAlways}
                                </div>
                            </Box>
                        </Box>

                        <div style={{ display: 'flex', height: '30px', marginTop: '0px', marginLeft: '60px', fontSize: '16px', fontWeight: 'bold' }}>
                            常時表示インフォメーションプレビュー（タブレット端末）
                        </div>
                        <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: '#002874', marginLeft: "60px", marginTop: "0px", marginBottom: "14px", marginRight: "120px", alignItems: "center" }}>
                            <InfoIcon style={{ color: 'white', marginLeft: '30px', width: '60px', height: '60px' }} />
                            <Box sx={{ display: 'flex', flexDirection: 'column', marginLeft: "10px", marginTop: "0px", marginBottom: "0px", marginRight: "30px" }}>
                                <div style={{ display: 'flex', height: '30px', marginTop: '15px', marginLeft: '0px', fontSize: '16px', fontWeight: 'bold', color: 'white', alignItems: 'center' }}>
                                    {previewMainTextAlways}
                                </div>
                                <div style={{ display: 'flex', height: '20px', marginBottom: '15px', marginLeft: '0px', fontSize: '12px', fontWeight: 'bold', color: 'white', alignItems: 'center' }}>
                                    {previewSubTextAlways}
                                </div>
                            </Box>
                        </Box>


                        <div style={{ display: 'flex', height: '30px', marginTop: '20px', marginLeft: '30px', fontSize: '16px', fontWeight: 'bold' }}>
                            自動スタート案内時表示インフォメーション
                        </div>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={itemStyle}>
                                自動スタート案内
                            </Typography>
                            <RadioGroup
                                value={enableAutoStart}
                                onChange={handleEnableAutoStartChange}
                                row
                            >
                                <FormControlLabel
                                    value={EnableAutoStart.Abled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="ON"

                                />
                                <FormControlLabel
                                    value={EnableAutoStart.Disabled}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="OFF"
                                    sx={{ marginLeft: '20px' }}
                                />
                            </RadioGroup>
                        </Box>

                        <Box sx={{ display: 'flex', flexDirection: 'row', marginLeft: '20px', marginTop: '0px' }}>
                            <Typography component="div" sx={[itemStyle, { marginTop: '10px' }]}>
                                案内の基準
                            </Typography>
                            <RadioGroup
                                value={autoStartType}
                                onChange={handleAutoStartTypeChange}
                            >
                                <FormControlLabel
                                    value={AutoStartType.TimeSchedule}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="スタート予定時間(1-59)"

                                />
                                <div style={{ display: 'flex', height: '20px', marginTop: '0px', marginLeft: '30px', fontSize: '16px' }}>
                                    スタート予定時刻の
                                    <TextField
                                        type="number"
                                        value={startTimeSchedule}
                                        disabled={Number(autoStartType) === 1}
                                        error={dataError?.includes('startTimeSchedule')}
                                        onChange={handleStartTimeScheduleChange}
                                        style={{ width: '80px', fontSize: '14px', height: "30px", marginLeft: '10px', marginRight: '10px' }}
                                        inputProps={{
                                            inputMode: "numeric",
                                            pattern: "[0-9]*",
                                            min: 1,
                                            max: 59,
                                        }}
                                        InputProps={{
                                            style: { height: "30px" },
                                        }}
                                        size="small"
                                    ></TextField>
                                    分前のカート番号をスタートホールへ案内する
                                </div>
                                <FormControlLabel
                                    value={AutoStartType.StartNumber}
                                    control={<Radio sx={{
                                        color: '#9e9e9e',
                                        '&.Mui-checked': {
                                            color: '#FF8000',
                                        },
                                    }} />}
                                    label="スタートまでの順番(1-9)"
                                    sx={{ marginTop: '10px' }}
                                />
                                <div style={{ display: 'flex', height: '20px', marginTop: '0px', marginLeft: '30px', fontSize: '16px' }}>
                                    スタートの順番が
                                    <TextField
                                        type="number"
                                        value={startNumber}
                                        disabled={Number(autoStartType) !== 1}
                                        error={dataError?.includes('startNumber')}
                                        onChange={handleStartNumberChange}
                                        style={{ width: '80px', fontSize: '14px', height: "30px", marginLeft: '10px', marginRight: '10px' }}
                                        inputProps={{
                                            inputMode: "numeric",
                                            pattern: "[0-9]*",
                                            min: 1,
                                            max: 9,
                                        }}
                                        InputProps={{
                                            style: { height: "30px" },
                                        }}
                                        size="small"
                                    ></TextField>
                                    番目の組のカート番号をスタートホールへ案内する
                                </div>
                            </RadioGroup>
                        </Box>


                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginLeft: '20px', marginTop: '20px' }}>
                            <Typography component="div" sx={itemStyle}>
                                サブ文言設定<br/>(45文字まで)
                            </Typography>
                            <TextField
                                value={subTextAuto}
                                error={dataError?.includes('subTextAuto')}
                                onChange={handleSubTextAutoChange}
                                style={{ width: '644px', fontSize: '14px', height: "30px", marginRight: '20px' }}
                                inputProps={{ maxLength: 45 }}
                                InputProps={{
                                    inputMode: "numeric",
                                    style: { height: "30px" },
                                }}
                                size="small"
                            ></TextField>
                            <Button variant="contained"
                                onClick={clickPreviewAutoText}>
                                プレビュー
                            </Button>
                        </Box>

                        <div style={{ display: 'flex', height: '30px', marginTop: '30px', marginLeft: '60px', fontSize: '16px', fontWeight: 'bold' }}>
                            自動スタート案内レビュー（モニター下部）
                        </div>
                        <Box sx={{ display: 'flex', flexDirection: 'row', backgroundColor: 'black', marginLeft: "60px", marginTop: "0px", marginBottom: "30px", marginRight: "120px", alignItems: "center" }}>
                            <InfoIcon style={{ color: 'yellow', marginLeft: '30px', width: '60px', height: '60px' }} />
                            <Box sx={{ display: 'flex', flexDirection: 'column', backgroundColor: 'black', marginLeft: "10px", marginTop: "0px", marginBottom: "0px", marginRight: "30px" }}>
                                <div style={{ display: 'flex', height: '30px', marginTop: '15px', marginLeft: '0px', fontSize: '16px', fontWeight: 'bold', color: 'yellow', alignItems: 'center' }}>
                                    {previewMainTextAuto}
                                </div>
                                <div style={{ display: 'flex', height: '20px', marginBottom: '15px', marginLeft: '0px', fontSize: '12px', fontWeight: 'bold', color: 'white', alignItems: 'center' }}>
                                    {previewSubTextAuto}
                                </div>
                            </Box>
                        </Box>
                    </Box>
                </Box>

                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        marginTop: '10px',
                    }}
                >
                    <Button variant="contained" sx={{
                        marginBottom: '60px'
                    }}
                        onClick={checkStartGuideSettingsData}>
                        保存
                    </Button>
                </Box>
                <ApiDialog
                    openApiDialog={openApiDialog}
                    handleCloseErrorDialog={handleCloseErrorDialog}
                    succeed={apiSucceed}
                    apiType={"startguidesettings"}>
                </ApiDialog>
            </Box>
        </LocalizationProvider>
    );
}

export default Home;
