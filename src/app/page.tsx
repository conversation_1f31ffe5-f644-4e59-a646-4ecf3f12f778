"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { handleAuthToken } from "@/auth/Auth"
import ErrorHint from "@/components/error-hint";
import { generateClient } from "aws-amplify/data";
import type { Schema } from "@/../amplify/data/resource";
import { Amplify } from "aws-amplify"
import outputs from "@/../amplify_outputs.json";
import { record, flushEvents } from 'aws-amplify/analytics';

Amplify.configure(outputs)
const Home = () => {
  const router = useRouter();

  const [loginSuccess, isLoginSuccess] = useState(true);

  const client = generateClient<Schema>();

  // function sendMessage(content: string) {
  //   client.mutations.createMessageSchedule({
  //     title: 'My_message',
  //     message: content,
  //     // send 1 minute from now, time format: yyyy-mm-ddThh:mm:ss
  //     deliveryDate: new Date(Date.now() + 60000).toISOString().toString().slice(0, -5),
  //     timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  //   });
  // };

  useEffect(() => {
    // record({
    //   name: 'Page visit',
    //   attributes: { name: 'info', message: 'Main page.' },
    // });
    // flushEvents();

    handleAuthToken().then(
      (value) => {
        isLoginSuccess(value)
        if (value) {
          // Next.jsがクライアントサイドでマウントされた後にリダイレクトを行います。
          localStorage.getItem('menu_type') === "online_compe" ? router.replace("/onlinecompe/registration") 
            : localStorage.getItem('menu_type') === "start_guide" ? router.replace("/startguide/settings")
            : localStorage.getItem('menu_type') === "office_settings" ? router.replace("/office/settings")
            : router.replace("/answerlist");
        }
        // else {
        //   const login = process.env.BASE_URL?.replace("api/v2/web", "") + "ops/login";
        //   window.location.href = login;
        // }
      },
    );
  }, [router]);

  return (!loginSuccess && <ErrorHint onButtonClick={() => { }} screenWidth={undefined} isAll={undefined} isNotLogin={true} jumpSetting={undefined} />);
};



export default Home;
