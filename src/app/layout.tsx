"use client";

import React, { useEffect, useState } from "react";
import packageInfo from "../../package.json";
import {
  AppBar,
  Box,
  Collapse,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  Toolbar,
  Typography,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import MenuIcon from "@mui/icons-material/Menu";
import HomeIcon from "@mui/icons-material/Home";
import AssessmentIcon from "@mui/icons-material/Assessment";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import Link from "next/link";
import { usePathname } from "next/navigation";
import "../styles/globals.css";
import Popover from "@mui/material/Popover";
import { logout } from "../api/auth-api";
import { getQuesnaireSettings } from "../api/caddy-api";
import { CommonUtils } from '@/utils/common-utils';

const drawerWidth = 220;
const drawerMinWidth = 48;

const defaultMenuItems = [
  {
    text: "ホーム",
    // href: "/",
    icon: <HomeIcon />,
    onClick: () => {
      const BaseUrl = process.env.BASE_URL;
      window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops";
    },
  },
  {
    text: "アンケート",
    icon: <AssessmentIcon />,
    children: [
      { text: "アンケート回答一覧", href: "/answerlist" },
      { text: "アンケート結果", href: "/answer" },
      { text: CommonUtils.getCaddyNameFromLocalStorage() + "評価", href: "/caddy" },
      { text: "各種設定", href: "/settings" },
    ],
  },
];
const onlineCompeMenuItems = [
  {
    text: "ホーム",
    // href: "/",
    icon: <HomeIcon />,
    onClick: () => {
      const BaseUrl = process.env.BASE_URL;
      window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops";
    },
  },
  {
    text: "コンペ設定",
    icon: <AssessmentIcon />,
    children: [
      { text: "新規コンペ登録", href: "/onlinecompe/registration" },
      { text: "コンペ一覧", href: "/onlinecompe/list" },
      { text: "基本設定", href: "/onlinecompe/basicsetting" },
    ],
  },
  {
    text: "ティーシート",
    icon: <AssessmentIcon />,
    children: [{ text: "ティーシート", href: "/onlinecompe/teesheet" }],
  },
];

const startGuideMenuItems = [
  {
    text: "ホーム",
    // href: "/",
    icon: <HomeIcon />,
    onClick: () => {
      const BaseUrl = process.env.BASE_URL;
      window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops";
    },
  },
  {
    text: "スタート案内",
    icon: <AssessmentIcon />,
    children: [
      { text: "設定", href: "/startguide/settings" },
    ],
  },
];

const officeSettingsMenuItems = [
  {
    text: "ホーム",
    // href: "/",
    icon: <HomeIcon />,
    onClick: () => {
      const BaseUrl = process.env.BASE_URL;
      window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops";
    },
  },
  {
    text: "システム設定",
    icon: <AssessmentIcon />,
    children: [
      { text: "設定", href: "/office/settings" },
    ],
  },
];

const theme = createTheme({
  palette: {
    primary: {
      main: "#304A89",
    },
  },
  spacing: 8,
  typography: {
    //font
    fontFamily: `"メイリオ", "Meiryo", "ＭＳ ゴシック", "Osaka", "Source Sans Pro", sans-serif`,

    h1: { fontSize: 20 },
    h2: { fontSize: 18 },
    h3: { fontSize: 16 },
    h4: { fontSize: 14 },
    h5: { fontSize: 12 },
    h6: { fontSize: 10 },
  },
  components: {
    MuiToolbar: {
      styleOverrides: {
        root: {
          minHeight: 56,
          "@media (min-width:600px)": {
            minHeight: 56,
          },
        },
      },
    },
  },
});

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathName = usePathname() || "/";
  const showMenu =
    !pathName.startsWith("/project") &&
    pathName !== "/onlinecompe/list/detail/print" &&
    pathName !== "/appwebview/startguide";
  const [open, setOpen] = useState(true);
  const [openSubMenu, setOpenSubMenu] = useState(true);
  const [widthSize, setWidthSize] = useState(true);

  const getVersionMsg = () => {
    const baseUrl = process.env.BASE_URL || "";
    const version = process.env.VERSION || "";
    const versionName = packageInfo.version || "";
    const versionCode = packageInfo.versionCode || "";

    if (baseUrl.includes("dev.marshal-i.com")) {
      if (!version) return "";
      const match = version.match(/Commit:\s+(\S+)/);
      const commitId = match ? match[1] : "";
      return commitId ? `APPバージョン: ${commitId}` : "";
    }

    if (!versionName) return "";
    if (!versionCode) return `APPバージョン： ${versionName}`;
    return `APPバージョン： ${versionName} (${versionCode})`;
  };

  const versionMsg = getVersionMsg();

  const handleDrawerToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  useEffect(() => {
    window.postMessage({ type: "UPDATE_OPEN", open }, "*");
  }, [open]);

  const handleSubMenuToggle = () => {
    setOpenSubMenu((prevOpenSubMenu) => !prevOpenSubMenu);
  };
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 910) {
        setOpen(false);
        setWidthSize(false);
      } else {
        setOpen(true);
        setWidthSize(true);
      }
    };

    handleResize(); // Initialize the state based on the current window width
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const [username, setUsername] = useState<string | null>(null);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === "UPDATE_USERNAME") {
        setUsername(event.data.username);
      }
    };

    window.addEventListener("message", handleMessage);

    const storedUsername = localStorage.getItem("username");
    setUsername(storedUsername || "");

    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    const logoutStatus = await logout();
    handleClose();
  };

  const handleProfileChange = async () => {
    const BaseUrl = process.env.BASE_URL;
    window.location.href =
      BaseUrl?.replace("api/v2/web", "") + "ops/changeprofile";
    handleClose();
  };

  const openPop = Boolean(anchorEl);

  const [menuItems, setMenuItems] = useState<any[]>([]);

  const [titleName, setTitleName] = useState<string>("");

  useEffect(() => {
    // get menu type from url param menu_type
    const urlParams = new URLSearchParams(window.location.search);
    const menuType = urlParams.get("menu_type");
    if (menuType) {
      localStorage.setItem("menu_type", menuType);
    }

    const menuItems =
      localStorage.getItem("menu_type") === "online_compe" ? onlineCompeMenuItems
        : localStorage.getItem("menu_type") === "start_guide" ? startGuideMenuItems 
        : localStorage.getItem("menu_type") === "office_settings" ? officeSettingsMenuItems
        : defaultMenuItems;
    console.log(menuItems);
    setMenuItems(menuItems);

    const titleName = localStorage.getItem("menu_type") === "online_compe" ? "オンラインコンペ" 
        : localStorage.getItem("menu_type") === "start_guide" ? "スタート案内" 
        : localStorage.getItem("menu_type") === "office_settings" ? "システム設定"
        : CommonUtils.getCaddyNameFromLocalStorage() + "評価";
        
    setTitleName(titleName);

  }, []);

  // アンケート設定を更新する
  const fetchQuesnaireSettings = async () => {
    try {
      const response = await getQuesnaireSettings();
      if (response) {
        const caddyNameType = response.caddy_name_type;
        console.log(caddyNameType);
        localStorage.setItem("caddy_name_type", caddyNameType + '');
      }
    } catch (error) {
      console.error("Error fetching QuesnaireSettings:", error);
    }
  };
  useEffect(() => {
    fetchQuesnaireSettings();
  }, []);

  return (
    <html lang="en">
      <head>
        <title>{titleName}</title>
      </head>
      <body>
        <ThemeProvider theme={theme}>
          {showMenu && (
            <Box sx={{ display: "flex" }}>
              <CssBaseline />
              <AppBar
                position="fixed"
                color="primary"
                sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}
              >
                <Toolbar>
                  <Typography variant="h1" noWrap component="div">
                    マーシャルアイクラウド
                  </Typography>
                  {username && (
                    <Box
                      sx={{
                        height: "100%",
                        ml: "auto",
                        display: "flex",
                        alignItems: "center",
                        backgroundColor: "transparent",
                        transition: "background-color 0.3s",
                        "&:hover": {
                          backgroundColor: "#728eda",
                        },
                        borderRadius: "0px",
                        padding: "10px",
                        cursor: "pointer",
                      }}
                      onClick={handleClick}
                    >
                      <Typography variant="h3">
                        {username ? `${username}` : ""}
                      </Typography>
                      <ArrowDropDownIcon />
                    </Box>
                  )}
                </Toolbar>
              </AppBar>
              <Drawer
                variant="permanent"
                sx={{
                  width: open ? drawerWidth : drawerMinWidth,
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  boxSizing: "border-box",
                  display: "flex",
                  flexDirection: "column", // Ensure the Drawer layout is columnar
                  "& .MuiDrawer-paper": {
                    width: open ? drawerWidth : drawerMinWidth,
                    transition: "width 0.3s",
                    overflowX: "hidden",
                    display: "flex",
                    flexDirection: "column", // Ensure the Drawer content is in column direction
                  },
                }}
              >
                <Toolbar />
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: open ? "flex-end" : "center",
                    padding: "4px",
                  }}
                >
                  <IconButton onClick={handleDrawerToggle}>
                    {open ? <ChevronLeftIcon /> : <MenuIcon />}
                  </IconButton>
                </Box>
                <List sx={{ flexGrow: 1 }}>
                  {menuItems.map((item, index) => (
                    <div key={index}>
                      <ListItem
                        button
                        onClick={
                          item.onClick
                            ? item.onClick
                            : item.children
                              ? handleSubMenuToggle
                              : undefined
                        }
                        component={item.href ? Link : "div"}
                        href={item.href}
                        sx={{
                          justifyContent: open ? "initial" : "center",
                          px: 2.5,
                          "&:hover": {
                            backgroundColor: "rgba(0, 0, 0, 0.04)",
                          },
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 0,
                            mr: open ? 3 : "auto",
                            justifyContent: "center",
                          }}
                        >
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText
                          primaryTypographyProps={{
                            fontSize: "16px",
                            color: "primary",
                          }}
                          primary={item.text}
                          sx={{ opacity: open ? 1 : 0, whiteSpace: "nowrap" }}
                        />
                      </ListItem>
                      {item.children && (
                        <Collapse
                          in={openSubMenu && open}
                          timeout="auto"
                          unmountOnExit
                        >
                          <List component="div" disablePadding>
                            {item.children.map(
                              (
                                subItem: { href: string; text: string },
                                subIndex: number
                              ) => (
                                <ListItem
                                  button
                                  component={Link}
                                  href={subItem.href}
                                  key={subIndex}
                                  sx={{ pl: 4 }}
                                >
                                  <ListItemText
                                    primaryTypographyProps={{
                                      fontSize: "14px",
                                      color: "primary",
                                    }}
                                    primary={subItem.text}
                                    sx={{ marginLeft: 4 }}
                                  />
                                </ListItem>
                              )
                            )}
                          </List>
                        </Collapse>
                      )}
                    </div>
                  ))}
                </List>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    padding: "8px",
                  }}
                >
                  {open && versionMsg.length > 0 && (
                    <ListItemText
                      primaryTypographyProps={{
                        fontSize: "14px",
                      }}
                      primary={`${versionMsg}`}
                      sx={{
                        textAlign: "center",
                        whiteSpace: "normal",
                        overflowWrap: "break-word",
                      }}
                    />
                  )}
                </Box>
              </Drawer>

              <Box
                component="main"
                height="100vh"
                display="flex"
                flexDirection="column"
                sx={{
                  flexGrow: 1,
                  backgroundColor: "#F1F3F9",
                  p: widthSize ? 3 : 2,
                  transition: "0.3s",
                }}
              >
                <Toolbar />
                {children}
              </Box>
              <Popover
                open={openPop}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "center",
                }}
                transformOrigin={{
                  vertical: "top",
                  horizontal: "center",
                }}
              >
                <List sx={{ p: 1 }}>
                  <Stack direction="row" spacing={2}>
                    <ListItemButton
                      sx={{ height: "100%" }}
                      onClick={handleProfileChange}
                    >
                      <ListItemText
                        primaryTypographyProps={{
                          fontSize: "16px",
                          color: "primary",
                        }}
                        primary="プロファイル変更"
                      />
                    </ListItemButton>
                    <ListItemButton
                      sx={{ height: "100%" }}
                      onClick={handleLogout}
                    >
                      <ListItemText
                        primaryTypographyProps={{
                          fontSize: "16px",
                          color: "primary",
                        }}
                        primary="ログアウト"
                      />
                    </ListItemButton>
                  </Stack>
                </List>
              </Popover>
            </Box>
          )}
          {!showMenu && children}
        </ThemeProvider>
      </body>
    </html>
  );
}
