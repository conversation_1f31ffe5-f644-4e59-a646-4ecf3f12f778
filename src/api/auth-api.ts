import axios, { AxiosResponse } from "axios";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "../../amplify/data/resource";


// const BaseUrl = "http://192.168.31.79:888/api/web";
// const BaseUrl='https://dev.marshal-i.com/api/v2/web'
export const BaseUrl = process.env.BASE_URL;
export const client = generateClient<Schema>();


export const handleError = (response: any) => {
  throw response;
}

const errorMessages: { [key: number]: string } = {
  400: "Bad Request, param error",
  404: "Not Found",
  500: "Internal Server Error",
  601: "MySQL connection error",
  602: "MySQL SQL error",
};


export const handleHttpError = (error: any, method: string, path: string,) => {
  // console.log("errorMessage",error)
  if (error.status === 401) {
    window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops/login";
    return
  }
  let errorMessage = "";
  if (error) {
    if (typeof error.data?.message === "string" && error.data.message) {
      errorMessage = error.data.message;
    } else if (typeof error.data === "string" && error.data) {
      errorMessage = error.data;
    } else if (typeof error.message === "string" && error.message) {
      errorMessage = error.message;
      error.status = 500;
    } else {
      errorMessage = errorMessages[error.status] || "An unexpected error occurred";
    }
  }
  // console.log("errorMessage",error.status)
  // console.log("errorMessage",errorMessage)
  const officeId = localStorage.getItem('office_id') || '';

  let logStr;

  if (!BaseUrl || BaseUrl === "") {
    logStr = generateErrorLog("FATAL",
      method,
      path,
      error.status,
      "環境変数 BASE_URL が設定されていません。",
      "EvaluationModule",
      officeId);
  } else {
    logStr = generateErrorLog("WARN",
      method,
      path,
      error.status,
      errorMessage,
      "EvaluationModule",
      officeId);
  }

  console.log("logStr", logStr)

  client.queries.sendCloudWatch({
    content: logStr
  });
  throw new Error(errorMessage);
};



/**
 * ------------------ auth related api start ------------------
*/

const AuthUrl = BaseUrl?.replace("/web", "");

export const accesssTokenResp = async (authToken: string) => {
  const response = await axios.post(
    `${AuthUrl}/access`,
    {
      auth_token: authToken,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  return response.data;
}

export const refreshTokenResp = async (refreshToken: string) => {

  const response = await axios.post(
    `${AuthUrl}/refresh`,
    {
      refresh_token: refreshToken,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  return response.data;
}


const lock = async (fn: () => any) => {
  await navigator.locks.request('global_lock', async () => {
    await fn();
  });
};


export async function reqWithRefreshToken(axiosCallBack: () => Promise<AxiosResponse>): Promise<AxiosResponse> {
  try {
    return await axiosCallBack()
  } catch (e1) {
    if (axios.isAxiosError(e1) && e1.response?.status === 401) {
      try {
        await lock(async () => {
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            let resp = await refreshTokenResp(refreshToken)
            if (resp.access_token && resp.refresh_token) {
              localStorage.setItem('access_token', resp.access_token);
              localStorage.setItem('refresh_token', resp.refresh_token);
            }
          }
        });
        return await axiosCallBack()
      } catch (e2) {
        if (axios.isAxiosError(e2) && e2.response) {
          const login = BaseUrl?.replace("api/v2/web", "") + "ops/login";
          window.location.href = login;
        } else {
          throw e2
        }
      }
    }

    if (axios.isAxiosError(e1) && e1.response) {
      return e1.response
    } else {
      throw e1
    }
  }
}

export const logout = async (): Promise<any> => {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    const accessToken = localStorage.getItem('access_token');
    const response = await axios.post(
      `${AuthUrl}/logout`,
      {
        refresh_token: refreshToken,
        access_token: accessToken,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      }
    );
    if (response.status === 200 && response.data.code === 200) {
      localStorage.removeItem('last_auth_token');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('username');
      localStorage.removeItem('office_id');
      window.location.href = BaseUrl?.replace("api/v2/web", "") + "ops/login";
      return true
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};


/**
 * ------------------ auth related api end ------------------
*/



export function generateErrorLog(
  level: string,
  method: string,
  path: string,
  statusCode: number,
  error: string,
  module: string,
  officeId: string
): string {
  // const timestamp = new Date().toISOString();
  const timestamp = new Date().toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' });

  const log = {
    timestamp: timestamp,
    level: level,
    category: "Network",
    message: error,
    office_id: officeId,
    office_key: "",
    module: module,
    stack_trace: "",
    param: {
      req: {
        method: method,
        path: path
      },
      res: {
        status_code: statusCode,
        error: error
      }
    }
  };

  return JSON.stringify(log, null, 4);
}