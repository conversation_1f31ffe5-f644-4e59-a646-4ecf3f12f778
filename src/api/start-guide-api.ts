
import axios from "axios";
import { BaseUrl, handleError, handleHttpError, reqWithRefreshToken } from "./auth-api";
import { StartGuideSettings } from "@/models/startguide/resp/start-guide-setting";


export const updateStartGuideSettings = async (
    startGuideSettings: StartGuideSettings
): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/start-guidance/settings`,
            startGuideSettings,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/start-guidance/settings")
        return false;
    }
};

export const getStartGuideSettings = async (): Promise<StartGuideSettings> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/start-guidance/settings`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data as StartGuideSettings;
        } else if (response.status === 204) {
            return response.data.data as StartGuideSettings;
        } else {
            handleError(response);
            return {} as StartGuideSettings;
        }
    } catch (error) {
        handleHttpError(error, "GET", "/start-guidance/settings")
        return {} as StartGuideSettings;
    }
}
