
import { OnlineCompe } from "@/models/compe/req/online-compe-creation";
import { OnlineCompeDefaultSetting } from "@/models/compe/req/online-compe-default-setting";
import { OnlineCompeLatestNo, UploadImgResp } from "@/models/compe/resp/online-compe-creation";
import { LeaderBoardRankingResp, RankingTypeResp } from "@/models/compe/resp/ranking-resp";
import { JoinedPlayer, OnlineCompeJoinedPlayersReq, OnlineCompePlayerJoinReq, OnlineCompeJoinedPlayerHdcpReq } from "@/models/compe/req/online-compe-player";
import { OnlineCompeOfficeResp } from "@/models/compe/resp/online-compe-office";
import { TeeSheetResp } from "@/models/tee/resp/tee-sheet";
import { PairingResp } from "@/models/compe/resp/pairing-resp";
import { TeeInfoResp } from "@/models/tee/resp/tee-info";
import { CoursesResp } from "@/models/course/resp/course-resp";
import { PlayerInfoResp } from "@/models/compe/resp/player-resp";
import { OnlineCompeJoinedPlayersResp } from "@/models/compe/resp/online-compe-player";
import { TeePlayer, TeePlayerResp } from "@/models/tee/resp/tee-player";
import axios from "axios";
import { BaseUrl, handleError, handleHttpError, reqWithRefreshToken } from "./auth-api";


/**
 * ------------------ online-compe related api start ------------------
*/


export const createOnlineCompe = async (
    onlineCompe: OnlineCompe
): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.put(
            `${BaseUrl}/online-compe/create`,
            onlineCompe,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/question")
        return false;
    }
};

export const updateOnlineCompe = async (onlineCompe: OnlineCompe): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/update`,
            onlineCompe,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true
        } else {
            handleError(response);
            return false
        }

    } catch (error) {
        return false
    }
}


export const getOnlineCompe = async (compeNo: number): Promise<OnlineCompe> => {

    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/${compeNo}`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data as OnlineCompe;
        } else {
            handleError(response);
            return {} as OnlineCompe;
        }

    } catch (error) {
        handleHttpError(error, "GET", `/online-compe/${compeNo}`)
        return {} as OnlineCompe;
    }
}


export const updateDefaultSetting = async (
    onlineCompeDefaultSetting: OnlineCompeDefaultSetting
): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/default-setting/update`,
            onlineCompeDefaultSetting,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/question")
        return false;
    }
};

export const getDefaultSetting = async (): Promise<OnlineCompeDefaultSetting> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/default-setting`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data as OnlineCompeDefaultSetting;
        } else {
            handleError(response);
            return {} as OnlineCompeDefaultSetting;
        }
    } catch (error) {
        handleHttpError(error, "GET", "/online-compe/default-setting")
        return {} as OnlineCompeDefaultSetting;
    }
}

export const getOnlineCompeLatestNo = async (): Promise<OnlineCompeLatestNo> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/latest-no`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data as OnlineCompeLatestNo;
        } else {
            handleError(response);
            return { compe_no: -1 } as OnlineCompeLatestNo;
        }

    } catch (error) {
        handleHttpError(error, "GET", "/online-compe/latest-no")
        return { compe_no: -1 } as OnlineCompeLatestNo;
    }
}


export const uploadImg = async (
    uploadImg: FormData,
    compeNo: number
): Promise<UploadImgResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/img/upload/${compeNo}`,
            uploadImg,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as UploadImgResp;
        } else {
            handleError(response);
            return {} as UploadImgResp;
        }

    } catch (error) {
        return {} as UploadImgResp;

    }
}

export const getCourses = async (): Promise<CoursesResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/course/list`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as CoursesResp;
        } else {
            handleError(response);
            return {} as CoursesResp;
        }
    } catch (error) {
        handleHttpError(error, "GET", "/online-compe/course-list")
        return {} as CoursesResp;
    }
}

export const searchPlayerInfo = async (player_name: string, birthday: string, glid_no: string | null, hdcp_date: string | null, search_type: number): Promise<PlayerInfoResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/player-info/search`,
            {
                params: {
                    player_name,
                    birthday,
                    glid_no,
                    hdcp_date, // YYYY-MM-DD
                    search_type,
                },
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as PlayerInfoResp;
        } else {
            handleError(response);
            return {} as PlayerInfoResp;
        }
    } catch (error) {
        return {} as PlayerInfoResp;
    }
}


export const joinOnlineCompe = async (req: OnlineCompePlayerJoinReq): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/join`, req,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true
        } else {
            handleError(response);
            return false
        }

    } catch (error) {
        return false
    }
}

export const listJoinedPlayers = async (compeNo: number): Promise<OnlineCompeJoinedPlayersResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/compe-player/${compeNo}/list`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as OnlineCompeJoinedPlayersResp;
        } else {
            handleError(response);
            return {} as OnlineCompeJoinedPlayersResp;
        }

    } catch (error) {
        return {} as OnlineCompeJoinedPlayersResp;
    }
}


export const updateJoinedPlayerHdcp = async (req: OnlineCompeJoinedPlayerHdcpReq): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/tee/sheet/player/update`, req,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true
        } else {
            handleError(response);
            return false
        }

    } catch (error) {
        return false
    }
}

export const updateJoinedPlayers = async (compeNo: number, req: OnlineCompeJoinedPlayersReq): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/compe-player/${compeNo}/update`, req,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true
        } else {
            handleError(response);
            return false
        }

    } catch (error) {
        return false
    }
}


export const listOfficeCompe = async (req: OnlineCompeOfficeReq): Promise<OnlineCompeOfficeResp> => {
    try {

        //OnlineCompeOfficeReq to query
        const params = new URLSearchParams();
        for (const key in req) {
            const value = req[key as keyof OnlineCompeOfficeReq]
            if (value !== null && value !== undefined) {
                params.append(key, value.toString());
            } else {
                continue
            }
        }
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/office-compe/list?${params}`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as OnlineCompeOfficeResp;
        } else {
            handleError(response);
            return {} as OnlineCompeOfficeResp;
        }

    } catch (error) {
        return {} as OnlineCompeOfficeResp;
    }
}

export const getTeeInfos = async (): Promise<TeeInfoResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/tee/info`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as TeeInfoResp;
        } else {
            handleError(response);
            return {} as TeeInfoResp;
        }
    } catch (error) {
        return {} as TeeInfoResp;
    }
}

export const teeSheet = async (dateStr: string | null): Promise<TeeSheetResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/tee/sheet`,
            {
                params: {
                    date_str: dateStr
                },
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as TeeSheetResp;
        } else {
            handleError(response);
            return {} as TeeSheetResp;
        }

    } catch (error) {
        return {} as TeeSheetResp
    }
}

export const searchPlayersFromTeeSheet = async (player_name: string | null, birthday: string | null, play_date: string | null): Promise<TeePlayerResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/tee/sheet/player/search`,
            {
                params: {
                    player_name, birthday, play_date
                },
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as TeePlayerResp;
        } else {
            handleError(response);
            return {} as TeePlayerResp;
        }

    } catch (error) {
        return {} as TeePlayerResp
    }
}

export const leaderboardRanking = async (
    compeNo: string,
    aggregation_type: string
): Promise<LeaderBoardRankingResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/leaderboard/ranking/${compeNo}`,
            {
                params: { aggregation_type },
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as LeaderBoardRankingResp;
        } else {
            handleError(response);
            return {} as LeaderBoardRankingResp;
        }

    } catch (error) {
        return {} as LeaderBoardRankingResp;
    }
}

export const leaderboardRankingShared = async (
    share_key: string,
    aggregation_type: string,
    compe_no: string
): Promise<LeaderBoardRankingResp> => {
    try {
        const response = await axios.get(
            `${BaseUrl}/online-compe/leaderboard/ranking/shared/${share_key}?aggregation_type=${aggregation_type}&compe_no=${compe_no}`,
            {
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );
        if (response.status === 200 && response.data.code === 200) {
            return response.data as LeaderBoardRankingResp;
        } else {
            handleError(response);
            return {} as LeaderBoardRankingResp;
        }

    } catch (error) {
        return {} as LeaderBoardRankingResp;
    }
}

export const rankingType = async (compeNo: string): Promise<RankingTypeResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/leaderboard/ranking/type/${compeNo}`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as RankingTypeResp
        } else {
            handleError(response);
            return {} as RankingTypeResp;
        }

    } catch (error) {
        return {} as RankingTypeResp;
    }
}

export const rankingTypeShared = async (share_key: string, compe_no: string): Promise<RankingTypeResp> => {
    try {
        const response = await axios.get(
            `${BaseUrl}/online-compe/leaderboard/ranking/type/shared/${share_key}?compe_no=${compe_no}`,
            {
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );
        if (response.status === 200 && response.data.code === 200) {
            return response.data as RankingTypeResp
        } else {
            handleError(response);
            return {} as RankingTypeResp;
        }

    } catch (error) {
        return {} as RankingTypeResp;
    }
}

export const createSharedKey = async (compe_no: number): Promise<string> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/online-compe/leaderboard/ranking/share-key/create`, {
            compe_no,
        },
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data.share_key as string;
        } else {
            handleError(response);
            return "";
        }
    } catch (error) {
        return "";
    }
}

export const shareByEmail = async (email: string, compe_no: number, share_key: string): Promise<boolean> => {
    try {
        const response = await axios.post(
            `${BaseUrl}/online-compe/leaderboard/ranking/share`, {
            email,
            compe_no,
            share_key,
        },
            {
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }

    } catch (error) {
        return false;
    }
}

export const pairing = async (compeNo: number): Promise<PairingResp> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/online-compe/pairing/${compeNo}`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data as PairingResp;
        } else {
            handleError(response);
            return {} as PairingResp;
        }

    } catch (error) {
        return {} as PairingResp;
    }
}

/**
 * ------------------ online-compe related api end ------------------
*/