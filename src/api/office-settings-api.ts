
import axios from "axios";
import { BaseUrl, handleError, handleHttpError, reqWithRefreshToken } from "./auth-api";
import { OfficeSettings } from "@/models/officesettings/resp/office-settings";


export const updateOfficeSettings = async (
    officeSettings: OfficeSettings
): Promise<boolean> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/office/settings`,
            officeSettings,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/office/settings")
        return false;
    }
};

export const getOfficeSettings = async (): Promise<OfficeSettings> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(
            `${BaseUrl}/office/settings`,
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data as OfficeSettings;
        } else if (response.status === 204) {
            return response.data.data as OfficeSettings;
        } else {
            handleError(response);
            return {} as OfficeSettings;
        }
    } catch (error) {
        handleHttpError(error, "GET", "/office/settings")
        return {} as OfficeSettings;
    }
}
