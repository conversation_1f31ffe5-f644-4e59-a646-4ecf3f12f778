import { Caddy, Evaluation, Question, Questionnaire, StatisticalData, SurveyData, QuesnaireSettings, } from "@/models/caddy-models";
import { BaseUrl, handleError, handleHttpError, reqWithRefreshToken } from "./auth-api";
import axios from "axios";


/**
 * ------------------ caddy related api start ------------------
*/


export const fetchRows = async (
    page: number,
    limit: number,
    startDate: string,
    endDate: string,
    caddie: string,
    weekday: string
): Promise<Questionnaire | undefined> => {
    try {
        let lowestUrl = `${BaseUrl}/answer/lowest?start_date=${startDate}&end_date=${endDate}`;
        if (caddie.length > 0 && caddie !== "none" && caddie !== "0" && caddie !== "NaN") {
            lowestUrl += `&caddy_id=${caddie}`;
        }
        if (weekday.length > 0) {
            lowestUrl += `&weekday=${weekday}`;
        }

        let lowestResponse;
        try {
            const response = await reqWithRefreshToken(
                () => axios.get(lowestUrl, {
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                    },
                })
            );
            if (response.status === 200 && response.data.code === 200) {
                lowestResponse = response.data;
            } else {
                lowestResponse = { data: [] };
            }
        } catch (error) {
            lowestResponse = { data: [] };
        }

        let url = `${BaseUrl}/questionnaire?page=${page}&limit=${limit}`;
        if (startDate.length > 0) {
            url += `&start_date=${startDate}`;
        }
        if (endDate.length > 0) {
            url += `&end_date=${endDate}`;
        }
        if (caddie.length > 0 && caddie !== "none" && caddie !== "0" && caddie !== "NaN") {
            url += `&caddy_id=${caddie}`;
        }
        if (weekday.length > 0) {
            url += `&weekday=${weekday}`;
        }

        const response = await reqWithRefreshToken(
            () => axios.get(url, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                }
            })
        );

        if (response.status === 200 && response.data.code === 200) {
            response.data.lowest = lowestResponse.data
            return response.data;
        } else if (response.status === 204) {
            return response.data;
        } else {
            handleError(response);
        }
    } catch (error) {
        handleHttpError(error, "GET", "/questionnaire");
    }
};


export const getCsvUrlAndDownload = async (
    startDate: string,
    endDate: string,
    caddie: string,
    weekday: string
): Promise<void> => {
    try {
        const params = new URLSearchParams();
        if (startDate.length > 0) {
            params.append(`start_date`, `${startDate}`);
        }
        if (endDate.length > 0) {
            params.append(`end_date`, `${endDate}`);
        }
        if (caddie.length > 0 && caddie !== "none" && caddie !== "0" && caddie !== "NaN") {
            params.append(`caddy_id`, `${caddie}`);
        }
        if (weekday.length > 0) {
            params.append(`weekday`, `${weekday}`);
        }
        let url = `${BaseUrl}/questionnaire/csv?${params}`;

        const response = await reqWithRefreshToken(
            () => axios.get(url, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                    'Content-Type': 'application/octet-stream'
                }
            })
        );

        if (response.status === 200) {
            const bom = new Uint8Array([0xEF, 0xBB, 0xBF]);
            const csvData = new Blob([bom, response.data], { type: 'text/csv;charset=utf-8' });
            const csvUrl = URL.createObjectURL(csvData);

            const link = document.createElement('a');
            link.href = csvUrl;
            link.download = 'アンケート回答一覧.csv';
            document.body.appendChild(link);
            link.click();

            document.body.removeChild(link);
            URL.revokeObjectURL(csvUrl);
        } else {
            handleError(response)
        }
    } catch (error) {
        handleHttpError(error, "GET", "/questionnaire/csv");
    }
};

export const fetchCaddies = async (): Promise<Caddy[] | undefined> => {

    try {
        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/caddylist`, {
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error) {
        handleHttpError(error, "GET", "/caddylist")
    }
};

export const getQuestion = async (): Promise<Question[] | undefined> => {

    try {
        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/question`, {
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error) {
        handleHttpError(error, "GET", "/question")
    }
};

export const postQuestion = async (
    content: any,
    require: number,
    type: any
) => {

    try {
        const response = await reqWithRefreshToken(() => axios.post(
            `${BaseUrl}/question`,
            {
                content: content,
                require: require,
                type: type,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/question")
    }
};

export const updateQuestion = async (
    id: number,
    content: string,
    require: number
): Promise<any> => {

    try {
        const response = await reqWithRefreshToken(() => axios.put(
            `${BaseUrl}/question/${id}`,
            {
                content: content,
                require: require,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "PUT", "/question")
    }
};

export const updateQuestionIndex = async (
    id: number,
    index: number
): Promise<any> => {

    try {
        const response = await reqWithRefreshToken(() => axios.put(
            `${BaseUrl}/questionindex`,
            {
                id: id,
                index: index,
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
                },
            }
        ));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "PUT", "/questionindex")
    }
};

export const deleteQuestion = async (id: number): Promise<boolean> => {

    try {
        const response = await reqWithRefreshToken(() => axios.delete(`${BaseUrl}/question/${id}`, {
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "DELETE", "/question")
        return false;
    }
};

export const getEvaluations = async (): Promise<Evaluation[] | undefined> => {

    try {
        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/evaluation`, {
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error: any) {
        handleHttpError(error, "GET", "/evaluation")
    }
};

export const updateEvaluations = async (
    evaluations: Evaluation[]
): Promise<any> => {

    try {
        const response = await reqWithRefreshToken(() => axios.put(`${BaseUrl}/evaluation`, evaluations, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "PUT", "/evaluation")
    }
};

export const getCaddyEvaluate = async (
    start_month: string,
    end_month: string,
    weekday: number,
    caddy_id?: string
): Promise<SurveyData[] | undefined> => {

    try {
        const params: { [key: string]: any } = {
            start_month,
            end_month,
            weekday,
        };

        if (caddy_id !== undefined && "none" !== caddy_id && "" !== caddy_id) {
            params.caddy_id = caddy_id;
        }

        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/caddy`, {
            params,
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));

        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error) {
        handleHttpError(error, "GET", "/caddy")
    }
};

export const getStatistical = async (
    start_month: string,
    end_month: string,
    weekday: number,
    caddy_id?: string
): Promise<StatisticalData[] | undefined> => {
    try {
        const params: { [key: string]: any } = {
            start_month,
            end_month,
            weekday,
        };

        if (caddy_id !== undefined && "none" !== caddy_id && "" !== caddy_id) {
            params.caddy_id = caddy_id;
        }

        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/statistical`, {
            params,
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));

        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error) {
        handleHttpError(error, "GET", "/statistical")
    }
};

export const getQuesnaireSettings = async (): Promise<QuesnaireSettings | undefined> => {
    try {
        const response = await reqWithRefreshToken(() => axios.get(`${BaseUrl}/quesnairesettings`, {
            headers: {
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return response.data.data;
        } else if (response.status === 204) {
            return response.data.data;
        } else {
            handleError(response);
        }
    } catch (error: any) {
        handleHttpError(error, "GET", "/quesnairesettings")
    }
};

export const updateQuesnaireSettings = async (
    quesnaireSettings: QuesnaireSettings
): Promise<any> => {
    try {
        const response = await reqWithRefreshToken(() => axios.post(`${BaseUrl}/quesnairesettings`, quesnaireSettings, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            },
        }));
        if (response.status === 200 && response.data.code === 200) {
            return true;
        } else {
            handleError(response);
            return false;
        }
    } catch (error) {
        handleHttpError(error, "POST", "/quesnairesettings")
    }
};