# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/configuration-reference

# For a detailed guide to building and testing with Node, read the docs:
# https://circleci.com/docs/language-javascript/ for more details
version: 2.1

# Orbs are reusable packages of CircleCI configuration that you may share across projects, enabling you to create encapsulated, parameterized commands, jobs, and executors that can be used across multiple projects.
# See: https://circleci.com/docs/orb-intro/
orbs:
  # See the Node orb documentation here: https://circleci.com/developer/orbs/orb/circleci/node
  node: circleci/node@5.2
  aws-cli: circleci/aws-cli@4.1.3

jobs:
  check-branch:
    docker:
      - image: cimg/node:16.10
    steps:
      - checkout
      - run:
          name: Check branch
          command: |
            if [ "$CIRCLE_BRANCH" != "$PRODUCTION_BRANCH" ] && [ "$CIRCLE_BRANCH" != "$DEVELOP_BRANCH" ]; then
              echo "No need to deploy this branch"
              exit 1;
            fi
  # Below is the definition of your job to build and test your app, you can rename and customize it as you want.
  check-app-exists-and-deploy:
    executor: aws-cli/default
    # environment:
    #   - APP_NAME_BLUE: "app-blue"
    #   - APP_NAME_GREEN: "app-green"
    steps:
      - checkout
      - when:
          condition:
            equal: ["<< pipeline.git.branch >>", "develop"]
          steps:
            - run:
                name: Write commit ID to file
                command: |
                  echo -e "Branch: $CIRCLE_BRANCH\nCommit: $(date '+%Y-%m-%dT%H:%M:%SZ')-$CIRCLE_SHA1" > ./version.txt
                  echo "$(cat ./version.txt)"
            - aws-cli/setup:
                aws_access_key_id: $DEV_AWS_ACCESS_KEY_ID_GREEN
                aws_secret_access_key: $DEV_AWS_SECRET_ACCESS_KEY
                region: $DEV_AWS_REGION
      - when:
          condition:
            equal: ["<< pipeline.git.branch >>", "release/v2/golf"]
          steps:
            - aws-cli/setup:
                aws_access_key_id: $GOLF_AWS_ACCESS_KEY_ID
                aws_secret_access_key: $GOLF_AWS_SECRET_ACCESS_KEY
                region: $GOLF_AWS_REGION
      - when:
          condition:
            equal: ["<< pipeline.git.branch >>", "release/v2/pgm"]
          steps:
            - aws-cli/setup:
                aws_access_key_id: $PGM_AWS_ACCESS_KEY_ID
                aws_secret_access_key: $PGM_AWS_SECRET_ACCESS_KEY
                region: $PGM_AWS_REGION
      - when:
          condition:
            equal: ["<< pipeline.git.branch >>", "release/v2/accordia"]
          steps:
            - aws-cli/setup:
                aws_access_key_id: $ACCORDIA_AWS_ACCESS_KEY_ID
                aws_secret_access_key: $ACCORDIA_AWS_SECRET_ACCESS_KEY
                region: $ACCORDIA_AWS_REGION
      - run:
          name: "Check if app exists"
          no_output_timeout: 30m
          command: |
            prefix=
            if [ `echo $CIRCLE_BRANCH | cut -d '/' -f 1` == "release" ];then
              prefix=`echo $CIRCLE_BRANCH | cut -d '/' -f 3`
              prefix=`echo ${prefix^^}`_
            elif [[ $CIRCLE_BRANCH == "develop" || $CIRCLE_BRANCH == "circleci-bitbucket-dev" ]];then
              prefix=DEV_
            else
              echo "No need to deploy this branch"
              exit 0;
            fi
            for key in 'APP_NAME_BLUE' 'APP_NAME_GREEN' 'BITBUCKET_REPO_URL' 'BITBUCKET_TOKEN' 'PRODUCTION_BRANCH' 'AWS_ACCESS_KEY_ID' 'AWS_SECRET_ACCESS_KEY' 'AWS_REGION' 'API_BASE_URL';do
              value_name="${prefix}${key}"
              value=$(eval echo \$$value_name)
              if [ -z $value ];then
                echo "Missing environment variable: $value_name"
                exit 1;
              fi
              export $key=$value
            done

            apps=`aws amplify list-apps | jq '.apps[].name'`
            echo $apps
            if [[ `echo $apps|grep $APP_NAME_BLUE|wc -l` -eq 0 || `echo $apps|grep $APP_NAME_GREEN|wc -l` -eq 0 ]]; then
              stackName=create-caddy-app-prod-`date +"%Y%m%d%H%M%S"`
              echo "Creating stack: $stackName"
              aws cloudformation create-stack \
                --stack-name $stackName \
                --template-body file://$PWD/CloudFormation.yaml \
                --parameters ParameterKey=BitbucketRepo,ParameterValue=$BITBUCKET_REPO_URL ParameterKey=BitbucketToken,ParameterValue=$BITBUCKET_TOKEN ParameterKey=ProductionBranch,ParameterValue=$PRODUCTION_BRANCH ParameterKey=BlueAppName,ParameterValue=$APP_NAME_BLUE ParameterKey=GreenAppName,ParameterValue=$APP_NAME_GREEN ParameterKey=ApiBaseUrl,ParameterValue=$API_BASE_URL \
                --capabilities CAPABILITY_IAM CAPABILITY_NAMED_IAM
              stackResult=`aws cloudformation wait stack-create-complete --stack-name $stackName`
              echo $stackResult
              echo "Stack created successfully"
              APP_ID_BLUE=`aws amplify list-apps | jq '.apps[]|select(.name=="'$APP_NAME_BLUE'")|.appId'|tr -d '"'`
              jobResult=`aws amplify start-job --app-id $APP_ID_BLUE --branch-name $PRODUCTION_BRANCH --job-type RELEASE`
              echo $jobResult
              jobId=`echo $jobResult|jq '.jobSummary.jobId'|tr -d '"'`
              waitSeconds=1800
              while [[ $waitSeconds -gt 0 ]]; do
                jobStatus=`aws amplify get-job --app-id $APP_ID_BLUE --branch-name $PRODUCTION_BRANCH --job-id $jobId | jq '.job.summary.status'|tr -d '"'`
                if [[ $jobStatus == "SUCCEED" ]]; then
                  echo "Job completed successfully"
                  exit 0;
                fi
                sleep 10
                waitSeconds=$((waitSeconds-10))
              done
              echo "Job timeout!"
              exit 1;
            else
              echo "App already exists"
              APP_ID_BLUE=`aws amplify list-apps | jq '.apps[]|select(.name=="'$APP_NAME_BLUE'")|.appId'|tr -d '"'`
              BLUE_LAST_UPDATED=`aws amplify get-branch --app-id $APP_ID_BLUE --branch-name $PRODUCTION_BRANCH | jq '.branch.updateTime'|tr -d '"'`
              BLUE_LAST_UPDATED=`date -d $BLUE_LAST_UPDATED +%s`
              APP_ID_GREEN=`aws amplify list-apps | jq '.apps[]|select(.name=="'$APP_NAME_GREEN'")|.appId'|tr -d '"'`
              GREEN_LAST_UPDATED=`aws amplify get-branch --app-id $APP_ID_GREEN --branch-name $PRODUCTION_BRANCH | jq '.branch.updateTime'|tr -d '"'`
              GREEN_LAST_UPDATED=`date -d $GREEN_LAST_UPDATED +%s`
              echo $BLUE_LAST_UPDATED
              echo $GREEN_LAST_UPDATED
              APP_ID=
              if [[ $BLUE_LAST_UPDATED -gt $GREEN_LAST_UPDATED ]]; then
                APP_ID=$APP_ID_GREEN
              else
                APP_ID=$APP_ID_BLUE
              fi
              jobResult=`aws amplify start-job --app-id $APP_ID --branch-name $PRODUCTION_BRANCH --job-type RELEASE`
              echo $jobResult
              jobId=`echo $jobResult|jq '.jobSummary.jobId'|tr -d '"'`
              waitSeconds=1800
              while [[ $waitSeconds -gt 0 ]]; do
                jobStatus=`aws amplify get-job --app-id $APP_ID --branch-name $PRODUCTION_BRANCH --job-id $jobId | jq '.job.summary.status'|tr -d '"'`
                if [[ $jobStatus == "SUCCEED" ]]; then
                  echo "Job completed successfully"
                  exit 0;
                fi
                sleep 10
                waitSeconds=$((waitSeconds-10))
              done
              exit 0;
            fi

  blue-green-switch:
    executor: aws-cli/default
    steps:
      - checkout
      - aws-cli/setup:
          aws_access_key_id: $AWS_ACCESS_KEY_ID
          aws_secret_access_key: $AWS_SECRET_ACCESS_KEY
          region: $AWS_REGION
      - run:
          name: "Switch blue and green"
          command: |
            prefix=
            if [ `echo $CIRCLE_BRANCH | cut -d '/' -f 1` == "release" ];then
              prefix=`echo $CIRCLE_BRANCH | cut -d '/' -f 3`
              prefix=`echo ${prefix^^}`_
            elif [[ $CIRCLE_BRANCH == "develop" || $CIRCLE_BRANCH == "circleci-bitbucket-dev" ]];then
              prefix=DEV_
            else
              echo "No need to deploy this branch"
              exit 0;
            fi
            for key in 'APP_NAME_BLUE' 'APP_NAME_GREEN' 'BITBUCKET_REPO_URL' 'BITBUCKET_TOKEN' 'PRODUCTION_BRANCH' 'AWS_ACCESS_KEY_ID' 'AWS_SECRET_ACCESS_KEY' 'AWS_REGION' 'API_BASE_URL';do
              value_name="${prefix}${key}"
              value=$(eval echo \$$value_name)
              if [ -z $value ];then
                echo "Missing environment variable: $value_name"
                exit 1;
              fi
              export $key=$value
            done

            APP_ID_BLUE=`aws amplify list-apps | jq '.apps[]|select(.name=="'$APP_NAME_BLUE'")|.appId'|tr -d '"'`
            APP_ID_GREEN=`aws amplify list-apps | jq '.apps[]|select(.name=="'$APP_NAME_GREEN'")|.appId'|tr -d '"'`
            echo "APP_ID_BLUE: $APP_ID_BLUE"
            echo "APP_ID_GREEN: $APP_ID_GREEN"
            BLUE_LAST_UPDATED=`aws amplify get-branch --app-id $APP_ID_BLUE --branch-name $PRODUCTION_BRANCH | jq '.branch.updateTime'|tr -d '"'`
            BLUE_LAST_UPDATED=`date -d $BLUE_LAST_UPDATED +%s`
            GREEN_LAST_UPDATED=`aws amplify get-branch --app-id $APP_ID_GREEN --branch-name $PRODUCTION_BRANCH | jq '.branch.updateTime'|tr -d '"'`
            GREEN_LAST_UPDATED=`date -d $GREEN_LAST_UPDATED +%s`
            blueDomain=`aws amplify get-app --app-id $APP_ID_BLUE | jq '.app.defaultDomain'|tr -d '"'`
            greenDomain=`aws amplify get-app --app-id $APP_ID_GREEN | jq '.app.defaultDomain'|tr -d '"'`
            echo "blueDomain: $blueDomain"
            echo "greenDomain: $greenDomain"
            PRODUCTION_BRANCH_NAME=`echo $PRODUCTION_BRANCH|tr '/' '-'`
            blueDomainWithBranch=$PRODUCTION_BRANCH_NAME.$blueDomain
            greenDomainWithBranch=$PRODUCTION_BRANCH_NAME.$greenDomain
            if [[ $BLUE_LAST_UPDATED -gt $GREEN_LAST_UPDATED ]]; then
              distributionIds=`aws cloudfront list-distributions | jq ".DistributionList.Items[]|select(.Origins.Items[].DomainName==\"$greenDomain\")|.Id"|tr -d '"'`
              echo "Distribution IDs: $distributionIds"
              if [[ -z $distributionIds ]]; then
                distributionIds=`aws cloudfront list-distributions | jq ".DistributionList.Items[]|select(.Origins.Items[].DomainName==\"$greenDomainWithBranch\")|.Id"|tr -d '"'`
              fi
              if [[ -z $distributionIds ]]; then
                echo "Distribution no need to update."
                exit 0;
              else
                for distributionId in $distributionIds; do
                  aws cloudfront get-distribution --id $distributionId | jq '.Distribution.DistributionConfig' > /tmp/distribution.json
                  etag=`aws cloudfront get-distribution --id $distributionId | jq '.ETag'|tr -d '"'`
                  echo "Distribution ID: $distributionId"
                  echo "ETag: $etag"
                  cat /tmp/distribution.json
                  sed -i "s/$greenDomain/$blueDomain/g" /tmp/distribution.json
                  sed -i "s/$greenDomainWithBranch/$blueDomainWithBranch/g" /tmp/distribution.json
                  result=`aws cloudfront update-distribution --id $distributionId --distribution-config file:///tmp/distribution.json --if-match $etag`
                  echo $result
                done
              fi
            else
              distributionIds=`aws cloudfront list-distributions | jq ".DistributionList.Items[]|select(.Origins.Items[].DomainName==\"$blueDomain\")|.Id"|tr -d '"'`
              if [[ -z $distributionIds ]]; then
                distributionIds=`aws cloudfront list-distributions | jq ".DistributionList.Items[]|select(.Origins.Items[].DomainName==\"$blueDomainWithBranch\")|.Id"|tr -d '"'`
                echo "Distribution IDs: $distributionIds"
              fi
              if [[ -z $distributionIds ]]; then
                echo "Distribution no need to update."
                exit 0;
              else
                for distributionId in $distributionIds; do
                  aws cloudfront get-distribution --id $distributionId | jq '.Distribution.DistributionConfig' > /tmp/distribution.json
                  etag=`aws cloudfront get-distribution --id $distributionId | jq '.ETag'|tr -d '"'`
                  echo "Distribution ID: $distributionId"
                  echo "ETag: $etag"
                  cat /tmp/distribution.json
                  sed -i "s/$blueDomain/$greenDomain/g" /tmp/distribution.json
                  sed -i "s/$blueDomainWithBranch/$greenDomainWithBranch/g" /tmp/distribution.json
                  result=`aws cloudfront update-distribution --id $distributionId --distribution-config file:///tmp/distribution.json --if-match $etag`
                  echo $result
                done
              fi
            fi
            result=`aws cloudfront create-invalidation --distribution-id $distributionId --paths "/*"`
            echo $result
            exit 0;

  build-and-test:
    # Specify the execution environment. You can specify an image from Docker Hub or use one of our convenience images from CircleCI's Developer Hub.
    # See: https://circleci.com/docs/executor-intro/ & https://circleci.com/docs/configuration-reference/#executor-job
    docker:
      # Specify the version you desire here
      # See: https://circleci.com/developer/images/image/cimg/node
      - image: cimg/node:16.10

    # Add steps to the job
    # See: https://circleci.com/docs/jobs-steps/#steps-overview & https://circleci.com/docs/configuration-reference/#steps
    steps:
      # Checkout the code as the first step.
      - checkout

      # The node orb's install-packages step will install the dependencies from a package.json.
      # The orb install-packages step will also automatically cache them for faster future runs.
      - node/install-packages:
          # If you are using yarn, change the line below from "npm" to "yarn"
          pkg-manager: npm
      - run:
          name: Run tests
          command: npm run test

# Orchestrate jobs using workflows
# See: https://circleci.com/docs/workflows/ & https://circleci.com/docs/configuration-reference/#workflows
workflows:
  develop: # This is the name of the workflow, feel free to change it to better match your workflow.
    # Inside the workflow, you define the jobs you want to run.
    jobs:
      - check-app-exists-and-deploy:
          filters:
            branches:
              only:
                - develop
                - circleci-bitbucket-dev
      - blue-green-switch:
          filters:
            branches:
              only:
                - develop
                - circleci-bitbucket-dev
          requires:
            - check-app-exists-and-deploy

  production:
    jobs:
      - check-app-exists-and-deploy:
          filters:
            branches:
              only:
                - /release\/.*/
      # This is a special job type in CircleCI that pauses the workflow to wait for a human to approve it.
      - start-blue-green-switch:
          type: approval
          requires:
            - check-app-exists-and-deploy
          filters:
            branches:
              ignore:
                - develop
      - blue-green-switch:
          filters:
            branches:
              only:
                - /release\/.*/
          requires:
            - start-blue-green-switch
