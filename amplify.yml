version: 1
# backend:
#   phases:
#     build:
#       commands:
#         - npm ci --cache .npm --prefer-offline
#         # - node modify-webpack.js
#         - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
#         - npx ampx generate outputs --app-id $AWS_APP_ID --branch $AWS_BRANCH --out-dir src
frontend:
  phases:
    preBuild:
      commands:
        - rm -rf out
        - npm ci
    build:
      commands:
        - echo "AWS_BRANCH=$AWS_BRANCH" >> .env
        - echo "AWS_APP_ID=$AWS_APP_ID" >> .env
        - cat .env
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - "**/*"
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
      - .npm/**/*
      - node_modules/**/*
