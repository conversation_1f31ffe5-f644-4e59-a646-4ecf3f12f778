import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'
 
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
  },
  test: {
    environment: 'jsdom',
    setupFiles: "./setup.ts",
    reporters: ['junit', 'json', 'verbose'],
    outputFile: {
      junit: './reports/junit-report.xml',
      json: './reports/json-report.json',
    },
    coverage: {
      provider: 'v8',
      reporter: ['html'],
      reportsDirectory: './tests/coverage',
    },
  },
})