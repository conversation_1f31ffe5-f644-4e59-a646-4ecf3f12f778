// modify-webpack.js
const fs = require('fs');
const path = require('path');

const nextConfigPath = path.resolve(__dirname, 'next.config.js');

// read next.config.js
let configContent = fs.readFileSync(nextConfigPath, 'utf-8');

// ignore axe-core
const modifyConfig = `
module.exports = {
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push({
        'axe-core': 'commonjs axe-core',
      });
    }
    return config;
  },
};
`;

// write config
fs.writeFileSync(nextConfigPath, modifyConfig);
console.log('Webpack configuration modified to exclude axe-core on the server side.');
