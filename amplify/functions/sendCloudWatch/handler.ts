import type { Handler } from 'aws-lambda';
// import { CloudWatchLogs } from 'aws-sdk';
// import { secret } from '@aws-amplify/backend';

// const cloudwatchlogs = new CloudWatchLogs();

export const handler: Handler = async (event) => {
    var { content } = event.arguments;
    // logGroupName = logGroupName?? (secret as any)['APP_ID'];
    // const logParams : CloudWatchLogs.Types.PutLogEventsRequest = {
    //     logGroupName: '/aws/amplify/' + logGroupName,
    //     logStreamName: logStreamName,
    //     logEvents: [
    //         {
    //             timestamp: new Date().getTime(), // 日志时间戳
    //             message: content // 日志内容
    //         }
    //     ]
    // };

    // try {
    //     await cloudwatchlogs.putLogEvents(logParams).promise();
    //     return {
    //         statusCode: 200,
    //         body: JSON.stringify('Log successfully sent to CloudWatch Logs!'),
    //     };
    // } catch (error) {
    //     console.error('Error sending log to CloudWatch:', error);
    //     return {
    //         statusCode: 500,
    //         body: JSON.stringify('Error sending log to CloudWatch Logs'),
    //     };
    // }
    console.log(content);
};