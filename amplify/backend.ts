import { defineBackend } from '@aws-amplify/backend';
import { sendSESEmailFunc } from './functions/sendEmail/resource';
import { sendCloudWatch } from './functions/sendCloudWatch/resource';
import { auth } from './auth/resource';
import { data } from './data/resource';
import { aws_events } from "aws-cdk-lib";
import {
	Policy,
	PolicyDocument,
	PolicyStatement,
	Role,
	ServicePrincipal,
} from 'aws-cdk-lib/aws-iam';
import { Stack } from 'aws-cdk-lib';
import { CfnApp } from "aws-cdk-lib/aws-pinpoint";

export const backend = defineBackend({
  auth,
  data,
  sendSESEmailFunc,
	sendCloudWatch
});
// pinpoint
const analyticsStack = backend.createStack("analytics-stack");
const pinpoint = new CfnApp(analyticsStack, "Pinpoint", {
  name: "amplify-pinpoint",
});
const pinpointPolicy = new Policy(analyticsStack, "PinpointPolicy", {
  policyName: "PinpointPolicy",
  statements: [
    new PolicyStatement({
      actions: ["mobiletargeting:UpdateEndpoint", "mobiletargeting:PutEvents"],
      resources: [pinpoint.attrArn + "/*"],
    }),
  ],
});
backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(pinpointPolicy);
backend.auth.resources.unauthenticatedUserIamRole.attachInlinePolicy(pinpointPolicy);
backend.addOutput({
  analytics: {
    amazon_pinpoint: {
      app_id: pinpoint.ref,
      aws_region: Stack.of(pinpoint).region,
    }
  },
});

// Create a new stack for the EventBridge data source
const eventStack = backend.createStack("MyExternalDataSources");

// Reference or create an EventBridge EventBus
const eventBus = aws_events.EventBus.fromEventBusName(
  eventStack,
  "MyEventBus",
  "default"
);

backend.sendSESEmailFunc.resources.lambda.addToRolePolicy(
	new PolicyStatement({
		actions: ['ses:SendEmail', 'ses:SendRawEmail'],
		resources: ['*'],
	})
)

const ebSchedulerDS = backend.data.addHttpDataSource(
	// name of the data source that needs to be passed to my amplify/data/resource.ts file
	'ebSchedulerDS',
	'https://scheduler.ap-northeast-1.amazonaws.com',
	{
		authorizationConfig: {
			signingRegion: 'ap-northeast-1',
			signingServiceName: 'scheduler',
		},
	}
)

ebSchedulerDS.grantPrincipal.addToPrincipalPolicy(
	new PolicyStatement({
		actions: ['scheduler:CreateSchedule'],
		resources: ['*'],
	})
)
const schedulerRole = new Role(
	Stack.of(backend.data),
	'createMessageSchedulerRole',
	{
		assumedBy: new ServicePrincipal('scheduler.amazonaws.com'),
		inlinePolicies: {
			invokeFunction: new PolicyDocument({
				statements: [
					new PolicyStatement({
						actions: ['lambda:InvokeFunction'],
						resources: [backend.sendSESEmailFunc.resources.lambda.functionArn],
					}),
				],
			}),
		},
	}
)
// pass the role from the ebScheduler to the schedulerRole
ebSchedulerDS.grantPrincipal.addToPrincipalPolicy(
	new PolicyStatement({
		actions: ['iam:PassRole'],
		resources: [schedulerRole.roleArn],
	})
)

//set the role and the function's arn to env vars
backend.data.resources.cfnResources.cfnGraphqlApi.environmentVariables = {
	SCHEDULE_FUNCTION_ROLE_ARN: schedulerRole.roleArn,
	SCHEDULE_FUNCTION_ARN: backend.sendSESEmailFunc.resources.lambda.functionArn,
}
