{"name": "app", "version": "1.1.0", "versionCode": 3, "private": true, "scripts": {"dev": "next dev --port 8081", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "coverage": "vitest run --coverage", "common": "cross-env NODE_ENV=common next dev", "development": "cross-env NODE_ENV=development next dev", "accordia": "cross-env NODE_ENV=accordia next dev", "pgm": "cross-env NODE_ENV=pgm next dev"}, "dependencies": {"@aws-amplify/backend": "^1.1.1", "@aws-amplify/backend-cli": "^1.2.4", "@aws-sdk/client-eventbridge": "^3.635.0", "@aws-sdk/client-sesv2": "^3.635.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.17.1", "@mui/material": "~5.15.0", "@mui/x-date-pickers": "^7.7.1", "@types/aws-lambda": "^8.10.145", "ampx": "^0.2.1", "aws-amplify": "^6.6.0", "aws-cdk": "^2.154.0", "aws-cdk-lib": "^2.154.0", "axios": "^1.3.1", "backend-cli": "^1.0.1", "dayjs": "^1.11.11", "framer-motion": "^12.12.1", "less": "~4.2.0", "less-loader": "~11.0.0", "next": "~14.2.0", "next-with-less": "^3.0.1", "react": "~18.3.0", "react-beautiful-dnd": "^13.1.1", "react-confirm-alert": "^3.0.6", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "~18.3.0", "react-transition-group": "^4.4.5", "recharts": "^2.12.7", "tailwindcss": "~3.4.0"}, "devDependencies": {"@babel/core": "^7.24.7", "@testing-library/react": "^16.0.0", "@types/node": "^20", "@types/react": "~18.3.0", "@types/react-dom": "~18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.19", "babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.4", "jsdom": "^24.1.1", "lefthook": "^1.12.2", "msw": "^2.3.5", "postcss": "^8.4.38", "tailwindcss": "~3.4.0", "typescript": "~5.4.0", "vitest": "^3.2.4"}}