AWSTemplateFormatVersion: '2010-09-09'
Parameters:
  BitbucketToken:
    Type: String
    Description: GitHub Personal Access Token for accessing the repository
  BitbucketRepo:
    Type: String
    Description: GitHub repository URL
  ProductionBranch:
    Type: String
    Description: The production branch for the Amplify app (e.g., master)
  BlueAppName:
    Type: String
    Description: The name of the blue Amplify app
  GreenAppName:
    Type: String
    Description: The name of the green Amplify app
  ApiBaseUrl:
    Type: String
    Description: The base URL for the API

Resources:
  AmplifyServiceRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: amplify.amazonaws.com
            Action: sts:AssumeRole
      Path: "/"
      Policies:
        - PolicyName: AmplifyCodeCommitPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - codecommit:*
                Resource: "*"
              - Effect: Allow
                Action:
                  - amplify:*
                Resource: "*"
              - Effect: Allow
                Action:
                  - 'cloudformation:*'
                Resource:
                  - 'arn:aws:cloudformation:*:*:stack/amplify-*'
              - Effect: Allow
                Action:
                  - 'iam:*'
                  - 'appsync:*'
                  - 'apigateway:*'
                  - 'cognito-idp:*'
                  - 'cognito-identity:*'
                  - 'lambda:*'
                  - 'dynamodb:*'
                  - 's3:*'
                  - 'cloudfront:*'
                  - 'events:*'
                  - 'mobiletargeting:GetApp'
                  - 'kinesis:*'
                  - 'es:*'
                Resource: "*"
                Condition:
                  'ForAnyValue:StringEquals':
                    'aws:CalledVia':
                      - cloudformation.amazonaws.com
              - Effect: Allow
                Action:
                  - 'appsync:*'
                  - 'amplify:*'
                  - 'amplifybackend:*'
                  - 'amplifyuibuilder:*'
                  - 'sts:AssumeRole'
                  - 'mobiletargeting:*'
                  - 'cognito-idp:*'
                  - 'dynamodb:*'
                  - 'lambda:*'
                  - 'iam:*'
                  - 'sns:*'
                  - 'rekognition:DescribeCollection'
                  - 'logs:DescribeLogStreams'
                  - 'logs:GetLogEvents'
                  - 'lex:GetBot'
                  - 'lex:GetBuiltinIntent'
                  - 'lex:GetBuiltinIntents'
                  - 'lex:GetBuiltinSlotTypes'
                  - 'cloudfront:GetCloudFrontOriginAccessIdentity'
                  - 'cloudfront:GetCloudFrontOriginAccessIdentityConfig'
                  - 'polly:DescribeVoices'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ssm:*'
                  - 'ec2messages:*'
                  - 'cloudwatch:PutMetricData'
                  - 'ds:CreateComputer'
                  - 'ds:DescribeDirectories'
                  - 'ssmmessages:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'geo:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ecr:DescribeRepositories'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 's3:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'cloudfront:*'
                  - 'route53:ChangeResourceRecordSets'
                  - 'route53:ListHostedZonesByName'
                  - 'route53:ListResourceRecordSets'
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:GetQueueAttributes'
                  - 'sqs:SetQueueAttributes'
                Resource: '*'
              - Effect: Allow
                Action:
                  'logs:DescribeLogGroups'
                Resource: 'arn:aws:logs:*:*:log-group:*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogStream'
                  - 'logs:PutLogEvents'
                Resource: 'arn:aws:logs:*:*:log-group:/aws/amplify/*:log-stream:*'
        - PolicyName: AmplifyCodeBuildPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: CDKPreDeploy
                Effect: Allow
                Action:
                  - 'cloudformation:*'
                Resource:
                  - 'arn:aws:cloudformation:*:*:stack/amplify-*'
                  - 'arn:aws:cloudformation:*:*:stack/CDKToolkit/*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: AmplifyMetadata
                Effect: Allow
                Action:
                  - 'amplify:ListApps'
                  - 'cloudformation:ListStacks'
                  - 'ssm:DescribeParameters'
                  - 'appsync:GetIntrospectionSchema'
                  - 'amplify:GetBackendEnvironment'
                Resource:
                  - '*'
              - Sid: AmplifyHotSwappableResources
                Effect: Allow
                Action:
                  - 'appsync:GetSchemaCreationStatus'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:UpdateResolver'
                  - 'appsync:ListFunctions'
                  - 'appsync:UpdateFunction'
                  - 'appsync:UpdateApiKey'
                Resource:
                  - '*'
              - Sid: AmplifyHotSwappableFunctionResource
                Effect: Allow
                Action:
                  - 'lambda:InvokeFunction'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:GetFunction'
                  - 'lambda:UpdateFunctionConfiguration'
                Resource:
                  - 'arn:aws:lambda:*:*:function:amplify-*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: AmplifySchema
                Effect: Allow
                Action:
                  - 's3:GetObject'
                Resource:
                  - 'arn:aws:s3:::*amplify*'
                  - 'arn:aws:s3:::cdk-*-assets-*-*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: CDKDeploy
                Effect: Allow
                Action:
                  - 'sts:AssumeRole'
                Resource:
                  - 'arn:aws:iam::*:role/cdk-*-deploy-role-*-*'
                  - 'arn:aws:iam::*:role/cdk-*-file-publishing-role-*-*'
                  - 'arn:aws:iam::*:role/cdk-*-image-publishing-role-*-*'
                  - 'arn:aws:iam::*:role/cdk-*-lookup-role-*-*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: AmplifySSM
                Effect: Allow
                Action:
                  - 'ssm:GetParametersByPath'
                  - 'ssm:GetParameters'
                  - 'ssm:GetParameter'
                Resource:
                  - 'arn:aws:ssm:*:*:parameter/amplify/*'
                  - 'arn:aws:ssm:*:*:parameter/cdk-bootstrap/*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: AmplifyModifySSMParam
                Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:DeleteParameters'
                Resource: 'arn:aws:ssm:*:*:parameter/amplify/*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
              - Sid: AmplifyDiscoverRDSVpcConfig
                Effect: Allow
                Action:
                  - 'rds:DescribeDBProxies'
                  - 'rds:DescribeDBInstances'
                  - 'rds:DescribeDBClusters'
                  - 'ec2:DescribeSubnets'
                  - 'rds:DescribeDBSubnetGroups'
                Resource:
                  - 'arn:aws:rds:*:*:db:*'
                  - 'arn:aws:rds:*:*:cluster:*'
                  - 'arn:aws:rds:*:*:db-proxy:*'
                  - 'arn:aws:rds:*:*:subgrp:*'
                  - 'arn:aws:ec2:*:*:subnet/*'
                Condition:
                  StringEquals:
                    'aws:ResourceAccount': '${aws:PrincipalAccount}'
  BlueAmplifyApp:
    Type: AWS::Amplify::App
    Properties:
      Name: !Ref BlueAppName
      Repository: !Ref BitbucketRepo
      OauthToken: !Ref BitbucketToken
      IAMServiceRole: !GetAtt AmplifyServiceRole.Arn
      Platform: WEB_COMPUTE
      BuildSpec: |
        version: 1
        frontend:
            phases:
                preBuild:
                  commands:
                    - 'rm -rf out'
                    - 'npm install'
                build:
                    commands:
                        - 'npm run build'
            artifacts:
                baseDirectory: .next
                files:
                    - '**/*'
            cache:
                paths: []

  GreenAmplifyApp:
    Type: AWS::Amplify::App
    Properties:
      Name: !Ref GreenAppName
      Repository: !Ref BitbucketRepo
      OauthToken: !Ref BitbucketToken
      IAMServiceRole: !GetAtt AmplifyServiceRole.Arn
      Platform: WEB_COMPUTE
      BuildSpec: |
        version: 1
        frontend:
            phases:
                preBuild:
                  commands:
                    - 'rm -rf out'
                    - 'npm install'
                build:
                    commands:
                        - 'npm run build'
            artifacts:
                baseDirectory: .next
                files:
                    - '**/*'
            cache:
                paths: []

  BlueAmplifyBranch:
    Type: AWS::Amplify::Branch
    DependsOn: BlueAmplifyApp
    Properties:
      AppId: !GetAtt BlueAmplifyApp.AppId
      BranchName: !Ref ProductionBranch
      EnableAutoBuild: false
      Stage: PRODUCTION
      Framework: "Next.js - SSR"
      EnvironmentVariables:
        - Name: BlueAmplifyAppId
          Value: !GetAtt BlueAmplifyApp.AppId
        - Name: GreenAmplifyAppId
          Value: !GetAtt GreenAmplifyApp.AppId
        - Name: ProductionBranch
          Value: !Ref ProductionBranch
        - Name: BASE_URL
          Value: !Ref ApiBaseUrl

  GreenAmplifyBranch:
    Type: AWS::Amplify::Branch
    DependsOn: GreenAmplifyApp
    Properties:
      AppId: !GetAtt GreenAmplifyApp.AppId
      BranchName: !Ref ProductionBranch
      EnableAutoBuild: false
      Stage: PRODUCTION
      Framework: "Next.js - SSR"
      EnvironmentVariables:
        - Name: BlueAmplifyAppId
          Value: !GetAtt BlueAmplifyApp.AppId
        - Name: GreenAmplifyAppId
          Value: !GetAtt GreenAmplifyApp.AppId
        - Name: ProductionBranch
          Value: !Ref ProductionBranch
        - Name: BASE_URL
          Value: !Ref ApiBaseUrl

  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Origins:
          - Id: myOrigin
            DomainName: !Join
              - ''
              - - !Join
                  - '-'
                  - !Split [ '/', !Ref ProductionBranch]
                - "."
                - !GetAtt BlueAmplifyApp.DefaultDomain
            CustomOriginConfig:
              HTTPPort: 80
              HTTPSPort: 443
              OriginProtocolPolicy: http-only
        DefaultCacheBehavior:
          TargetOriginId: myOrigin
          ViewerProtocolPolicy: redirect-to-https
          AllowedMethods:
            - GET
            - HEAD
            - OPTIONS
            - PUT
            - POST
            - PATCH
            - DELETE
          CachedMethods:
            - GET
            - HEAD
          Compress: true
          ForwardedValues:
            QueryString: true
            Cookies:
              Forward: all
            Headers:
              - Authorization
              - Content-Type
              - Accept
        Enabled: true
        ViewerCertificate:
          CloudFrontDefaultCertificate: true

