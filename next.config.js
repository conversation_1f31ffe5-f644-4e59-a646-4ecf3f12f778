const fs = require('fs');
const path = require('path');
const withLess = require("next-with-less");

// read version.txt
const readVersionFile = () => {
    try {
        const filePath = path.resolve(__dirname, 'version.txt');
        const versionContent = fs.readFileSync(filePath, 'utf8');
        return versionContent;
    } catch (err) {
        console.error('Error reading version.txt:', err);
        return 'Unknown';
    }
};

/** @type {import('next').NextConfig} */
module.exports = withLess({
    basePath: '/webapp',
    assetPrefix: '/webapp',
    lessLoaderOptions: {
        lessOptions: {
            javascriptEnabled: true,
        },
    },
    reactStrictMode: false,
    output: "export",
    env: {
        BASE_URL: process.env.BASE_URL || '',
        VERSION: readVersionFile(),
    },
    webpack: (config, { isServer }) => {
        if (isServer) {
            config.externals.push({
                'axe-core': 'commonjs axe-core', // ignore handlebars on server
            });
        }
        return config;
    },
    webpack: (config, { isServer }) => {
        if (isServer) {
            config.externals.push({
                'axe-core': 'commonjs axe-core', // ignore handlebars on server
            });
        }
        return config;
    },
});
