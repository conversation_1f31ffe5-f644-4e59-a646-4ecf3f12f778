default:
  image: node:20 # recommended: specify node version

stages:
  - install
  - test
  - build
  - deploy
  - canary

variables:
  PIPELINE_NAME: "app pipeline"

install:
  stage: install
  cache:
    policy: push
    paths:
      - "node_modules/"
      - ".next/"
  script:
    - echo "setup app"
    - npm install

run_test:
  stage: test
  needs: 
    - install   
  cache:
    policy: pull
    paths:
      - "node_modules/"
      - ".next/" 
  script:
    - echo "test"
    - npm run test
    - npm run coverage
  # artifacts:
  #   paths:
  #     - "reports/"
  #     - "coverage/"

build_app:
  stage: build
  needs: 
    - run_test
  cache:
    policy: pull
    paths:
      - "node_modules/"
      - ".next/"
  script:
    - echo "build"
    - npm run build
  # artifacts:
  #   paths:
  #     - "out/"

deploy_to_s3:
  stage: deploy
  when: manual
  needs: 
    - run_test
  script:
    - echo "deploy to s3"

set_canary:
  stage: canary
  when: manual
  needs: 
    - deploy_to_s3
  script:
    - echo "get current router"
    - echo "set canary"
