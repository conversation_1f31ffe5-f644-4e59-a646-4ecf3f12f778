{"auth": {"user_pool_id": "ap-northeast-1_xxxxxxxxx", "aws_region": "ap-northeast-1", "user_pool_client_id": "xxxxxxxxxxxxxxxxxxxxxxxxxx", "xdentity_pool_id": "ap-northeast-1:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "mfa_methods": [], "standard_required_attributes": ["email"], "username_attributes": ["email"], "user_verification_types": ["email"], "mfa_configuration": "NONE", "password_policy": {"min_length": 8, "require_lowercase": true, "require_numbers": true, "require_symbols": true, "require_uppercase": true}, "unauthenticated_identities_enabled": true}, "data": {"url": "https://xxxxxxxxxxxxxxxxxxxxxxxxxx.appsync-api.ap-northeast-1.amazonaws.com/graphql", "aws_region": "ap-northeast-1", "api_key": "da2-xxxxxxxxxxxxxxxxxxxxxxxxxx", "default_authorization_type": "API_KEY", "authorization_types": ["AMAZON_COGNITO_USER_POOLS", "AWS_IAM"], "model_introspection": {"version": 1, "models": {"Message": {"name": "Message", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "title": {"name": "title", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "deliveryDate": {"name": "deliveryDate", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "timezone": {"name": "timezone", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Messages", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {}, "nonModels": {"CreateMessageScheduleReturnType": {"name": "CreateMessageScheduleReturnType", "fields": {"message": {"name": "message", "isArray": false, "type": "String", "isRequired": true, "attributes": []}}}}, "mutations": {"createMessageSchedule": {"name": "createMessageSchedule", "isArray": false, "type": {"nonModel": "CreateMessageScheduleReturnType"}, "isRequired": false, "arguments": {"deliveryDate": {"name": "deliveryDate", "isArray": false, "type": "String", "isRequired": true}, "timezone": {"name": "timezone", "isArray": false, "type": "String", "isRequired": true}, "title": {"name": "title", "isArray": false, "type": "String", "isRequired": true}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": true}}}}}}, "version": "1.1"}